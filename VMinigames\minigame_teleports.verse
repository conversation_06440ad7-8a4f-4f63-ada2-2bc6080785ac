using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 


minigame_teleports := class(auto_creative_device, i_init):

	@editable SquidTeleporter:?teleporter_device = false
	@editable RaceChannel:?channel_device = false
	@editable ParkourTeleporter:?teleporter_device = false

	Init<override>(Container:vcontainer):void=
		for(Channel:tag_minigame_channel_squid{}.GetAll(Self)):
			Channel.ReceivedTransmitEvent.Subscribe1(OnSquidChannel)
		for(Channel:tag_minigame_channel_race{}.GetAll(Self)):
			Channel.ReceivedTransmitEvent.Subscribe1(OnRaceChannel)
		for(Channel:tag_minigame_channel_parkour{}.GetAll(Self)):
			Channel.ReceivedTransmitEvent.Subscribe1(OnParkourChannel)


	OnSquidChannel(MAgent:?agent):void=
		if(Agent := MAgent?):
			SquidTeleporter.G().Teleport(Agent)

	OnRaceChannel(MAgent:?agent):void=
		if(Agent := MAgent?):
			RaceChannel.G().Transmit(MAgent)

	OnParkourChannel(MAgent:?agent):void=
		if(Agent := MAgent?):
			ParkourTeleporter.G().Teleport(Agent)
		
	
	