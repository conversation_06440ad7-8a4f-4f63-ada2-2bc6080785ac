using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
using. VPropSpawner

respawn_existing_props := class(auto_creative_device, i_init_async):
    @editable PropsToRespawn:[]prop_w_asset = array{}
    @editable RespawnSettings:respawn_existing_props_settings = respawn_existing_props_settings{}

    var PropSpawner:?prop_spawner = false

    InitAsync<override>(Container:vcontainer)<suspends>:void=
        set PropSpawner = Container.ResolveOp[prop_spawner] or Err()
        for(PropWAsset:PropsToRespawn):
            set PropWAsset.InitTr = PropWAsset.Prop.GetTransform()

        loop:
            if(TimeRespawn := respawn_existing_props_settings_time[RespawnSettings]
            ):
                Sleep(TimeRespawn.CheckDelayInSec)
            else if(ChannelRespawn := respawn_existing_props_settings_channel[RespawnSettings]
            ):
                ChannelRespawn.RespawnChannelOnReceived.ReceivedTransmitEvent.Await()

            for(PropWAsset:PropsToRespawn):
                if(not PropWAsset.Prop.IsValid[]):
                    if(Spawned := PropWAsset.MSpawned?):
                        Spawned.Dispose()
                    MPropUnique := PropSpawner.G().Spawn(PropWAsset.Asset, PropWAsset.InitTr)
                    if(PropUnique := MPropUnique?):
                        set PropWAsset.MSpawned = MPropUnique
                        set PropWAsset.Prop = PropUnique.Prop
                    else:
                        LError()


prop_w_asset := class<concrete>:
    @editable var Prop:creative_prop = creative_prop{}
    var InitTr:transform = transform{}
    var MSpawned:?creative_prop_unique = false
    @editable Asset:creative_prop_asset = DefaultCreativePropAsset


respawn_type := enum:
    Time
    Channel

respawn_existing_props_settings_channel := class<concrete>(respawn_existing_props_settings):
    @editable RespawnChannelOnReceived:channel_device = channel_device{}

respawn_existing_props_settings_time := class<concrete>(respawn_existing_props_settings):
    @editable CheckDelayInSec:float = 20.0

respawn_existing_props_settings := class<concrete>():
    