using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}

# observable_value_float_arr<public> := class(){
# 	ChangedEvent<public>:event([]float) = event([]float){}
#     var Value:[]float = array{}

# 	SetValue<public>(PValue:[]float):void={
# 		set Value = PValue
# 		ChangedEvent.Signal(Value)
# 	}
# 	GetValue<public>()<transacts>:[]float={
# 		Value
# 	}
# }

# observable_value_float<public> := class(){
# 	ChangedEvent<public>:event(float) = event(float){}
#     var Value:float = 0.0

# 	SetValue<public>(PValue:float):void={
# 		set Value = PValue
# 		ChangedEvent.Signal(Value)
# 	}
# 	GetValue<public>()<transacts>:float={
# 		Value
# 	}
# }

observable_value_int<public> := class:
	ChangedEvent<public>:event(int) = event(int){}
	var Value<public>:int = 0

	Set<public>(PValue:int):void=
		set Value = PValue
		ChangedEvent.Signal(PValue)

	Get<public>()<transacts>:int=
		Value

	Add<public>(ToAdd:int):void=
		Set(Value + ToAdd)

ToString<public>(Val:observable_value_int):string=
	"{Val.Get()}"

observable_value_float<public> := class{
	ChangedEvent<public>:event(float) = event(float){}
	var Value<public>:float

	Set<public>(PValue:float):void={
		set Value = PValue
		ChangedEvent.Signal(PValue)
	}

	Get<public>()<transacts>:float={
		Value
	}
}
observable_value_string<public> := class{
	ChangedEvent<public>:event(string) = event(string){}
	var Value<public>:string

	Set<public>(PValue:string):void={
		set Value = PValue
		ChangedEvent.Signal(PValue)
	}

	Get<public>()<transacts>:string={
		Value
	}
}
observable_value_logic<public> := class{
	ChangedEvent<public>:event(logic) = event(logic){}
	var Value<public>:logic

	Set<public>(PValue:logic):void={
		set Value = PValue
		ChangedEvent.Signal(PValue)
	}

	Get<public>()<transacts>:logic={
		Value
	}
}

#map agent to observable value
# #get autofill value
# map_agent_observable<public>(t:type) := class(map_agent(observable(t))){
	
# }

# observable_value_agents<public>(t:type) := class{
# 	Map<public>:map_agent_t(observable_value(t))

# 	# Set<public>(Agent:agent, PValue:t):observable_value_agents(t)={
# 	# 	ChangedEvent.Signal(PValue)
# 	# 	observable_value_agents(t){
# 	# 		Value := PValue
# 	# 		ChangedEvent := ChangedEvent
# 	# 	}
# 	# }

# 	# Get<public>(Agent:agent)<transacts>:observable_value(t)={
# 	# 	Value
# 	# }
# }
