using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/UI
using. /Fortnite.com/UI
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
ui_phone_reg2_generated := class:
	ButtonCard:button_regular
	StackBox_119:stack_box
	Overlay_50:overlay
	ui_phone_reg2:canvas

ButtonCardTextVar<localizes>:message = "Accept"
make_ui_phone_reg2_generated():ui_phone_reg2_generated=

	ButtonCard :button_regular= button_regular:
		DefaultText := ButtonCardTextVar
	StackBox_119 :stack_box= stack_box:
		Orientation := orientation.Vertical
		Slots := array:
			stack_box_slot:
				Padding := margin:
					Bottom := 3.000000
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := ButtonCard
	Overlay_50 :overlay= overlay:
		Slots := array:
			overlay_slot:
				Padding := margin:
					Bottom := 100.000000
				HorizontalAlignment := horizontal_alignment.Center
				VerticalAlignment := vertical_alignment.Bottom
				Widget := StackBox_119
	ui_phone_reg2 :canvas= canvas:
		Slots := array:
			canvas_slot:
				Alignment := vector2:
					X := 0.500000
					Y := 0.500000
				Offsets := margin:
					Right := 574.988892
					Bottom := 850.834900
				Anchors := anchors:
					Minimum := vector2:
						X := 0.500000
						Y := 0.500000
					Maximum := vector2:
						X := 0.500000
						Y := 0.500000
				SizeToContent := false
				Widget := Overlay_50


	ui_phone_reg2_generated:
		ButtonCard := ButtonCard
		StackBox_119 := StackBox_119
		Overlay_50 := Overlay_50
		ui_phone_reg2 := ui_phone_reg2
