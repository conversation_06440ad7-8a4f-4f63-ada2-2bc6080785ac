using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }

map_code<public> := class(i_init_per_player, i_removable_per_player){
    var PlayerDataMap : [agent]p_data = map{}

    Init<public>(Container:player_events_manager_devic):map_code={
        Container.Register(Self)
        Self
    }

    InitPlayer<override>(Agent:agent):void={
        if(Player := player[Agent]
            PlayerUI := GetPlayerUI[Player]
        ){
            NewPlayerData := p_data_c(PlayerUI)
            if. set PlayerDataMap[Agent] = NewPlayerData
        }
    }

    RemovePlayer<override>(Agent:agent):void={
        if(PlayerData := PlayerDataMap[Agent]){
            PlayerData.PlayerUI.RemoveWidget(PlayerData.Canvas)
        }
        set PlayerDataMap = PlayerDataMap.WithRemoved(Agent)
    }
}

p_data_c(PlayerUI:player_ui):p_data=

	MapCodeWidget := texture_block:
		DefaultImage := VMapCode.Textures.MapCodeTexture
		DefaultDesiredSize := Vector2(340.0, 170.0)

	LeftMapCodeWidget := texture_block:
		DefaultImage := VMapCode.Textures.MapCodeTextureLeft
		DefaultDesiredSize := Vector2(340.0, 170.0)

	Canvas := canvas_c(array:
		canvas_slot:
			Widget := MapCodeWidget
			Anchors := Anchors(0.5, 0.5, 0.005, 0.005)
			Alignment := Alignment(0.5, 0.0)
		canvas_slot:
			Widget := LeftMapCodeWidget
			Anchors := AnchorsXY(0.01, 0.01)
			Alignment := Alignment(0.0, 0.0)
	)
	PlayerUI.AddWidget(Canvas)

	p_data:
		PlayerUI := PlayerUI
		MapCodeWidget := MapCodeWidget
		Canvas := Canvas

		
p_data := class(){
    PlayerUI : player_ui
    Canvas : canvas
    MapCodeWidget : texture_block
}