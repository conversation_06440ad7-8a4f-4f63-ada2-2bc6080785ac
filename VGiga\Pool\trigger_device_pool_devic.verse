#gen
#pool_devic
#trigger_device
#id-a0dbdc97-48e6-485f-a070-3e50adead147
using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using.  VGiga 

trigger_device_pool_devic<public> := class(creative_device):
    @editable Area:area_box_devic = area_box_devic{}

    var MInited:?trigger_device_pool = false

    GetPool<public>()<transacts>:trigger_device_pool=
        if(Inited := MInited?):
            Inited
        else:
            Inited := trigger_device_pool:
                Area := Area
                D := Self
            .Init()
            set MInited = option. Inited
            Inited


trigger_device_pool<public> := class(class_interface):
    var FreeCreativeDevices : []trigger_device = array{}
    var AllCreativeDevices : []trigger_device = array{}
    Area<public>:area_interface
    D<public>:creative_device

    var Inited:logic = false
    
    Init<public>()<transacts>:trigger_device_pool=
        if(Inited?):
            Self
        else:
            set Inited = true
            set FreeCreativeDevices = for(Obj : D.GetDevicesInArea(trigger_device, Area)):
                Obj
            set AllCreativeDevices = FreeCreativeDevices
            Self

    GetAllFree<public>()<transacts>:[]trigger_device=
        if(not Inited?):
            LError()
        FreeCreativeDevices

    GetAll<public>()<transacts>:[]trigger_device=
        if(not Inited?):
            LError()
        AllCreativeDevices
        
    Rent<public>()<decides><transacts>:trigger_device=
        if(CreativeDevice := FreeCreativeDevices[0]
            Val := FreeCreativeDevices.RemoveElement[0]
            set FreeCreativeDevices = Val
        ):
            return CreativeDevice
        else:
            FailError[]
            return trigger_device{}

    RentDisposable<public>()<decides><transacts>:pooled_trigger_device=
        Device := Rent[]
        pooled_trigger_device:
            Device := Device
            MyPool := Self

    Return<public>(CreativeDevice:trigger_device):void=
        if. CreativeDevice.TeleportTo[Area.GetCenterPoint(), rotation{}]
        set FreeCreativeDevices += array. CreativeDevice


pooled_trigger_device<public> := class(i_disposable):
    MyPool<public>:trigger_device_pool
    Device<public>:trigger_device

    Dispose<override>():void=
        MyPool.Return(Device)

    
#id-a0dbdc97-48e6-485f-a070-3e50adead147