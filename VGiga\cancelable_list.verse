
cancelable_list<public> := class{
    var List:[]cancelable = array{}
    var List2:[](tuple()-> void) = array{}

    CancelClear<public> ():void={
        for(Cancelable:List){
            Cancelable.Cancel()
        }
        for(Cancelable:List2){
            Cancelable()
        }
        set List = array{}
        set List2 = array{}
    }
    Add<public>(ListP:[]cancelable):void={
        set List += ListP
    }
    Add<public>(Item:cancelable):void={
        set List += array{Item}
    }
    AddActionArr<public>(ListP:[](tuple()-> void)):void={
        set List2 += ListP
    }
    AddAction<public>(Item:(tuple()-> void)):void={
        set List2 += array{Item}
    }
}