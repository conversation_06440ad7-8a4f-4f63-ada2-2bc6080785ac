using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga
using. VResourcesSystem

trigger_checkpoints := class(auto_creative_device, i_init_async):
	var Resources :?resources_manager= false

	InitAsync<override>(Container:vcontainer)<suspends>:void=
		set Resources = Container.ResolveOp[resources_manager] or Err()
		Triggers := tag_trigger_checkpoint{}.GetAll(Self)

		for(Trigger:Triggers):
			spawn. OnTrigger(Trigger)

	OnTrigger(Trigger:trigger_device)<suspends>:void=
		loop:
			MAgent := Trigger.TriggeredEvent.Await()
			if(Agent := MAgent?):
				Resources.G().GivePoints(Agent, 10)

		
			
		
	