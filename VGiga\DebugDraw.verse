using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}

my_debug_draw<public> := class(debug_draw_channel) {}

(D:debug_draw).DrawLineHalfSecWhite<public>(Start:vector3, End:vector3)<transacts>:void={
	D.DrawLine(Start, 
		End,
		?Color := NamedColors.White,
		?DrawDurationPolicy := debug_draw_duration_policy.FiniteDuration,
		?Duration := 0.5,
		?Thickness := 5.0
	)
}

(D:debug_draw).DrawLineHalfSec<public>(Start:vector3, End:vector3, Color:color)<transacts>:void=
	D.DrawLine(Start, 
		End,
			?Color := Color,
			?DrawDurationPolicy := debug_draw_duration_policy.FiniteDuration,
			?Duration := 0.5,
			?Thickness := 5.0
	)
(D:debug_draw).DrawArrowHalfSec<public>(Start:vector3, End:vector3, Color:color)<transacts>:void=
	D.DrawArrow(Start, 
		End,
			?Color := Color,
			?DrawDurationPolicy := debug_draw_duration_policy.FiniteDuration,
			?Duration := 0.5,
			?Thickness := 5.0
	)

(D:debug_draw).DrawSphereHalfSec<public>(Start:vector3, Radius:float, Color:color)<transacts>:void=
	D.DrawSphere(Start,
			?Radius := Radius,
			?Color := Color,
			?DrawDurationPolicy := debug_draw_duration_policy.FiniteDuration,
			?Duration := 0.5,
			?Thickness := 5.0
	)
	