using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga

widget_syncer<public> := class:
    MainWidget<public>:widget
    var<private> WidgetsToSync<public>:[agent]widget = map{}

    Sync<public>():void=
        for(Ag->Widget:WidgetsToSync):
            SyncWidget(Widget, MainWidget)

    Add<public>(Agent:agent, Widget:widget):void=
        if. set WidgetsToSync[Agent] = Widget

    Remove<public>(Agent:agent):void=
        set WidgetsToSync = WidgetsToSync.WithRemoved(Agent)

    var DynamicStackBoxes:[stack_box][]stack_box_slot = map{}

    AddWidgetDynamicSlot<public>(Agent:agent, StackBox:stack_box, Slot:stack_box_slot):void=
        Widget:widget = Slot.Widget
        var StackBoxSlots :[]stack_box_slot= DynamicStackBoxes[StackBox] or array{}
        # if(StackBoxSlotMap[Widget]):
        #     LErrorPrint("stack box widget already existing")
        set StackBoxSlots += array. Slot
        if. set DynamicStackBoxes[StackBox] = StackBoxSlots
        StackBox.AddWidget(Slot)

    RemoveWidgetDynamicSlot<public>(Agent:agent, StackBox:stack_box, Widget:widget):void=
        var StackBoxSlots :[]stack_box_slot= DynamicStackBoxes[StackBox] or block:
            LError()
            array{}

        var I:int = 0
        loop:
            if(Element := StackBoxSlots[I], Element.Widget = Widget):
                if(NewSlots := StackBoxSlots.RemoveElement[I]):
                    StackBox.RemoveWidget(Widget)
                    if. set DynamicStackBoxes[StackBox] = NewSlots
                    break
                else:
                    LError()
                
            set I += 1
            if(I >= StackBoxSlots.Length):
                break

    SyncWidget<public>(Widget:widget, New:widget):void=
        Widget.SetVisibility(New.GetVisibility())
        Widget.SetEnabled(New.IsEnabled())

        if(Canvas := canvas[Widget]
            NewCanvas := canvas[New]
        ):
            Sync(Canvas, NewCanvas)

        else if(TexBlock := texture_block[Widget]
            NewTexBlock := texture_block[New]
        ):
            Sync(TexBlock, NewTexBlock)

        else if(TextBase := text_base[Widget]
            NewTextBase := text_base[New]
        ):
            Sync(TextBase, NewTextBase)

        else if(Old := button[Widget]
            NewWidget := button[New]
        ):
            Sync(Old, NewWidget)

        else if(Old := color_block[Widget]
            NewWidget := color_block[New]
        ):
            Sync(Old, NewWidget)
            
        else if(Old := overlay[Widget]
            NewWidget := overlay[New]
        ):
            Sync(Old, NewWidget)

        else if(Old := stack_box[Widget]
            NewWidget := stack_box[New]
        ):
            Sync(Old, NewWidget)
        else if(Old := text_block[Widget]
            NewWidget := text_block[New]
        ):
            SyncTextBlock(Old, NewWidget)

        else if(Old := text_button_base[Widget]
            NewWidget := text_button_base[New]
        ):
            Sync(Old, NewWidget)
        else:
            LErrorPrint("error widget sync, wrong order?")
            if(Old := canvas[Widget]):
                Print("Old is canvas")
            if(Old := texture_block[Widget]):
                Print("Old is texture_block")
            if(Old := text_base[Widget]):
                Print("Old is text_base")
            if(Old := button[Widget]):
                Print("Old is button")
            if(Old := color_block[Widget]):
                Print("Old is color_block")
            if(Old := overlay[Widget]):
                Print("Old is overlay")
            if(Old := stack_box[Widget]):
                Print("Old is stack_box")
            if(Old := text_block[Widget]):
                Print("Old is text_block")
            if(Old := text_button_base[Widget]):
                Print("Old is text_button_base")

            if(NewWidget := canvas[New]):
                Print("NewWidget is canvas")
            if(NewWidget := texture_block[New]):
                Print("NewWidget is texture_block")
            if(NewWidget := text_base[New]):
                Print("NewWidget is text_base")
            if(NewWidget := button[New]):
                Print("NewWidget is button")
            if(NewWidget := color_block[New]):
                Print("NewWidget is color_block")
            if(NewWidget := overlay[New]):
                Print("NewWidget is overlay")
            if(NewWidget := stack_box[New]):
                Print("NewWidget is stack_box")
            if(NewWidget := text_block[New]):
                Print("NewWidget is text_block")
            if(NewWidget := text_button_base[New]):
                Print("NewWidget is text_button_base")

    Sync<public>(Original:canvas, New:canvas):void=
        for(Index := 0..New.Slots.Length-1):
            if(Widget := Original.Slots[Index].Widget
                NewWidget := New.Slots[Index].Widget
            ):
                SyncWidget(Widget, NewWidget)

            else:
                LErrorPrint("error canvas sync, probably widget was removed")
                
    Sync(Original:texture_block, New:texture_block):void=
        Original.SetImage(New.GetImage())
        Original.SetDesiredSize(New.GetDesiredSize())
        Original.SetTint(New.GetTint())
        Original.SetTiling(New.GetTiling())

    Sync(TextBase:text_base, New:text_base):void=
        TextBase.SetText(New.GetText().ToMessage())
        TextBase.SetTextColor(New.GetTextColor())
        TextBase.SetJustification(New.GetJustification())
        TextBase.SetOverflowPolicy(New.GetOverflowPolicy())
        TextBase.SetTextOpacity(New.GetTextOpacity())

    Sync(Old:button, New:button):void=
        SyncWidget(Old.Slot.Widget, New.Slot.Widget)

    Sync(Old:color_block, New:color_block):void=
        Old.SetColor(New.GetColor())
        Old.SetOpacity(New.GetOpacity())
        Old.SetDesiredSize(New.GetDesiredSize())

    Sync(Old:overlay, New:overlay):void=
        if(Old.Slots.Length <> New.Slots.Length):
            OldSlotsToRemove := Old.Slots
            for(OldSlotToRem: OldSlotsToRemove):
                Old.RemoveWidget(OldSlotToRem.Widget)
            
            for(NewSlot:New.Slots):
                Old.AddWidget(CloneOverlaySlot(NewSlot))

        for(Index := 0..New.Slots.Length-1):
            if(Widget := Old.Slots[Index].Widget
                NewWidget := New.Slots[Index].Widget
            ):
                SyncWidget(Widget, NewWidget)

            else:
                LErrorPrint("error overlay sync, probably widget was removed")
                
    Sync(Old:stack_box, New:stack_box):void=
        if(NewSlots := DynamicStackBoxes[New]):
            var OldSlots :[]stack_box_slot= DynamicStackBoxes[Old] or array{}
            if(OldSlots.Length <> NewSlots.Length):
                LPrint("OldSlots.Length {OldSlots.Length} <> NewSlots.Length {NewSlots.Length}")
                for(OldSlotToRem:OldSlots):
                    Old.RemoveWidget(OldSlotToRem.Widget)
                set OldSlots = for(SlotToAdd:NewSlots):
                    NewSlotCloned := CloneStackBoxSlot(SlotToAdd)
                    Old.AddWidget(NewSlotCloned)
                    NewSlotCloned
                if. set DynamicStackBoxes[Old] = OldSlots

            for(Index := 0..NewSlots.Length-1):
                if(Widget := OldSlots[Index].Widget
                    NewWidget := NewSlots[Index].Widget
                ):
                    SyncWidget(Widget, NewWidget)

        for(Index := 0..New.Slots.Length-1):
            if(Widget := Old.Slots[Index].Widget
                NewWidget := New.Slots[Index].Widget
            ):
                SyncWidget(Widget, NewWidget)

            else:
                LErrorPrint("error stack_box sync, probably widget was removed")

    SyncTextBlock(Old:text_block, New:text_block):void=
        Old.SetShadowOffset(New.GetShadowOffset())
        Old.SetShadowColor(New.GetShadowColor())
        Old.SetShadowOpacity(New.GetShadowOpacity())

    Sync(Old:text_button_base, New:text_button_base):void=
        Old.SetText(New.GetText().ToMessage())

        #todo slider slider_regular

better_stack_box_c(StackBox:stack_box):better_stack_box=
    better_stack_box:
        StackBox := StackBox
        Slots := StackBox.Slots

better_stack_box := class:
    StackBox:stack_box
    var Slots<public>:[]stack_box_slot = array{}

    AddWidget<public>(Slot:stack_box_slot):void=
        set Slots += array. Slot
        StackBox.AddWidget(Slot)

    RemoveWidget<public>(Widget:widget):void=
        var Index:int = -1
        var I:int = 0
        loop:
            if(Slots[I].Widget = Widget):
                set Index = I
                
            set I += 1
            if(I >= Slots.Length):
                break

        if:
            NewSlots := Slots.Remove[I, I + 1]
            set Slots = NewSlots
        StackBox.RemoveWidget(Widget)