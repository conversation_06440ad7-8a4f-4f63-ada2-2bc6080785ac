#id-8c8de2e5-6fdd-417e-ba3d-d1fe93ab91d8
using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using.  VGiga 

beacon_device_pool_devic<public> := class(auto_creative_device, i_init):
    @editable Area:area_box_devic = area_box_devic{}
    var FreeCreativeDevices : []beacon_device = array{}
    var AllCreativeDevices : []beacon_device = array{}

    var PlayerEvents :?player_events_manager_devic= false
    var ShowToAll :logic= false

    Init<override>(Container:vcontainer):void=
        set PlayerEvents = Container.ResolveOp[player_events_manager_devic] or Err()
        set FreeCreativeDevices = for(Obj : Self.GetDevicesInArea(beacon_device, Area)):
            Obj
        set AllCreativeDevices = FreeCreativeDevices

    GetAllFree<public>()<transacts>:[]beacon_device=
        FreeCreativeDevices

    GetAll<public>()<transacts>:[]beacon_device=
        AllCreativeDevices
        
    SetShowToAll<public>():void=
        set ShowToAll = true

    Rent<public>()<decides><transacts>:beacon_device=
        if(CreativeDevice := FreeCreativeDevices[0]
            Val := FreeCreativeDevices.RemoveElement[0]
            set FreeCreativeDevices = Val
        ):
            return CreativeDevice
        else:
            FailError[]
            return beacon_device{}

    RentDisposable<public>()<decides><transacts>:pooled_beacon_device=
        Device := Rent[]
        pooled_beacon_device:
            Device := Device
            MyPool := Self

    Return<public>(CreativeDevice:beacon_device):void=
        if. CreativeDevice.TeleportTo[Area.GetCenterPoint(), rotation{}]
        set FreeCreativeDevices += array. CreativeDevice


pooled_beacon_device<public> := class(i_disposable):
    MyPool<public>:beacon_device_pool_devic
    Device<public>:beacon_device

    Dispose<override>():void=
        MyPool.Return(Device)

    ForAgent<public>(Agent:agent):pooled_beacon_device_for_agent=
        if(MyPool.ShowToAll?):
            for(Ag->Data:MyPool.PlayerEvents.G().RegisteredPlayers):
                for(X:=0..10):
                    Device.AddToShowList(Ag)
        else:
            Device.AddToShowList(Agent)

        pooled_beacon_device_for_agent:
            MyPool := MyPool
            Device := Device
            Agent := Agent
    

pooled_beacon_device_for_agent<public> := class(i_disposable):
    MyPool<public>:beacon_device_pool_devic
    Device<public>:beacon_device
    Agent<public>:agent

    Dispose<override>():void=
        if(MyPool.ShowToAll?):
            for(Ag->Data:MyPool.PlayerEvents.G().RegisteredPlayers):
                Device.RemoveFromShowList(Ag)
        else:
            Device.RemoveFromShowList(Agent)
        MyPool.Return(Device)