using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using {/Verse.org/Random}
using { VGiga }
using { VGiga.Defaults }
<#

VAR1_devic<public> := class(creative_device):
	
	Init<public>(Container:player_events_manager_devic
	):VAR1=
		Manager := VAR1:
			D := Self
		.Init()
		Container.Register(Manager)
		Manager

VAR1<public> := class<internal>(i_init_per_player_async):
	D:VAR1_devic
	var PlayerDataMap : map_agent_VAR2 = map_agent_VAR2{}

	Init():VAR1=
		Self
	
	InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
		PlayerDataMap.Set(Agent, VAR2{})
		PlayerRemoved.Await()
		PlayerDataMap.Remove(Agent)
	

VAR2<public> := class():
	
#>
#gen
#map_t1_t2
#agent
#VAR2