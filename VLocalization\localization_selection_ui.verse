using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }

localization_selection_ui<public> := class(i_init, auto_creative_device, i_init_per_player_async):
	@editable LangSelectionPopup:?popup_dialog_device = false
	@editable LangSelectionPopupLangs:[]lang = array{lang.Eng, lang.Pol}

	var OpenAtPlayerStart :logic= true
	var Localization:i_localization = empty_i_localization{}

	Init<override>(Container:vcontainer):void=
		LangSelectionPopup.G()
		set Localization = Container.Resolve_i_localization()

	InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
		Print("localization_selection_ui InitPlayerAsync"); Sleep(0.0)
		if(OpenAtPlayerStart?):
			Sleep(1.0)
			Popup := LangSelectionPopup.G()
			Popup.Show(Agent)
			race:
				Sleep(30.0)
				loop:
					AgentWithId := Popup.RespondingButtonEvent.Await()
					if(Agent = AgentWithId(0)):
						ButtonClicked := AgentWithId(1)
						Lang := LangSelectionPopupLangs[ButtonClicked] or Err()
						Localization.SetLanguage(Agent, Lang)
						break
				Popup.DismissedEvent.AwaitFor(Agent)
				Popup.TimeOutEvent.AwaitFor(Agent)
			Popup.Hide(Agent)

