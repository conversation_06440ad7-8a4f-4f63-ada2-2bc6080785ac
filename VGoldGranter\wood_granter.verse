# using {/Verse.org/Simulation}
# using {/Verse.org/Simulation/Tags}
# using {/Verse.org/Assets}
# using {/Verse.org/Verse}
# using {/Verse.org/Random}
# using {/Verse.org/Colors}
# using {/Verse.org/Colors/NamedColors}
# using {/Verse.org/Native}
# using {/Verse.org/Concurrency}
# using {/UnrealEngine.com/Temporary}
# using {/UnrealEngine.com/Temporary/UI}
# using {/UnrealEngine.com/Temporary/SpatialMath}
# using {/UnrealEngine.com/Temporary/Diagnostics}
# using {/UnrealEngine.com/Temporary/Curves}
# using {/Fortnite.com/UI}
# using {/Fortnite.com/Devices}
# using {/Fortnite.com/Devices/CreativeAnimation}
# using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
# using {/Fortnite.com/Vehicles}
# using {/Fortnite.com/Teams}
# using {/Fortnite.com/Playspaces}
# using {/Fortnite.com/Game}
# using {/Fortnite.com/FortPlayerUtilities}
# using {/Fortnite.com/Characters}
# using {/Fortnite.com/AI}
# using { VGiga }
# using { VGiga.Pool }


# wood_granter_devic<public> := class(creative_device):
# 	@editable GrantersPool:item_granter_device_pool = item_granter_device_pool{}
	
# 	Init<public>(Container:player_events_manager_devic):wood_granter=
# 		Manager := wood_granter:
# 			D := Self
# 			ItemGranterPool := GrantersPool
# 		.Init()
# 		Container.Register(Manager)
# 		Manager


# wood_granter<public> := class(i_init_per_player_async):
# 	D<public>:wood_granter_devic
# 	var PlayerDataMap : p_data_map = p_data_map{}
# 	ItemGranterPool<public> : item_granter_device_pool
# 	var GoldGrantTickRate : float = 2.0

# 	Init():wood_granter=
# 		spawn. Run()
# 		Self

# 	Run()<suspends>:void=
# 		loop:
# 			for(Player -> Data : PlayerDataMap.DataMap, Data.GoldToGrant > 0):
# 				GrantWood(Player, Data.GoldToGrant)
# 				set Data.GoldToGrant = 0
# 				Sleep(0.1)
			
# 			Sleep(GoldGrantTickRate)
	
# 	InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
# 		Print("wood_granter InitPlayerAsync"); Sleep(0.0)
# 		PlayerDataMap.Set(Agent, p_data{})
# 		PlayerRemoved.Await()
# 		PlayerDataMap.Remove(Agent)


# 	AddToGrant<public>(Agent:agent, Amount:int):void=
# 		if(PlayerData := PlayerDataMap.Get[Agent], Amount > 0):
# 			set PlayerData.GoldToGrant += Amount
# 			PlayerDataMap.Set(Agent, PlayerData)


# 	GrantWood<private>(Agent:agent, Amount:int):void=
# 		var CurrentAmount : int = Amount
		
# 		if(GoldGranter := ItemGranterPool.Rent[]):
# 			Powers := array{6,5,4,3,2,1,0}
# 			for(Power : Powers):
# 				if(Divider := Round[Pow(10.0, Power * 1.0)]
# 					Iterations := Floor(CurrentAmount / Divider)
# 					Iterations > 0
# 					CrAmt := Mod[Amount, Divider]
# 					set CurrentAmount = CrAmt
# 				):
# 					GoldGranter.SetNextItem(Power)
# 					for(I:=1..Iterations):
# 						GoldGranter.GrantItem(Agent)

# 			ItemGranterPool.Return(GoldGranter)
