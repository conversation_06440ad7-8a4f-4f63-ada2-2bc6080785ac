using. /Fortnite.com/AI
using. /Fortnite.com/Characters
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Game
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Teams
using. /Fortnite.com/UI
using. /Fortnite.com/Vehicles
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/Curves
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/UI
using. /Verse.org/Assets
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Concurrency
using. /Verse.org/Native
using. /Verse.org/Random
using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Verse
using. VCreaturesSystem
using. VGiga
using. VHouseSystem
using. VQuestSystem
using. VResourcesSystem


gm_balance := class(class_interface):
	var GainRatio<public> :float = 1.0
	var MatchmakingDisableAfterMinutes<public>:float = 120.0
	var HouseBalance<public>:houses_balance = gm_balance_houses{}.HouseBalance

	var QuestBalance<public>:quest_system_balance = quest_system_balance{}
	var ResourcesManagerBalance<public>:resources_manager_balance = resources_manager_balance{}
	var BalanceSpawners<public>:upgradable_spawner_balance = upgradable_spawner_balance{}
	var TestBalance<public>:logic = false
	var Rewards<public>:gm_balance_rewards = gm_balance_rewards{}
	# var PetsBalance<public>:pets_balance = pets_balance{}

	FillBalanceSpawnersFromHouse<public>(SpawnDelayInUefn:[]float, ArrayToPrefixHouseGains:[]int):void=

		GoldGainPerSecArr := ArrayToPrefixHouseGains + for(
			I->Upgrade:HouseBalance.Presets.Map
			Upgrade.EventDataAfterUnlocking.ArrayFirst[IsSpawnerUpgrade]
		):
			Upgrade.GoldGainPerSec

		var PrevPerKill :float= 0.0
		set BalanceSpawners.GoldPerKillArr = for(X->GoldGain:GoldGainPerSecArr):
			if (SpawnDelay := SpawnDelayInUefn[X]
				PerKillF := GoldGain * SpawnDelay
				PerKillWithPrev := Floor[PerKillF + PrevPerKill] 
			):
				set PrevPerKill = PerKillF
				PerKillWithPrev
			else:
				LError()
				0

	ChangeIfTestBalance<public>():void=
		if(TestBalance?):
			for(Preset:HouseBalance.Presets.Map, Preset.GoldCost > 0):
				set Preset.GoldCost = 1

gm_balance_rewards := class(class_interface):
	var RaceParkourRatio<public>:float = 1.0
	var RaceParkourRatioCoin<public>:float = 1.0
	
	var MGlassBridgeCompleteTimeSec<public>:?float = option. 150.0
	var GlassBridgeCompleteRatio<public>:float = 1.0

	var GlassBridgePlateRatio<public>:float = 1.0
	var MGlassBridgePlateTimeSec<public>:?float = option. 150.0

	var PiggyRatio<public>:float = 0.33
	var PiggyTimeSec<public>:float = 10.0

	var FruitRatio<public>:float = 4.0
	var FruitTimeSec<public>:float = 0.6
	
	var HitOreRatio<public>:float = 0.3
	var HitOreTimeSec<public>:float = 0.5
	