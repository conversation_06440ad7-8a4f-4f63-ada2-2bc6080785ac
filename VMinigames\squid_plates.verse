using { /UnrealEngine.com/Assets }
using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga
using. VResourcesSystem
using. VPropSpawner

squid_plates<public> := class(auto_creative_device, i_init_async, i_init_per_player_async):
	@editable ResetTimer:?timer_device = false
	@editable ClaimRewardButton:?button_device = false
	@editable EndPlayerCounterForReward:?player_counter_device = false
	@editable FloorVolume:?volume_device = false
	@editable StartTeleporter:?teleporter_device = false
	@editable MInputDeviceSprintEater:?input_trigger_device = false

	@editable CallWhenStartedChannel:?channel_device = false
	@editable CallWhenEndedChannel:?channel_device = false
	@editable PlateAsset:?creative_prop_asset = false

	@editable MGlassParticlesAsset:?creative_prop_asset = false
	@editable MPlayerWaterSplashParticlesAsset:?creative_prop_asset = false

	TimeOfPlateBeingHidden:float = 1.0
	TimeOfPlatesReset:float = 60.0 * 2
	# TimeOfPlatesReset:float = 30.0

	TimeOnePlateReward :float= 5.0 

	var TriggerGroups:[]triggers_group = array{}
	ResetEv:event() = event(){}
	PlayerEnteredOnEmptyEv:event() = event(){}
	PlayerCompletedMinigameEv<public>:event(agent) = event(agent){}

	var Resources :?resources_manager= false
	var PlayerEvents:?player_events_manager_devic = false
	var PropSpawner :?prop_spawner= false
	var MinigameResult :?minigame_result= false
	var Balance :?gm_balance= false

	var PlayersInMinigame:[agent]logic = map{}

	InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
		race:
			PlayerRemoved.Await()
			loop:
				PlayerEvents.G().PlayerEliminatedCharacter.AwaitFor(Agent)
				CallWhenEndedChannel.G().Transmit(option. Agent)
			loop:
				race:
					CallWhenStartedChannel.G().ReceivedTransmitEvent.AwaitForOption(Agent)
					StartTeleporter.G().TeleportedEvent.AwaitFor(Agent)
				if(PlayersInMinigame.Length = 0):
					PlayerEnteredOnEmptyEv.Signal()
				if(InputDeviceSprintEater := MInputDeviceSprintEater?):
					InputDeviceSprintEater.Register(Agent)
				if. set PlayersInMinigame[Agent] = true
				CallWhenEndedChannel.G().ReceivedTransmitEvent.AwaitForOption(Agent)
				if(InputDeviceSprintEater := MInputDeviceSprintEater?):
					InputDeviceSprintEater.Unregister(Agent)
				set PlayersInMinigame = PlayersInMinigame.WithRemoved(Agent)
			loop:
				FloorVolume.G().AgentEntersEvent.AwaitFor(Agent)
				if(Char := Agent.GetFortCharacterActive[]):
					if(PlayerWaterSplashParticles := MPlayerWaterSplashParticlesAsset?):
						MProp := PropSpawner.G().Spawn(PlayerWaterSplashParticles, Char.GetTransform().AddTranslationZ(25.0))
						if(Prop := MProp?):
							DisposePropLater of Prop
				Sleep(1.0)
				if(Char := Agent.GetFortCharacterActive[]):
						StartTeleporter.G().Teleport(Agent)
		set PlayersInMinigame = PlayersInMinigame.WithRemoved(Agent)

	DisposePropLater(Particles:i_disposable):void=
		spawn. DisposePropLaterAsync(Particles)
	DisposePropLaterAsync(Particles:i_disposable)<suspends>:void=
		Sleep(5.0)
		Particles.Dispose()
	
	InitAsync<override>(Container:vcontainer)<suspends>:void=
		set Balance = Container.ResolveOp[gm_balance] or Err()
		set MinigameResult = Container.ResolveOp[minigame_result] or Err()
		set PropSpawner = Container.ResolveOp[prop_spawner] or Err()
		set Resources = Container.ResolveOp[resources_manager] or Err()
		set PlayerEvents = Container.ResolveErrOp(player_events_manager_devic)

		Plates :[]positional= tag_squid_plate_trigger{}.GetAll(Self) 
		PlatesSorted := Plates.SortAsc(Compare_positional_X)

		for(I:= 0..PlatesSorted.Length-1):
			if(	Mod[I, 2] = 0
				TriggerOne := trigger_device[PlatesSorted[I]]
				TriggerTwo := trigger_device[PlatesSorted[I+1]]
			):
				set TriggerGroups += array:
					triggers_group:
						TriggerOne := trig_w_inittr:
							Trig := TriggerOne
							InitTr := TriggerOne.GetTransform()
						TriggerTwo := trig_w_inittr:
							Trig := TriggerTwo
							InitTr := TriggerTwo.GetTransform()
		
		for(Group:TriggerGroups):
			spawn. HandleGroup(Group)

		loop:
			VResetTimer := ResetTimer.G()
			VResetTimer.SetMaxDuration(TimeOfPlatesReset)
			VResetTimer.SetActiveDuration(TimeOfPlatesReset)
			VResetTimer.ResetForAll()
			VResetTimer.StartForAll()
			race:
				Sleep(TimeOfPlatesReset)
				ClaimRewardButton.G().InteractedWithEvent.Await()
				PlayerEnteredOnEmptyEv.Await()
			for(Agent->Player:PlayerEvents.G().RegisteredPlayers):
				if(EndPlayerCounterForReward.G().IsCounted[Agent]):
					PlayerCompletedMinigameEv.Signal(Agent)
					if(TimeCompletedReward := Balance.G().Rewards.MGlassBridgeCompleteTimeSec?):
						Resources.G().GivePoints(Agent, TimeCompletedReward)
						Resources.G().GiveGoldWithMultiplier(Agent, TimeCompletedReward, Balance.G().Rewards.GlassBridgeCompleteRatio, ?ShowNotification := true)

						if(TimeCompletedRewardInt := Floor[TimeCompletedReward]):
							MinigameResult.G().ShowResultHud(Agent, TimeCompletedRewardInt) 

					if(Agent.GetFortCharacterActive[]):
						StartTeleporter.G().Teleport(Agent)

			for(Group:TriggerGroups):
				Group.RewardedPlayers.Clear()
			ResetEv.Signal()
			# loop:
			# 	ResetEv.Await()

	HandleGroup(Group:triggers_group)<suspends>:void=
		ShowTrig(Group.TriggerOne)
		ShowTrig(Group.TriggerTwo)
		race:
			loop:
				ResetEv.Await()
				Sleep(0.0) # make sure hidden are shown first
				HideTrig(Group.TriggerOne)
				HideTrig(Group.TriggerTwo)
				Sleep(3.0)
				set Group.TrigToHide = false

				ShowTrig(Group.TriggerOne)
				ShowTrig(Group.TriggerTwo)

			loop:
				MAgentWTrigger := race:
					block:
						MAgentt := Group.TriggerOne.Trig.TriggeredEvent.Await()
						(MAgentt, Group.TriggerOne)
					block:
						MAgentt := Group.TriggerTwo.Trig.TriggeredEvent.Await()
						(MAgentt, Group.TriggerTwo)

				MAgent := MAgentWTrigger(0)
				TriggerHit := MAgentWTrigger(1)


				TrigToHide := if(OldTrig := Group.TrigToHide?):
					OldTrig
				else:
					RandTrigToHide := if(GetRandomInt(0,1) = 0):
						Group.TriggerOne
					else:
						Group.TriggerTwo
					set Group.TrigToHide = option. RandTrigToHide
					RandTrigToHide

				MTrigToGivePoints := Group.TrigToNotHide()

				if(TriggerHit = MTrigToGivePoints?):
					GivePointsIfNotRewarded(MAgent, Group)

				if(TriggerHit = TrigToHide):
					HideTrig(TrigToHide)
					race:
						Sleep(TimeOfPlateBeingHidden)
						ResetEv.Await()
						loop:
							if(TriggerForPoints := MTrigToGivePoints?):
								MAgent22 := TriggerForPoints.Trig.TriggeredEvent.Await()
								GivePointsIfNotRewarded(MAgent22, Group)
					ShowTrig(TrigToHide)

	ShowTrig(Trig:trig_w_inittr)<suspends>:void=
		Trig.Show()
		if(not Trig.SpawnedPlate?):
			set Trig.SpawnedPlate = PropSpawner.G().Spawn(PlateAsset.G(), Trig.InitTr.AddTranslationZ(25.0))
	
	HideTrig(Trig:trig_w_inittr)<suspends>:void=
		if(GlassParticlesAsset := MGlassParticlesAsset?):
			MProp := PropSpawner.G().Spawn(GlassParticlesAsset, Trig.InitTr.Translation, Rotation0)
			if(Prop := MProp?):
				DisposePropLater of Prop

		Trig.Hide()
		if(Spawned := Trig.SpawnedPlate?):
			Spawned.Dispose()
			set Trig.SpawnedPlate = false

	GivePointsIfNotRewarded(MAgent:?agent, Group:triggers_group):void=
		if(Agent := MAgent?, not Group.RewardedPlayers.Get[Agent]):
			Group.RewardedPlayers.Set(Agent, true)
			if(PlateReward := Balance.G().Rewards.MGlassBridgePlateTimeSec?):
				Resources.G().GivePoints(Agent, PlateReward)
				Resources.G().GiveGoldWithMultiplier(Agent, PlateReward, Balance.G().Rewards.GlassBridgePlateRatio, ?ShowNotification := true)
		
			

Compare_positional_X(AD:positional, BD:positional)<transacts>:int =
	A := AD.GetTransform().Translation.X
	B := BD.GetTransform().Translation.X
    if(A < B). -1
    else if(A > B). 1
    else	0

triggers_group := class():
	TriggerOne:trig_w_inittr
	TriggerTwo:trig_w_inittr
	var TrigToHide:?trig_w_inittr = false
	TrigToNotHide():?trig_w_inittr=
		if(VTrigToHide := TrigToHide?):
			if(TriggerOne = VTrigToHide):
				option. TriggerTwo
			else:
				option. TriggerOne
		else:
			false
		
	RewardedPlayers:map_agent_logic = map_agent_logic{}

trig_w_inittr := class<unique>():
	Trig:trigger_device
	InitTr:transform
	var SpawnedPlate:?creative_prop_unique = false

	Hide()<suspends>:void=
		# Trig.MoveTo(InitTr.AddTranslationZ(-5000.0), 1.1) 
		if. Trig.TeleportTo[InitTr.AddTranslationZ(-5000.0)]
	Show():void=
		if. Trig.TeleportTo[InitTr]
		
	