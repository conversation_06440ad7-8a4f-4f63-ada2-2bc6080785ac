using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }

prefab_definition_devic<public> := class(creative_device, class_interface):
	@editable
	Prefabs:[]prefab_definition_devic_object = array{}

	var PrefabsTransforms:[]transform = array{}

	var Inited:logic = false

	OnBegin<override>()<suspends>:void=
		if(Inited?):
			return

		set Inited = true
		Sleep(0.0)
		ParentTr := GetTransform()
		set PrefabsTransforms = for(I:Prefabs):
			var Tr :transform= I.Prop.GetTransform()
			set Tr.Translation = -ParentTr.Translation + Tr.Translation 
			Tr


	Spawn<public>(Transform:transform, Spawner:prop_spawner)<suspends>:props_manager=
		props_manager{Definition := Self}.Spawn(Transform, Spawner)


prefab_definition_devic_object := class<concrete>:
	@editable
	Prop:creative_prop = creative_prop{}
	@editable
	Asset:creative_prop_asset = DefaultCreativePropAsset
