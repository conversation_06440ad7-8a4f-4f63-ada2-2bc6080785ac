using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
using. VCustomBillboard.Assets
# using. VPropSpawner
using. VGiga.Assets.Mats

custom_billboard_devic<public> := class(creative_device):
	@editable BillboardProp:creative_prop = creative_prop{}

	
	SetText<public>(Text:string):void=
		var StartIndex :int= 0
	
		IsGreen := if(Text[StartIndex] = '<'
			Text[StartIndex+1] = 'g'
			Text[StartIndex+2] = 'r'
			Text[StartIndex+3] = 'e'
			Text[StartIndex+4] = 'e'
			Text[StartIndex+5] = 'n'
			Text[StartIndex+6] = '>'
		):
			set StartIndex += 7
			true
		else:
			false
		Len := Text.Length - StartIndex
		if(Len > 32):
			LErrorPrint("Message is too long: {Text}")
		End := Min(32, Len)
	
		if(IsGreen?):
			SetTextForPropGreen(BillboardProp, Text, StartIndex, End)
		else:
			SetTextForPropWhite(BillboardProp, Text, StartIndex, End)