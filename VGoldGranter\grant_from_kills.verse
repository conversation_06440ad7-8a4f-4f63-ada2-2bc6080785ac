# using {/Verse.org/Simulation}
# using {/Verse.org/Simulation/Tags}
# using {/Verse.org/Assets}
# using {/Verse.org/Verse}
# using {/Verse.org/Random}
# using {/Verse.org/Colors}
# using {/Verse.org/Colors/NamedColors}
# using {/Verse.org/Native}
# using {/Verse.org/Concurrency}
# using {/UnrealEngine.com/Temporary}
# using {/UnrealEngine.com/Temporary/UI}
# using {/UnrealEngine.com/Temporary/SpatialMath}
# using {/UnrealEngine.com/Temporary/Diagnostics}
# using {/UnrealEngine.com/Temporary/Curves}
# using {/Fortnite.com/UI}
# using {/Fortnite.com/Devices}
# using {/Fortnite.com/Devices/CreativeAnimation}
# using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
# using {/Fortnite.com/Vehicles}
# using {/Fortnite.com/Teams}
# using {/Fortnite.com/Playspaces}
# using {/Fortnite.com/Game}
# using {/Fortnite.com/FortPlayerUtilities}
# using {/Fortnite.com/Characters}
# using {/Fortnite.com/AI}
# using { VGiga }
# using. VResourcesSystem
# using. VCreaturesSystem
# using. VNotifications

# grant_from_kills_devic<public> := class(auto_creative_device, i_init, i_init_per_player_async):
# 	var ResourcesManager:?resources_manager = false
# 	var PlayerDataMap : map_agent_p_data_grant_from_kills = map_agent_p_data_grant_from_kills{}
# 	var Container<public>:?player_events_manager_devic = false
# 	var NotificationsFeed:?notifications_feed_devic = false

# 	@editable MXpAccoladeDeviceCreature:?accolades_device = false
	

# 	Init<override>(C:vcontainer):void=
# 		resources_manager
# 		set	ResourcesManager = option. C.ResolveErr(resources_manager)
# 		set	Container = option. C.ResolveErr(player_events_manager_devic)
# 		CreaturesSystem := C.ResolveErr(creatures_system)
# 		# zombies
# 		CreaturesSystem.CreatureEliminatedEvent.Subscribe1(OnCreatureEliminated)
# 		Container.G().PlayerEliminated.Subscribe1(OnPlayerEliminated)
# 		# players
# 		set NotificationsFeed = C.ResolveErrOp(notifications_feed_devic)

# 	OnCreatureEliminated(Result:device_ai_interaction_result):void=
# 		if(Killer := Result.Source?
# 			Amount := PlayerDataMap.Get[Killer].GoldPerCreatureKill
# 		):
# 			ResourcesManager.G().GiveGoldText(Killer, Amount, "Elimination")
# 			if(Xp := MXpAccoladeDeviceCreature?):
# 				Xp.Award(Killer)
# 				NotificationsFeed.G().Show(Killer, "✨".ToMessage(), "XP granted".ToMessage(), ?Color := NamedColors.Yellow)

# 	OnPlayerEliminated(Result:elimination_result):void=
# 		if(Killer := Result.EliminatingCharacter?.GetAgent[]
# 			Killed := Result.EliminatedCharacter.GetAgent[]
# 			Killer <> Killed
# 			Amount := PlayerDataMap.Get[Killer].GoldPerPlayerKill
# 		):
# 			# LPrint("OnPlayerEliminated GiveGold {Amount}")
# 			ResourcesManager.G().GiveGoldText(Killer, Amount, "Elimination")
# 			NotificationsFeed.G().Show(Killer, "✨".ToMessage(), "XP granted".ToMessage(), ?Color := NamedColors.Yellow)


# 	# SetGoldPerCreatureKill<public>(Agent:agent, Value:int):void=
# 	# 	if(Data := PlayerDataMap.Get[Agent]):
# 	# 		set Data.GoldPerCreatureKill = Value
	
# 	SetGoldPerPlayerKill<public>(Agent:agent, Value:int):void=
# 		# LPrint("SetGoldPerPlayerKill {Value}")
# 		if(Data := PlayerDataMap.Get[Agent]):
# 			set Data.GoldPerPlayerKill = Value
	
# 	InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
# 		PlayerDataMap.Set(Agent, p_data_grant_from_kills{})
# 		# ResourcePData :res_p_data= race:
# 		# 	ResourcesManager.PlayerDataMap.GetOrAwait(Agent)
# 		# 	block:
# 		# 		PlayerRemoved.Await()
# 		# 		return

# 		# race:
# 		PlayerRemoved.Await()
# 			# loop:
# 			# 	SetGoldPerPlayerKill(Agent, ResourcePData.GoldPerSec.Get() * 20)
# 			# 	ResourcePData.GoldPerSec.ChangedEvent.Await()

# 		PlayerDataMap.Remove(Agent)

# p_data_grant_from_kills<public> := class:
# 	var GoldPerCreatureKill :int = 10
# 	var GoldPerPlayerKill :int = 10


# #gen
# #map_t1_t2
# #agent
# #p_data_grant_from_kills
# #id-f6989bc7-930c-485b-8794-5a8fe87dea98
# map_agent_p_data_grant_from_kills<public> := class:
# 	var DataMap:[agent]p_data_grant_from_kills = map{}
# 	var MDataSetEvent:?event(tuple(agent, p_data_grant_from_kills)) = false

# 	Get<public>(Key:agent)<decides><transacts>:p_data_grant_from_kills=
# 		DataMap[Key]

# 	GetErr<public>(Key:agent)<decides><transacts>:p_data_grant_from_kills=
# 		if(Data := DataMap[Key]):
# 			Data
# 		else:
# 			FailError[]
# 			Err()

# 	GetMap<public>()<transacts>:[agent]p_data_grant_from_kills=
# 		DataMap

# 	GetOrAwait<public>(Key:agent)<suspends>:p_data_grant_from_kills=
# 		if(Data := DataMap[Key]):
# 			Data
# 		else:
# 			if(DataSetEvent := MDataSetEvent?):
# 				DataSetEvent.AwaitForData(Key)
# 			else:
# 				Ev := event(tuple(agent, p_data_grant_from_kills)){}
# 				set MDataSetEvent = option. Ev
# 				Ev.AwaitForData(Key)
			

# 	Set<public>(Key:agent, Data:p_data_grant_from_kills):void=
# 		if. set DataMap[Key] = Data
# 		if(DataSetEvent := MDataSetEvent?):
# 			DataSetEvent.Signal((Key, Data))

# 	Remove<public>(Key:agent)<transacts>:void=
# 		set DataMap = DataMap.WithRemoved(Key)
# #id-f6989bc7-930c-485b-8794-5a8fe87dea98