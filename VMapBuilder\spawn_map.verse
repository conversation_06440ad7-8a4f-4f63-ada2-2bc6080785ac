using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using.  VGiga 
using. VPropSpawner

BSizeF :float= 96.0

spawn_map_devic<public> := class(creative_device):
	@editable FromTopCastPropAsset:creative_prop_asset = DefaultCreativePropAsset
	@editable Datas:[]spawn_map_data = array{}

	Init<public>(Container:player_events_manager_devic,
		PropSpawner:prop_spawner,
		SpawnAtStart:logic
	)<suspends>:spawn_map=
		Manager := spawn_map_c(Self, PropSpawner, Container)
		.Init(SpawnAtStart)

#gen
#con
#id-2e4e0a3e-7d69-48dd-9bf3-06ae8dc8fd85
spawn_map_c(PD:spawn_map_devic,
	PPropSpawner:prop_spawner,
	PContainer:player_events_manager_devic
)<transacts>:spawn_map=
	spawn_map:
		D := PD
		PropSpawner := PPropSpawner
		Container := PContainer
#id-2e4e0a3e-7d69-48dd-9bf3-06ae8dc8fd85
spawn_map<public> := class():
	D<public>:spawn_map_devic
	PropSpawner<public>:prop_spawner
	Container<public>:player_events_manager_devic
	var SpawnedProps:[]creative_prop_unique = array{}
	var SpawnedDisposables:[]i_disposable = array{}
	# @editable PropAssets:[]prop_asset_data = array{}

	var IsSpawned:logic = false

	BeforeMapRespawnForAgentInAreaEvent<public>:event(agent) = event(agent){}

	Init<public>(SpawnAtStart:logic)<suspends>:spawn_map=
		for(Data:D.Datas):
			Data.Init()
		if(SpawnAtStart?):
			RespawnAll()
		Self

	DespawnAllEvent:event() = event(){}
	DespawnAll<public>():void=
		Logic:logic = true
		# LPrint("DespawnAll")
		# LPrint("SpawnedProps.Length {SpawnedProps.Length}")
		for(Prop:SpawnedProps):
			Prop.Dispose()
		for(Prop:SpawnedDisposables):
			Prop.Dispose()
		set SpawnedDisposables = array{}
		set SpawnedProps = array{}
		set IsSpawned = false
		DespawnAllEvent.Signal()

	RespawnAll<public>():void=
		if(IsSpawned?):
			return

		set IsSpawned = true
		Vector3OneX := vector3
		for(Data:D.Datas
			Region:Data.Region
			Area := Region.Area
		):
			for(Player->Void:Container.GetRegisteredPlayers()
				PlayerPos := Player.GetFortCharacterActive[].GetTransform().Translation
				Area.IsInsideOrClose[PlayerPos, 500.0]
			):
				BeforeMapRespawnForAgentInAreaEvent.Signal(Player)
				
			StartPos :=  Area.MinPos
			EndPos := Area.MaxPos
			DistBetween := Region.DistanceBetweenSpawns
			DistBetweenHalf := DistBetween / 2.0
			if(AmountPerX := Floor[(EndPos.X - StartPos.X) / DistBetween]
				AmountPerY := Floor[(EndPos.Y - StartPos.Y) / DistBetween]
			):
				for(X := 0..AmountPerX
					Y := 0..AmountPerY
			# 		ToEndPos := -StartPos + EndPos
			# 		AmountToSpawn := 
			# 			if(Path.SpawnMode = spawn_map_path_mode.DistancePerSpawn):
			# 				Floor[ToEndPos.LengthSquared() / Path.DistancePerSpawnSquared]
			# 			else:
			# 				Path.AmountToSpawn
			# 		ToEndPosDividedVec := ToEndPos.Divide(AmountToSpawn.F())
			# 		StartId := if(Path.SkipSpawningStart?). 1 else. 0
			# 		EndId := AmountToSpawn- if(Path.SkipSpawningEnd?). 2 else. 1
			# 		X:=StartId..EndId
					PropAssetData := Data.PropAssets.GetRandom[]
					RandForNotSpawn := GetRandomFloat(0.0, 100.0)
					RandForNotSpawn > PropAssetData.ChanceToNotSpawnAtAllPct
					Pos := vector3:
						X := StartPos.X + X * DistBetween + GetRandomFloat(-DistBetweenHalf, DistBetweenHalf)
						Y := StartPos.Y + Y * DistBetween + GetRandomFloat(-DistBetweenHalf, DistBetweenHalf)
						Z := StartPos.Z + PropAssetData.OffsetZ + GetRandomFloat(PropAssetData.OffsetZMin, PropAssetData.OffsetZMax)
					Region.CanSpawn[Pos]
					PropAsset := PropAssetData.PropAsset
					Scale := Vector3One * GetRandomFloat(PropAssetData.MinScale, PropAssetData.MaxScale)
					Rotation := MakeRotationFromYawPitchRollDegrees(
						GetRandomFloat(PropAssetData.MinYaw, PropAssetData.MaxYaw).SnapToGrid(90.0), 
						GetRandomFloat(PropAssetData.MinPitch, PropAssetData.MaxPitch).SnapToGrid(90.0), 
						0.0
					)
				):
					if(PropAssetData.SpacialSpawnType = special_spawn_type.None):
						spawn. SpawnAndAddToList(PropAsset,
							transform:
								Translation := Pos.Snap96()
								Rotation := Rotation
								Scale := Scale
						)
					else if(PropAssetData.SpacialSpawnType = special_spawn_type.BlockTree):
						TrunkAsset := PropAsset
						Tr := transform:
							Translation := Pos
							Rotation := Rotation
							Scale := Scale
						spawn. SpawnTreeAddToList(TrunkAsset, Tr, PropAssetData)
		
		# LPrint("RespawnedAll")
		# LPrint("SpawnedProps.Length {SpawnedProps.Length}")
		
	
	SpawnTreeAddToList(TrunkAsset:creative_prop_asset, TrMaybeInGround:transform, PropAssetData:prop_asset_data)<suspends>:void=
		var TrMaybeInGround96 :transform= TrMaybeInGround.Snap96()
		set TrMaybeInGround96.Translation.X += 48.0
		set TrMaybeInGround96.Translation.Y += 48.0

		race:
			block:
				DespawnAllEvent.Await()
				return
			Sleep(1.0)
			
		MCastProp := PropSpawner.Spawn(D.FromTopCastPropAsset, transform:
			Translation := TrMaybeInGround96.Translation
			Rotation := rotation{}
			Scale := TrMaybeInGround96.Scale
		)

		# don't delete, use for test raycast
		# Id := GetRandomInt(0,10000)
		# if(CastProp := MCastProp?):
		# 	Tr1 := CastProp.Prop.GetTransform()
			# Print("pos before sleep {Id}: {Tr1.Translation}")

		race:
			block:
				DespawnAllEvent.Await()
				if(CastProp := MCastProp?):
					CastProp.Dispose()
				return
			Sleep(0.0)

		
		if(CastProp := MCastProp?):
			var Tr :transform= CastProp.Prop.GetTransform()
			set Tr.Translation.X -= 48.01 #.01 for texture overlap
			set Tr.Translation.Y -= 48.01
			#.Snap96()
			# Print("pos after sleep {Id}: {CastProp.Prop.GetTransform().Translation}")
			CastProp.Dispose()

			# Tr := TrMaybeInGround.Snap96()

			LeavesAsset := if(Temp := PropAssetData.SpecialPropAssets[0]). Temp else. TrunkAsset
			SpawnerData := block_tree_manager_spawner_data:
				Tr := Tr
				TrunkAsset := TrunkAsset
				LeavesAsset := LeavesAsset
				PropSpawner := PropSpawner

			Disposable := BlockTreeSpawner.SpawnRespawningTreeAsync(SpawnerData)
			set SpawnedDisposables += array. Disposable
		
	SpawnAndAddToList(PropAsset:creative_prop_asset, Tr:transform)<suspends>:void=
		MProp := PropSpawner.Spawn(PropAsset, Tr)
		if(Prop := MProp?):
			set SpawnedProps += array. Prop

spawn_map_data := class<concrete>():
	@editable Region:[]spawn_map_data_region = array{}
	@editable PropAssets:[]prop_asset_data = array{}

	Init():void=
		for(Reg:Region):
			Reg.Init()
		

spawn_map_data_region := class<concrete>():
	@editable AreaProp<private>:creative_prop = creative_prop{}
	var Area:area_box = area_box{}
	@editable DistanceBetweenSpawns:float = 1.0
	@editable ExcludedAreas:[]area_box_editable = array{}

	CanSpawn(Pos:vector3)<decides><transacts>:void=
		for(ExcludedArea:ExcludedAreas):
			not ExcludedArea.IsInside[Pos]

	Init():void=
		set Area = area_box_c_center_z(AreaProp)
		for(Areaa:ExcludedAreas):
			Areaa.Init()
		

		
	
# 	@editable SkipSpawningStart:logic = false	
# 	@editable SkipSpawningEnd:logic = false
# 	@editable SpawnMode:spawn_map_path_mode = spawn_map_path_mode.AmountToSpawn
# 	@editable AmountToSpawn:int = 1
# 	@editable DistancePerSpawnSquared:float = 100.0
# 	@editable MinScale:float = 1.0
# 	@editable MaxScale:float = 1.0


prop_asset_data := class<concrete>():
	@editable PropAsset:creative_prop_asset = DefaultCreativePropAsset
	@editable MinYaw:float = 0.0
	@editable MaxYaw:float = 360.0
	@editable MinPitch:float = 0.0
	@editable MaxPitch:float = 0.0
	@editable MinScale:float = 1.0
	@editable MaxScale:float = 1.0
	@editable OffsetZ:float = 0.0
	@editable OffsetZMin:float = 0.0
	@editable OffsetZMax:float = 0.0
	@editable ChanceToNotSpawnAtAllPct:float = 0.0
	@editable SpacialSpawnType:special_spawn_type = special_spawn_type.None
	@editable SpecialPropAssets:[]creative_prop_asset = array{}

special_spawn_type := enum:
	None
	BlockTree