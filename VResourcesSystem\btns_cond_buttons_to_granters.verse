using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 


btns_conditional_buttons_to_granters_c<public>(D:creative_device,Area:area_interface, AllowOnlyOwnerToUse:logic, ?CustomGrantAction:?type{_(:agent, :item_granter_device)<suspends>:void} = false):btns_conditional_buttons_to_granters=
	# LPrint("Getting item grnters")
	ItemGranters := D.GetDevicesInAreaWithTag(item_granter_device, Area, buttons_to_granters_tag{})
	# LPrint("Getting buttons for item granters")
	Buttons := D.GetDevicesInAreaWithTag(button_device, Area, buttons_to_granters_tag{})

	Instance := btns_conditional_buttons_to_granters:
		ItemGranters := ItemGranters
		Buttons := Buttons
		AllowOnlyOwnerToUse := AllowOnlyOwnerToUse
		CustomGrantAction := CustomGrantAction
	.Init()
	Instance

btns_conditional_buttons_to_granters<public> := class:
	ItemGranters<public>:[]item_granter_device
	Buttons<public>:[]button_device

	AllowOnlyOwnerToUse<public>:logic
	CustomGrantAction<public>:?type{_(:agent, :item_granter_device)<suspends>:void}

	var MOwnerAgent :?agent = false

	SetOwner<public>(Agent:?agent):void=
		set MOwnerAgent = Agent

	Init():btns_conditional_buttons_to_granters=
		for(Button:Buttons
			ButtonPos := Button.GetTransform().Translation
		):
			if(var ClosestGranter :item_granter_device= ItemGranters[0]):
				var ClosestDistance :float= DistanceSquared(ClosestGranter.GetTransform().Translation, ButtonPos)
				for(Granter:ItemGranters
					DistanceToGranter := DistanceSquared(Granter.GetTransform().Translation, ButtonPos)
					DistanceToGranter < ClosestDistance
				):
					set ClosestGranter = Granter
					set ClosestDistance = DistanceToGranter
					
				spawn. AwaitButtonClick(Button, ClosestGranter)
				
			else:
				LErrorPrint("ItemGranters empty")
		Self
	

	AwaitButtonClick(Button:button_device, Granter:item_granter_device)<suspends>:void=
		loop:
			Agent := Button.InteractedWithEvent.Await()

			if(not AllowOnlyOwnerToUse?):
				if(Action := CustomGrantAction?):
					Action(Agent, Granter)
				else:
					Granter.GrantItem(Agent)
			else if(Agent = MOwnerAgent?):
				if(Action := CustomGrantAction?):
					Action(Agent, Granter)
				else:
					Granter.GrantItem(Agent)


		