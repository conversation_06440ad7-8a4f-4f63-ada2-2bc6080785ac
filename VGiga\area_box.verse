using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI} 


area_box_c<public>(Prop:positional)<transacts>:area_box=
	Tr := Prop.GetTransform()
	Pos := Tr.Translation
	Scale := block:
		S := Tr.Rotation.RotateVector( Tr.Scale )
		vector3:
			X := Max(-S.X,S.X)
			Y := Max(-S.Y,S.Y)
			Z := Max(-S.Z,S.Z)

	area_box:
		MinPos := Vector3(Pos.X - Scale.X * 100.0 / 2.0,
			Pos.Y - Scale.Y * 100.0 / 2.0,
			Pos.Z - Scale.Z * 100.0 / 2.0
		)
		MaxPos := Vector3(Pos.X + Scale.X * 100.0 / 2.0,
			Pos.Y + Scale.Y * 100.0 / 2.0,
			Pos.Z + Scale.Z * 100.0 / 2.0
		)
		CenterPoint := Vector3(Pos.X, Pos.Y, Pos.Z)
		Inited := true

area_box_c_center_z<public>(Prop:positional)<transacts>:area_box=
	Tr := Prop.GetTransform()
	Pos := Tr.Translation
	# Scale := Tr.Scale
	Scale := block:
		S := Tr.Rotation.RotateVector( Tr.Scale )
		vector3:
			X := Max(-S.X,S.X)
			Y := Max(-S.Y,S.Y)
			Z := Max(-S.Z,S.Z)
	area_box:
		MinPos := Vector3(Pos.X - Scale.X * 100.0 / 2.0,
			Pos.Y - Scale.Y * 100.0 / 2.0,
			Pos.Z
		)
		MaxPos := Vector3(Pos.X + Scale.X * 100.0 / 2.0,
			Pos.Y + Scale.Y * 100.0 / 2.0,
			Pos.Z
		)
		CenterPoint := Vector3(Pos.X, Pos.Y, Pos.Z)
		Inited := true	

area_box<public> := class<concrete>(area_interface):

	var MinPos<public>:vector3 = vector3{}
	var MaxPos<public>:vector3 = vector3{}
	var CenterPoint<public>:vector3 = vector3{}
	var Inited:logic = false
	
	IsTransformInside<override>(Tr:transform)<decides><transacts>:void=
		if(not Inited?):
			LErrorPrint("ERROR NOT INITED")
		MinPos.IsSmaller[Tr.Translation]
		Tr.Translation.IsSmaller[MaxPos]

	IsInside<override>(Vec:vector3)<decides><transacts>:void=
		if(not Inited?):
			LErrorPrint("ERROR NOT INITED")
		MinPos.IsSmaller[Vec]
		Vec.IsSmaller[MaxPos]

	IsInsideOrClose<public>(Vec:vector3, CloseDistance:float)<decides><transacts>:void=
		MinPosSmaller := MinPos - Vector3One * CloseDistance
		MaxPosBigger := MinPos + Vector3One * CloseDistance
		if(not Inited?):
			LErrorPrint("ERROR NOT INITED")
		MinPosSmaller.IsSmaller[Vec]
		Vec.IsSmaller[MaxPosBigger]
		

	IsInsideLog<override>(Vec:vector3)<decides><transacts>:void=
		LPrint("MinPos: {MinPos}")
		LPrint("Vec: {Vec}")
		LPrint("MaxPos: {MaxPos}")
		IsInside[Vec]

	GetRandomPointInside<override>()<transacts>:vector3=
		if(not Inited?):
			LErrorPrint("ERROR NOT INITED")
		vector3:
			X := GetRandomFloat(MinPos.X, MaxPos.X)
			Y := GetRandomFloat(MinPos.Y, MaxPos.Y)
			Z := GetRandomFloat(MinPos.Z, MaxPos.Z)

	GetCenterPoint<override>()<transacts>:vector3=
		if(not Inited?):
			LPrint("ERROR NOT INITED")
		CenterPoint
	
	AwaitAgentInside<override>(Agent:agent, PlayerExited:event())<suspends>:void=
		if(not Inited?):
			LErrorPrint("ERROR NOT INITED")
		race:
			PlayerExited.Await()
			loop:
				if(PlayerTr := Agent.GetFortCharacterActive[].GetTransform()
					IsInside[PlayerTr.Translation]
				):
					break
				else:
					Sleep(0.5)