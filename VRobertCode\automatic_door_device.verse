
using { /Fortnite.com/Devices }
using { /Verse.org/Simulation }
using { /UnrealEngine.com/Temporary/Diagnostics }

# See https://dev.epicgames.com/documentation/en-us/uefn/create-your-own-device-in-verse for how to create a verse device.

# A Verse-authored creative device that can be placed in a level
automatic_door_device := class(creative_device):
    @editable DoorVolume : volume_device = volume_device{}
    @editable SequenceOpen : cinematic_sequence_device = cinematic_sequence_device{}
    var isDoorOpened : logic = false

    # Runs when the device is started in a running game
    OnBegin<override>()<suspends>:void=
        DoorVolume.AgentEntersEvent.Subscribe(CheckDoorStatus)
        DoorVolume.AgentExitsEvent.Subscribe(CheckDoorStatus)


    CheckDoorStatus(Agent:agent):void= 
        PlayerCountInVolume := DoorVolume.GetAgentsInVolume()
        if(PlayerCountInVolume.Length = 0 and isDoorOpened = true):
            CloseDoor()
            set isDoorOpened = false
        if(PlayerCountInVolume.Length > 0 and isDoorOpened = false):
            OpenDoor()
            set isDoorOpened = true

    OpenDoor():void= 
        SequenceOpen.Play()

    CloseDoor():void=
        SequenceOpen.PlayReverse()


    

