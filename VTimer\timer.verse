using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga

timer<public> := class(auto_creative_device): #, i_init_per_player_async):
	@editable Timer:?timer_device = false

	OnBegin<override>()<suspends>:void=
		Timer.G()

	GetTimer<public>(Agent:agent):timer_manager=
		timer_manager:
			Agent := Agent
			Timer := Timer.G()

	StartTimer<public>(Agent:agent):timer_manager=
		Timer.G().Reset(Agent)
		Timer.G().Start(Agent)
		GetTimer(Agent)
		
	# InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
	# 	LPrint("ADDING TIMER ")
		
	# 	StartTimer(Agent)
	# 	Sleep(10.0)
	# 	Timer.G().Pause(Agent)
	# 	Sleep(10.0)
	# 	StartTimer(Agent)
	# 	Sleep(10.0)
	# 	Timer.G().Complete(Agent)


timer_manager<public> := class<internal>():
	Agent:agent
	Timer:timer_device

	Restart<public>():void=
		Timer.Reset(Agent)
		Timer.Start(Agent)

	Pause<public>():float=
		Time := Timer.GetActiveDuration(Agent)
		Timer.Pause(Agent)
		Time

	Complete<public>():float=
		Time := Timer.GetActiveDuration(Agent)
		Timer.Complete(Agent)
		Time
		
		
	
	
	
