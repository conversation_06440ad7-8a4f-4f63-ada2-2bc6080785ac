using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
using. VResourcesSystem
using { /UnrealEngine.com/Assets }
using. VAudio
points_skilled_interaction := class(auto_creative_device, i_init_async):

	@editable StartButton:?button_device = false
	@editable InteractionDevice:?skilled_interaction_device = false
	@editable NextInteractionWaitTimeInSeconds:float = 10.0

	@editable MPointsPerfect:?int = false
	@editable MPointsGood:?int = false
	@editable MPointsBad:?int = false

	var Resources :?resources_manager= false
	var Audio:?audio = false
	
	InitAsync<override>(Container:vcontainer)<suspends>:void=
		VButton := StartButton.G()
		VInteractionDevice := InteractionDevice.G()
		set Resources = Container.ResolveOp[resources_manager] or Err()
		# set Audio = Container.ResolveOp[audio] or Err()
	# 	Fruits := tag_fruit{}.GetAll(Self)

	# 	for(Fruit:Fruits):
	# 		spawn. RunFruit(Fruit)
			

	# RunFruit(Fruit:button_device)<suspends>:void=
	# 	InitScale := Fruit.GetTransform().Scale
		# var ClickCounter:int = MClicksForSpecialAction? or 0
		race:
			loop:
				Agent := VButton.InteractedWithEvent.Await()
				if(VInteractionDevice.BeginInteraction[Agent]):
					VButton.Disable()
					Sleep(NextInteractionWaitTimeInSeconds)
					VButton.Enable()
			loop:
				if(Points := MPointsPerfect?):
					Ag := VInteractionDevice.PerfectInputTriggeredEvent.Await()
					Resources.G().GivePoints(Ag(0), Points)
				else:
					Sleep(Inf)
			loop:
				if(Points := MPointsGood?):
					Ag := VInteractionDevice.GoodInputTriggeredEvent.Await()
					Resources.G().GivePoints(Ag(0), Points)
				else:
					Sleep(Inf)
			loop:
				if(Points := MPointsBad?):
					Ag := VInteractionDevice.BadInputTriggeredEvent.Await()
					Resources.G().GivePoints(Ag(0), Points)
				else:
					Sleep(Inf)
			


				# if(ClicksForSpecialAction := MClicksForSpecialAction?):
				# 	set ClickCounter += 1
				# 	if(ClickCounter >= ClicksForSpecialAction):
				# 		set ClickCounter = 0
				# 		if(SpecialActionChannel := MSpecialActionChannel?):
				# 			SpecialActionChannel.Transmit(option. Agent)
				# 		if(PointsForSpecialAction := MGrantPointsForSpecialAction?):
				# 			Resources.G().GivePoints(Agent, PointsForSpecialAction)
					
			# Resources.G().GiveGoldWithMultiplier(Agent, 0.0, 0.30)
			# Resources.G().GivePoints(Agent, 1)
			# CancelFx := SpawnParticleSystem(Modele.Piggy.Blow_up_Piggy, Fruit.GetTransform().Translation)
			# if. Fruit.TeleportTo[Fruit.GetTransform().WithScale(Vector3One * 0.0001)]
			# Audio.G().GotCash.Play(Agent)
			# Fruit.Disable()
			# Sleep(GetRandomFloat(RespawnMinRandomTime, RespawnMaxRandomTime))
			# if. Fruit.TeleportTo[Fruit.GetTransform().WithScale(InitScale)]
			# CancelFx.Cancel()
			# Fruit.Enable()

		