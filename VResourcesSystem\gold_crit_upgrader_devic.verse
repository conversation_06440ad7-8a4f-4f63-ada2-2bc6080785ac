using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
using. VNotifications 

gold_crit_upgrader_devic<public> := class(stat_upgrader_devic):

	GetStatId<override>():int_stat_id = int_stat_id.GoldCrit

	GainUpgradedMsg<localizes><override>(Gain:int, Level:int):message = "Money crit chance upgraded to level: {Level}\n Crit chance: {Gain}%"
	
	GetGainValueForText<override>(Agent:agent):int=
		Resources.G().GetCritChancePct(Agent)

	UpdateSecondText<override>(Agent:agent):void=
		GainBillboard.SetText("Crit Chance ${Resources.G().GetCritChancePctNextLevel(Agent)}%")

	GetUpgradePriceForLevel<override>(Agent:agent, Level:int):int=
		(Level + 1) * 1000
