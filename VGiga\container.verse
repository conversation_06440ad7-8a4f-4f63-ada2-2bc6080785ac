using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using. VGiga
class_interface_array := class:
	var Array:[]class_interface = array{}

vcontainer<public> := class(creative_device):
	var Initailizables : []i_init = array{}
	var InitailizablesSpawn : []i_init_async = array{}
	var InitailizablePerPlayer : []i_init_per_player = array{}
	var InitailizablePerPlayerRun : []i_init_per_player_async = array{}
	var InitailizableEverytimePerPlayer : []i_init_everytime_per_player = array{}
	var InitailizableEverytimePerPlayerRun : []i_init_everytime_per_player_async = array{}
	var RemovablePerPlayer : []i_removable_per_player = array{}

	var RegisteredList:[]class_interface = array{}
	# var RegisteredListMany:[]i_register_many = array{}

	Register<public>(Obj:class_interface)<transacts>:void=
		if(Casted := i_init[Obj]):
			set Initailizables += array{Casted}
			
		if(Casted := i_init_async[Obj]):
			set InitailizablesSpawn += array{Casted}
			
		if(Casted := i_init_per_player[Obj]):
			set InitailizablePerPlayer += array{Casted}
			
		if(Casted := i_init_per_player_async[Obj]):
			set InitailizablePerPlayerRun += array{Casted}
			
		if(Casted := i_init_everytime_per_player[Obj]):
			set InitailizableEverytimePerPlayer += array{Casted}
			
		if(Casted := i_init_everytime_per_player_async[Obj]):
			set InitailizableEverytimePerPlayerRun += array{Casted}
			
		if(Casted := i_removable_per_player[Obj]):
			set RemovablePerPlayer += array{Casted}

		# if(Casted := i_register_many[Obj]):
		# 	set RegisteredListMany += array. Casted

		set RegisteredList += array{Obj}

	
	RunRemovablePerPlayer(Agent:agent)<suspends>:void=
		for(Initailizable:RemovablePerPlayer):
			Initailizable.RemovePlayer(Agent)
	
	RunInitializablesEverytimePerPlayer(Agent:agent, RemovedEventOnce:event())<suspends>:void=
		for(Initailizable:InitailizableEverytimePerPlayer):
			Initailizable.InitEverytimePlayer(Agent)
			Sleep(0.0)
			
		for(Initailizable:InitailizableEverytimePerPlayerRun):
			spawn. Initailizable.InitEverytimePlayerAsync(Agent, RemovedEventOnce)
			Sleep(0.0)

	RunInitializablesPerPlayer(Agent:agent, RemovedEventOnce:event())<suspends>:void=
		for(Initailizable:InitailizablePerPlayer):
			Initailizable.InitPlayer(Agent)
			Sleep(0.0)
			
		for(Initailizable:InitailizablePerPlayerRun):
			spawn. Initailizable.InitPlayerAsync(Agent, RemovedEventOnce)
			Sleep(0.0)


	RunInitializablesLoop()<suspends>:void=
		loop:
			InitailizableCopy := Initailizables
			set Initailizables = array{}
			for(Initailizable:InitailizableCopy):
				Initailizable.Init(Self)
				
			InitailizableSpawnCopy := InitailizablesSpawn
			set InitailizablesSpawn = array{}
			for(Initailizable:InitailizableSpawnCopy):
				spawn. Initailizable.InitAsync(Self)
			
			LPrint("RunInitializablesLoop")
			Sleep(0.0)
			if(InitailizablesSpawn.Length = 0, Initailizables.Length = 0):
				return

	ResolveAll<public>(cast_type:castable_subtype(class_interface))<decides><transacts>:[]cast_type =
		var Ret:[]cast_type = for(Obj:RegisteredList
			Casted := cast_type[Obj]
		):
			Casted
		
		if(Ret.Length > 0):
			Ret
		else:
			FailError[]
			Err()
	ResolveAllInArea<public>(cast_type:castable_subtype(class_interface), Area:area_interface)<decides><transacts>:[]cast_type =
		var Ret:[]cast_type = for(Obj:RegisteredList
			Casted := cast_type[Obj]
			Pos := positional[Obj].GetTransform().Translation
			Area.IsInside[Pos]
		):
			Casted
		
		if(Ret.Length > 0):
			Ret
		else:
			FailError[]
			Err()
	ResolveAllInAreaLog<public>(Cast:type{_(:class_interface)<decides><transacts>:t}, Area:area_interface where t:type)<decides><transacts>:[]t =
		var Ret:[]t = for(Obj:RegisteredList
			Casted := Cast[Obj]
			A := Print("Casted found")
			Pos := positional[Obj].GetTransform().Translation
			B := Print("Pos: {Pos}")
			Area.IsInsideLog[Pos]
		):
			Casted
		
		if(Ret.Length > 0):
			Ret
		else:
			FailError[]
			Err()

	ResolveAllNoFail<public>(Cast:type{_(:class_interface)<decides><transacts>:t} where t:type)<transacts>:[]t =
		for(Obj:RegisteredList
			Casted := Cast[Obj]
		):
			Casted

	ResolveNoPrint<public>(cast_type:castable_subtype(class_interface))<decides><transacts>:cast_type =
		var Ret:?cast_type = false
		N := RegisteredList.Length
		var X :int= 0 
		loop:
			if(Item := RegisteredList[X], Casted := cast_type[Item]):
				set Ret = option. Casted
				break
			
			set X += 1
			if(X >= N):
				break
		
		if(Item := Ret?):
			Item
		else:
			Fail[]
			Err()

	# ResolveNoPrint<public>(Cast:type{_(:class_interface)<decides><transacts>:t} where t:type)<decides><transacts>:t =
	# 	var Ret:?t = false
	# 	N := RegisteredList.Length
	# 	var X :int= 0 
	# 	loop:
	# 		if(Item := RegisteredList[X], Casted := Cast[Item]):
	# 			set Ret = option. Casted
	# 			break
			
	# 		set X += 1
	# 		if(X >= N):
	# 			break
		
	# 	if(Item := Ret?):
	# 		Item
	# 	else:
	# 		Fail[]
	# 		Err()

	Resolve<public>(cast_type:castable_subtype(class_interface))<decides><transacts>:cast_type =
		if(C := ResolveNoPrint[cast_type]):
			C
		else:
			FailError[]
			Err()
	ResolveOp<public>(cast_type:castable_subtype(class_interface))<decides><transacts>:?cast_type =
		if(C := ResolveNoPrint[cast_type]):
			option. C
		else:
			FailError[]
			Err()
	# ResolveOpCastable<public>(Cast:type{_(:class_interface)<decides><transacts>:t} where t:type)<decides><transacts>:?t =
	# 	if(C := ResolveNoPrint[Cast]):
	# 		option. C
	# 	else:
	# 		FailError[]
	# 		Err()

	ResolveErr<public>(cast_type:castable_subtype(class_interface))<transacts>:cast_type=
		if(C := Resolve[cast_type]):
			C
		else:
			Err()
			
	ResolveErrOp<public>(cast_type:castable_subtype(class_interface))<transacts>:?cast_type=
		if(C := Resolve[cast_type]):
			option. C
		else:
			Err()

	ResolveAllErr<public>(cast_type:castable_subtype(class_interface))<transacts>:[]cast_type =
		if(C := ResolveAll[cast_type]):
			C
		else:
			Err()
