using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
using. VNotifications 
using. VCustomBillboard 

stat_upgrader_devic<public> := class(auto_creative_device, i_init):
	@editable BuyVolumeDevice:volume_device = volume_device{}
	@editable PriceBillboard:custom_billboard_devic = custom_billboard_devic{}
	@editable GainBillboard:custom_billboard_devic = custom_billboard_devic{}
	
	var Resources:?resources_manager = false
	var Save:?save_system = false
	var Notifications:?notifications_system = false

	var Owner:?agent = false

	GetStatId():int_stat_id= 
		LErrorPrint("This should be overriden")
		Err()

	GetGainValueForText(Agent:agent):int=
		Resources.G().GetGoldGain(Agent)

	GainUpgradedMsg<localizes>(GoldGain:int, Level:int):message = "Money gain upgraded to level: {Level}\n Gain: {GoldGain}💸"
		
	Init<override>(Container:vcontainer):void=
		set Resources = option. Container.ResolveErr(resources_manager)
		set Save = option. Container.ResolveErr(save_system)
		set Notifications = option. Container.ResolveErr(notifications_system)

		spawn. HandleBuyVolumeDeviceAsync()

	InitForAgent<public>(Agent:agent):void=
		UpdateTexts(Agent)
		set Owner = option. Agent

	UpdateTexts<public>(Agent:agent):void=
		CurLevel := Save.G().GetIntStat(Agent, GetStatId())
		PriceBillboard.SetText("Cost ${GetUpgradePriceForLevel(Agent, CurLevel)}")
		UpdateSecondText(Agent)

	UpdateSecondText(Agent:agent):void=
		GainBillboard.SetText("Next Gain ${Resources.G().GetGoldGainNextLevel(Agent)}")

	HandleBuyVolumeDeviceAsync<private>()<suspends>:void=
		loop:
			Agent := BuyVolumeDevice.AgentEntersEvent.Await()
			if(Agent = Owner?):
				Level := Save.G().GetIntStat(Agent, GetStatId()) + 1
				UpgradeCost := GetUpgradePriceForLevel(Agent, Level)
				TookGold := Resources.G().TryToTakeGold(Agent, UpgradeCost)
				if(TookGold?):
					Save.G().IncreaseIntStat(Agent, GetStatId())
					UpdateTexts(Agent)
					NewGain := GetGainValueForText(Agent)
					CurLevel := Save.G().GetIntStat(Agent, GetStatId())
					Notifications.G().ShowTopNotificationWithTime(Agent, GainUpgradedMsg(NewGain, Level), 5.0)

	GetUpgradePriceForLevel(Agent:agent, Level:int):int=
		(Level + 1) * 1000
