using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 

conditional_buttons_to_granters_tag := class(tag_comparable):
	GetComparable<override>()<computes>:comparable=
		"conditional_buttons_to_granters"
	
conditional_buttons_to_granters_c<public>(D:creative_device, Area:area_interface, AllowOnlyOwnerToUse:logic, ?ActionBeforeGrant:?type{_(:agent)<suspends>:void} = false):conditional_buttons_to_granters=
	ItemGranters := D.GetDevicesInAreaWithTag(item_granter_device, Area, conditional_buttons_to_granters_tag{})
	Buttons := D.GetDevicesInAreaWithTag(conditional_button_device, Area, conditional_buttons_to_granters_tag{})
	ButtonsOriginalItemCountRequired := for(Btn:Buttons):
		Btn.GetItemCountRequired(0)

	Instance := conditional_buttons_to_granters:
		ItemGranters := ItemGranters
		Buttons := Buttons
		AllowOnlyOwnerToUse := AllowOnlyOwnerToUse
		ActionBeforeGrant := ActionBeforeGrant
		ButtonsOriginalItemCountRequired := ButtonsOriginalItemCountRequired
	.Init()
	Instance

conditional_buttons_to_granters<public> := class:
	ItemGranters<public>:[]item_granter_device
	Buttons<public>:[]conditional_button_device
	ButtonsOriginalItemCountRequired<public>:[]int

	AllowOnlyOwnerToUse<public>:logic
	ActionBeforeGrant<public>:?type{_(:agent)<suspends>:void}

	var MOwnerAgent :?agent = false
	var IgnoreActivate<public>:logic = false

	SetOwner<public>(Agent:?agent):void=
		set MOwnerAgent = Agent

	Init():conditional_buttons_to_granters=
		for(Button:Buttons
			ButtonPos := Button.GetTransform().Translation
		):
			if(var ClosestGranter :item_granter_device= ItemGranters[0]
				var ClosestDistance :float= DistanceSquared(ClosestGranter.GetTransform().Translation, ButtonPos)
			):
				for(Granter:ItemGranters
					DistanceToGranter :float= DistanceSquared(Granter.GetTransform().Translation, ButtonPos)
					DistanceToGranter < ClosestDistance
				):
					set ClosestGranter = Granter
					set ClosestDistance = DistanceToGranter
				
				spawn. AwaitButtonClick(Button, ClosestGranter)
			else. LError()
		Self
	

	AwaitButtonClick(Button:conditional_button_device, Granter:item_granter_device)<suspends>:void=
		OriginalCount := Button.GetItemCountRequired(0)
		Button.SetItemCountRequired(0, 0)
		loop:
			Agent := Button.ActivatedEvent.Await()
			if(not IgnoreActivate?):
				if(Action := ActionBeforeGrant?):
					Action(Agent)

				if(not AllowOnlyOwnerToUse?):
					Granter.GrantItem(Agent)
				else if(Agent = MOwnerAgent?):
					Granter.GrantItem(Agent)


		