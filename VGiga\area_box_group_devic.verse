using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI} 

area_box_group_devic<public> := class(creative_device, area_interface):
	@editable Areas:[]area_box_devic = array{}
	Group:area_group = area_group{}

	OnBegin<override>()<suspends>:void=
		set Group.Areas = for(A:Areas). A
	
	IsTransformInside<override>(Tr:transform)<decides><transacts>:void=
		Group.IsTransformInside[Tr]
	
	IsInside<override>(Vec:vector3)<decides><transacts>:void=
		Group.IsInside[Vec]
	
	IsInsideLog<override>(Vec:vector3)<decides><transacts>:void=
		Group.IsInsideLog[Vec]

	GetRandomPointInside<override>()<transacts>:vector3=
		Group.GetRandomPointInside()

	AwaitAgentInside<override>(Agent:agent, PlayerExited:event())<suspends>:void=
		Group.AwaitAgentInside(Agent,PlayerExited)

	GetCenterPoint<override>()<transacts>:vector3=
		Group.GetRandomPointInside()

