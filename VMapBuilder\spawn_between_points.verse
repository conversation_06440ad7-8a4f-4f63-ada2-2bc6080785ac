using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using.  VGiga 
using. VPropSpawner

spawn_between_points_devic<public> := class(creative_device):
	@editable Datas:[]spawn_between_points_data = array{}

	Init<public>(Container:player_events_manager_devic,
		PropSpawner:prop_spawner,
		SpawnAtStart:logic
	):spawn_between_points=
		Manager := spawn_between_points:
			D := Self
			PropSpawner := PropSpawner
		.Init(SpawnAtStart)
		Manager


spawn_between_points<public> := class():
	D<public>:spawn_between_points_devic
	PropSpawner<public>:prop_spawner
	var SpawnedProps:[]creative_prop_unique = array{}

	Init<public>(SpawnAtStart:logic):spawn_between_points=
		if(SpawnAtStart?):
			RespawnAll()
		Self

	DespawnAll<public>():void=
		for(Prop:SpawnedProps):
			Prop.Dispose()

	RespawnAll<public>():void=
		for(Data:D.Datas
			Path:Data.Paths
			StartPos := Path.StartProp.GetTransform().Translation
			EndPos := Path.EndProp.GetTransform().Translation
			ToEndPos := -StartPos + EndPos
			AmountToSpawn := 
				if(Path.SpawnMode = spawn_between_points_path_mode.DistancePerSpawn):
					Floor[ToEndPos.LengthSquared() / Path.DistancePerSpawnSquared]
				else:
					Path.AmountToSpawn
			ToEndPosDividedVec := ToEndPos.Divide(AmountToSpawn.F())
			StartId := if(Path.SkipSpawningStart?). 1 else. 0
			EndId := AmountToSpawn- if(Path.SkipSpawningEnd?). 2 else. 1
			X:=StartId..EndId
			PropAsset := Data.PropsToSpawn.GetRandom[]
			Pos2 := if(Data.Snap96Grid?):
				(StartPos + (ToEndPosDividedVec).Mul(X.F())).Snap96()
			else:
				StartPos + (ToEndPosDividedVec).Mul(X.F())

			Pos := vector3:
				X := Pos2.X
				Y := Pos2.Y
				Z := (Pos2.Z + GetRandomFloat(Data.MinZOffset, Data.MaxZOffset)).SnapToGrid(96.0)

			Scale := Vector3One * GetRandomFloat(Path.MinScale, Path.MaxScale)
			Rotation := if(Data.Angles90Only?):
				Y := Round[GetRandomFloat(0.0,360.0) / 90.0] * 90.0
				MakeRotationFromYawPitchRollDegrees(Y, 0.0, 0.0)
			else:
				MakeRotationFromYawPitchRollDegrees(GetRandomFloat(0.0,360.0), 0.0, 0.0)
		):
			spawn. SpawnAndAddToList(PropAsset,
				transform:
					Translation := Pos
					Rotation := Rotation
					Scale := Scale
			)

		
	SpawnAndAddToList(PropAsset:creative_prop_asset, Tr:transform)<suspends>:void=
		MProp := PropSpawner.Spawn(PropAsset, Tr)
		if(Prop := MProp?):
			set SpawnedProps = array. Prop

		


spawn_between_points_data := class<concrete>():
	@editable Paths:[]spawn_between_points_data_path = array{}
	@editable PropsToSpawn:[]creative_prop_asset = array{}
	@editable Angles90Only:logic = false
	@editable Snap96Grid:logic = false
	@editable MinZOffset:float = 0.0
	@editable MaxZOffset:float = 0.0

spawn_between_points_data_path := class<concrete>():
	@editable StartProp:creative_prop = creative_prop{}
	@editable EndProp:creative_prop = creative_prop{}
	@editable SkipSpawningStart:logic = false	
	@editable SkipSpawningEnd:logic = false
	@editable SpawnMode:spawn_between_points_path_mode = spawn_between_points_path_mode.AmountToSpawn
	@editable AmountToSpawn:int = 1
	@editable DistancePerSpawnSquared:float = 100.0
	@editable MinScale:float = 1.0
	@editable MaxScale:float = 1.0


spawn_between_points_path_mode := enum:
	AmountToSpawn
	DistancePerSpawn

	
	
	