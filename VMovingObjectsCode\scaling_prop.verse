using { /Fortnite.com/Devices }

using { /Fortnite.com/Devices/CreativeAnimation }

using { /Verse.org/Simulation }

using { /UnrealEngine.com/Temporary/SpatialMath }

 

MatchScaleTargetTip<localizes>:message = "The optional position to move to World Space. Use this if you do not want to set a MoveTarget."

 

# A prop that scales toward either a given scale or a Creative prop's scale.

scaling_prop<public> := class<concrete>(movable_prop):

    # The array of vector3 targets for the RootProp to scale to.

    @editable {ToolTip := MoveTargetsTip}

    var ScaleTargets:[]vector3= array{}

 

    # The optional Creative prop for the RootProp to match scale to.

    @editable {ToolTip := MatchScaleTargetTip}

    var MatchScaleTarget:?creative_prop = false

 

    # The scale the prop is currently targeting.

    var TargetScale:vector3 = vector3{}

 

    # Scale the RootProp toward the ScaleTarget, or MatchScaleTarget if one is set.

    Move<override>()<suspends>:void=

        # Set the TargetScale to the MatchScaleTarget if it is set.

        if:

            ScaleToMatch := MatchScaleTarget?.GetTransform().Scale

        then:

            set TargetScale = ScaleToMatch

 

            # Call MoveToEase to start scaling the prop. The OneShot animation mode will play the animation once.

            RootProp.MoveToEase(MoveDuration, TargetScale, MoveEaseType, animation_mode.OneShot)

        else:

            # Otherwise, scale to each target in the ScaleTargets array.

            for:

                ScaleTarget:ScaleTargets

            do:

                set TargetScale = ScaleTarget

 

                # Call MoveToEase to start scaling the prop. The OneShot animation mode will play the animation once.

                RootProp.MoveToEase(MoveDuration, TargetScale, MoveEaseType, animation_mode.OneShot)