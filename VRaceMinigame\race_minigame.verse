using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
using. VNotifications
using. VResourcesSystem
using. VTimer
using. VMinigames

race_minigame<public> := class(auto_creative_device, i_init_per_player_async, i_init):

	@editable MHudMessageActiveCoinsValue:?hud_message_device = false
	@editable RaceManager:?race_manager_device = false

	@editable var CarSpawners:[]vehicle_spawner_rocketracing_device = array{}
	@editable var CarSpawnersTriggersAssignDriverFix:[]channel_device = array{}

	@editable FirstCheckpoint:?race_checkpoint_device = false
	@editable LastCheckpoint:?race_checkpoint_device = false
	@editable LastCheckpointTeleporter:?teleporter_device = false
	var Timer :?timer= false

	var Tracks:[]race_track = array{}
	var Notifications:?notifications_system = false
	var Resources:?resources_manager = false
	var PlayerEvents:?player_events_manager_devic = false
	var MinigameResult :?minigame_result= false
	var Rewards :?gm_balance_rewards= false

	AgentReachedCheckpointEv:event(agent)= event(agent){}
	AgentCollectedCoinEv:event(agent)= event(agent){}
	AgentFinishedCarRaceEv<public>:event(agent)= event(agent){}
	AgentFinishedParkourEv<public>:event(agent)= event(agent){}
	AgentFinishedEv<public>:event(agent)= event(agent){}

	var Loc :i_localization= empty_i_localization{}

	var Coins :[]collectible_object_device= array{}

	WaitForAnyTrack(Id:int, Agent:agent)<suspends>:race_track=
		if(Tracks.Length = 0):
			LError()
			Sleep(Inf)

		if(Track := Tracks[Id]):
			race:
				block:
					Track.StartChannel.G().ReceivedTransmitEvent.AwaitForOption(Agent)
					Track
				WaitForAnyTrack(Id + 1, Agent)
		else:
			Sleep(Inf)
			Err()
		

	Init<override>(Container:vcontainer):void=
		set Rewards = Container.ResolveOp[gm_balance_rewards] or Err()
		set MinigameResult = Container.ResolveOp[minigame_result] or Err()
		set Timer = Container.ResolveOp[timer] or Err()
		set Loc = Container.Resolve_i_localization()
		set Tracks = race_track_tag{}.GetAll(Self)
		set Resources = Container.ResolveErrOp(resources_manager)
		set PlayerEvents = Container.ResolveErrOp(player_events_manager_devic)
		
		set Notifications = Container.ResolveErrOp(notifications_system)
		for(X:= 0..CarSpawners.Length-1
			Spawner := CarSpawners[X]
		):
			Spawner.DestroyVehicle()

		for(Checkpoint:race_checkpoint_device_tag{}.GetAll(Self)):
			spawn. OnCheckpointReached(Checkpoint)

		set Coins = race_collectible_device_tag{}.GetAll(Self)
		for(Coin:Coins):
			spawn. OnCoinCollected(Coin)
	
	OnCoinCollected(Checkpoint:collectible_object_device)<suspends>:void=
		loop:
			Agent := Checkpoint.CollectedEvent.Await()
			AgentCollectedCoinEv.Signal(Agent)

	OnCheckpointReached(Checkpoint:race_checkpoint_device)<suspends>:void=
		loop:
			Agent := Checkpoint.CheckpointCompletedEvent.Await()
			AgentReachedCheckpointEv.Signal(Agent)

	HandleCoinRewards(Agent:agent, MTrack:?race_track)<suspends>:void=
		AgentCollectedCoinEv.AwaitFor(Agent)
		if(Track := MTrack?, RewardGold := Track.MGoldRewardPerCoinTimeSec?.IntToFloat()):
			RatioReward := Rewards.G().RaceParkourRatioCoin
			Resources.G().GiveGoldWithMultiplier(Agent, RewardGold, RatioReward, ?ShowNotification := true)
		# else:
		# 	# coins from jumping heli at start
		# 	Resources.G().GivePoints(Agent, 10.0)

	HandleCheckpointRewards(Agent:agent)<suspends>:void=
		loop:
			AgentReachedCheckpointEv.AwaitFor(Agent)
			# Resources.G().GiveGoldWithMultiplier(Agent, SecondsToFinishRaceCheckpoint, CheckpointRewardTimeMutli, ?ShowNotification := true)
		
	HandlePlayerExitingVehicle(Agent:agent, CarSpawner:vehicle_spawner_device)<suspends>:logic=
		loop:
			CarSpawner.AgentExitsVehicleEvent.AwaitFor(Agent)
			DidGoBackInTime :logic= race:
				block:
					var TimeLeftToEnterVehicle :int= 10
					loop:
						Notifications.G().ShowTopNotificationWithTime(Agent, ToMsg("{Loc.G(Agent, "Go back to the car!")} {TimeLeftToEnterVehicle}"), 0.9)
						Sleep(1.0)
						set TimeLeftToEnterVehicle -= 1
						if(TimeLeftToEnterVehicle <= 0):
							break
					false
				block:
					CarSpawner.AgentEntersVehicleEvent.AwaitFor(Agent)
					true
			if(not DidGoBackInTime?):
				return true  

	HandleCheckpointsTrack(Agent:agent, UsesCheckpoints:logic, TrackType:track_type, CarSpawner:vehicle_spawner_device)<suspends>:logic=
		if(not UsesCheckpoints?):
			Sleep(Inf)

		LPrint("HandleCheckpointsTrack")

		race:
			loop:
				HandleCheckpointRewards(Agent)
			block:
				Sleep(1.0)
				FirstCheckpoint.G().SetAsCurrentCheckpoint(Agent)
				if(TrackType = track_type.Car):
					RunFixToEndCheckpoints := HandlePlayerExitingVehicle(Agent, CarSpawner)
					return RunFixToEndCheckpoints
				else:
					Sleep(Inf)
		return false
		
	OnCoinCollected(Agent:agent, Coin:collectible_object_device, CancelEv:event())<suspends>:void=
		race:
			CancelEv.Await()
			loop:
				Coin.CollectedEvent.AwaitFor(Agent)

	RunGlobalTrack(Track:race_track)<suspends>:void=
		Track.GlobalTrackRunInProgress.Set(true)
		for(ObjectToMove:Track.ObjectsToMoveOnStart):
			ObjectToMove.Reset()
		Sleep(0.0)
		for(ObjectToMove:Track.ObjectsToMoveOnStart):
			ObjectToMove.Start()

		# if(StartCountDown := Track.MStartCountDown?):
		# 	if(Timerr := Track.MStartCountDownTimer?):
		# 		Timerr.SetMaxDuration(StartCountDown)
		# 		Timerr.SetActiveDuration(StartCountDown)
		# 		Timerr.ResetForAll()
		# 		Timerr.StartForAll()
			
		# 		Sleep(StartCountDown)



		# race:
			block:
				if(Limit := Track.MTimeLimit?):
					Sleep(Limit)
				else:
					LErrorPrint("TIME LIMIT REQUIRED FOR GLOBAL TRACK TO END")
					Sleep(Inf)
			# block:
			# 	if(Volume := Track.MGlobalTrack_EndMinigameNoPlayersInVolume?):
			# 		loop:
			# 			Volume.AgentExitsEvent.Await()
			# 			if(Volume.GetAgentsInVolume().Length = 0):
			# 				break
			# 	else:
			# 		Sleep(Inf)
			
		if(Volume := Track.MGlobalTrack_EndMinigameNoPlayersInVolume?):
			for(Agent:Volume.GetAgentsInVolume(), Char := Agent.GetFortCharacterActive[]):
				Char.Damage(500.0)
		else:
			LPrint("This minigame is missing end minigame volume to eliminate players")
		
		
		for(ObjectToMove:Track.ObjectsToMoveOnStart):
			ObjectToMove.Reset()
		Track.GlobalTrackRunInProgress.Set(false)

		
	InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
		Print("race_minigame InitPlayerAsync"); Sleep(0.0)
		CarSpawner := CarSpawners[0] or Err()
		AssignDriver :=  CarSpawnersTriggersAssignDriverFix[0] or Err()
		if:
			NewSpawners := CarSpawners.RemoveElement[0]
			set CarSpawners = NewSpawners
			NewDrivers := CarSpawnersTriggersAssignDriverFix.RemoveElement[0]
			set CarSpawnersTriggersAssignDriverFix = NewDrivers

		# Stopwatch := Timer.G().GetTimer(Agent)
		# Track := RaceTracks[GetRandomInt(0, RaceTracks.Length - 1)] or Err()
		var MCoinsBarValue :?int= false

		CoinsUi := make_points_canvas_generated()

		var ActiveTrack:?race_track = false

		race:
			PlayerRemoved.Await()
			loop:
				HandleCoinRewards(Agent, ActiveTrack)

				Rebirths := Resources.G().GetRebirths(Agent)
				Reward := 10 + Rebirths
			
				Resources.G().GivePoints(Agent, Reward, ?OnlyShowPopup:=true)

				if(CurValue := MCoinsBarValue? + Reward):
					set MCoinsBarValue = option. CurValue
					if(HudDevice := MHudMessageActiveCoinsValue?):
						HudDevice.Show(Agent, ToMsg(CurValue))
					else:
						CoinsUi.PointsValue.SetText("{CurValue}".ToMessage())

			loop:
				set ActiveTrack = false
				Track := WaitForAnyTrack(0, Agent)
				set ActiveTrack = option. Track
				if(Track.GlobalTrackRunInProgress.Get()?):
					LErrorPrint("ACTOR TRYING TO JOIN GLOBAL TRACK THAT IS ALREADY IN PROGRESS")
				if(not Track.GlobalTrackRunInProgress.Get()?):
					TrackType := Track.TrackType

					UsesCheckpoints := (TrackType = track_type.Car and true) or (TrackType = track_type.WalkCheckpoints and true) or false

					set MCoinsBarValue = Track.MUseCoinsProgressBarPointsInitialValue
					if(CoinsVal := MCoinsBarValue?):
						if(HudDevice := MHudMessageActiveCoinsValue?):
							HudDevice.Show(Agent, ToMsg(CoinsVal))
						else:
							Agent.AddToPlayerUi(CoinsUi.PointsCanvas)

					for(Coin:Coins):
						Coin.Respawn(Agent)

					if(TrackType = track_type.Car):
						CarSpawner.DestroyVehicle()
						if(StartTeleport := Track.MStartTeleport?):
							if. CarSpawner.TeleportTo[StartTeleport.GetTransform()]
							Sleep(0.0)
						CarSpawner.RespawnVehicle()
						Sleep(0.1)
						AssignDriver.Transmit(option. Agent)
					else if(StartTeleport := Track.MStartTeleport?):
						StartTeleport.Teleport(Agent)
					# CarSpawner.AssignDriver(Agent)
					# StartTime := GetSimulationElapsedTime()
					
					var RunFixToEndCheckpoints :logic= false


					StartGlobalTrack := (Track.GlobalTrackForAllPlayers? and not Track.GlobalTrackRunInProgress.Get()?) or false
					if(StartGlobalTrack?):
						spawn. RunGlobalTrack(Track)

					if(not Track.GlobalTrackForAllPlayers?):
						for(ObjectToMove:Track.ObjectsToMoveOnStart):
							ObjectToMove.Start()

					Print("Race race")
					race:
						if(Interval := Track.MDecreaseCoinsOverTimeInterval?):
							loop:
								if(NewVal := Max(0, MCoinsBarValue? - 1)):
									set MCoinsBarValue = option. NewVal
									if(HudDevice := MHudMessageActiveCoinsValue?):
										HudDevice.Show(Agent, ToMsg(NewVal))
									else:
										CoinsUi.PointsValue.SetText("{NewVal}".ToMessage())
									Sleep(Interval)
								else:
									Sleep(Inf)
						else:
							# Stopwatch.Restart()
							Sleep(Inf)
						loop:
							if(GateToStartTimer := Track.MGateToStartTimer?):
								GateToStartTimer.AgentEntersEvent.AwaitFor(Agent)
								# Stopwatch.Restart()
							else:
								# Stopwatch.Restart()
								Sleep(Inf)
						loop:
							if(StartTeleport := Track.MStartTeleport?):
								StartTeleport.TeleportedEvent.AwaitFor(Agent)
								if(GateToStartTimer := Track.MGateToStartTimer?):
									# Stopwatch.Pause()
								for(Coin:Coins):
									Coin.Respawn(Agent)
							else:
								Sleep(Inf)
						set RunFixToEndCheckpoints = HandleCheckpointsTrack(Agent, UsesCheckpoints, TrackType, CarSpawner)
						block:
							if(ExitVolume := Track.MExitVolume?):
								ExitVolume.AgentEntersEvent.AwaitFor(Agent)
								Print("ExitVolume ended")
							else:
								Sleep(Inf)
						block:
							if(ExitVolume := Track.MExitVolume2?):
								ExitVolume.AgentEntersEvent.AwaitFor(Agent)
								Print("ExitVolume ended")
							else:
								Sleep(Inf)
						block:
							PlayerEvents.G().PlayerEliminatedCharacter.AwaitFor(Agent)
							Sleep(5.0)
						block:
							Track.StartChannel.G().ReceivedTransmitEvent.AwaitForOption(Agent)
							Print("another start received to reset")
							# another start will cancel the race (aka when going back to start)
						block:
							if(UsesCheckpoints?):
								RaceManager.G().RaceCompletedEvent.AwaitFor(Agent)
								AgentFinishedCarRaceEv.Signal(Agent)
							else if(WinVolume := Track.MWinVolume?):
								WinVolume.AgentEntersEvent.AwaitFor(Agent)

								if(TrackType = track_type.Walk):
									AgentFinishedParkourEv.Signal(Agent)
							else:
								LError()

							if(ValueWon := MCoinsBarValue?):
								Resources.G().GivePoints(Agent, ValueWon)
								MinigameResult.G().ShowResultHud(Agent, ValueWon)

							# TotalTime := Stopwatch.Complete()
							# EndTime := GetSimulationElapsedTime()
							# TotalTime := EndTime - StartTime
							# LPrint("Race Total Time: {TotalTime}")

					Print("Race race ended")

					if(RunFixToEndCheckpoints?):
						LastCheckpoint.G().SetAsCurrentCheckpoint(Agent)
						Sleep(0.1)
						LastCheckpointTeleporter.G().Teleport(Agent)
						Sleep(0.1)

					if(Reward := Track.MEstimatedSecondsToFinishForReward?.IntToFloat()):
						RatioReward := Rewards.G().RaceParkourRatio
						Resources.G().GiveGoldWithMultiplier(Agent, Reward, RatioReward, ?ShowNotification := true)

					CarSpawner.DestroyVehicle()
					Sleep(0.1)
					if(ExitTeleport := Track.MExitTeleport?):
						ExitTeleport.Teleport(Agent)

					if(HudDevice := MHudMessageActiveCoinsValue?):
						HudDevice.Hide(Agent)
					else:
						Agent.RemoveFromPlayerUi(CoinsUi.PointsCanvas)

					
					if(not Track.GlobalTrackForAllPlayers?):
						for(ObjectToMove:Track.ObjectsToMoveOnStart):
							ObjectToMove.Start()

					# Stopwatch.Complete()
					Print("Race Finished")
					
					#used by gm to tp to house
					AgentFinishedEv.Signal(Agent)

		
		Agent.RemoveFromPlayerUi(CoinsUi.PointsCanvas)
		set CarSpawners += array. CarSpawner
		set CarSpawnersTriggersAssignDriverFix += array. AssignDriver


