using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI} 


map_agent_logic<public> := class:
	var DataMap:[agent]logic = map{}
	var MDataSetEvent:?event(tuple(agent, logic)) = false

	Get<public>(Key:agent)<decides><transacts>:void=
		DataMap[Key]

	GetErr<public>(Key:agent)<decides><transacts>:void=
		if(Data := DataMap[Key]):
			Data
		else:
			FailError[]
			Err()

	GetMap<public>()<transacts>:[agent]logic=
		DataMap

	GetOrAwait<public>(Key:agent)<suspends>:logic=
		if(Data := DataMap[Key]):
			Data
		else:
			var Return:?logic = false
			if(DataSetEvent := MDataSetEvent?):
				loop:
					Data := DataSetEvent.Await()
					if(Data(0) = Key):
						set Return = option. Data(1)
						break
				# DataSetEvent.AwaitForData(Key)
			else:
				Ev := event(tuple(agent, logic)){}
				set MDataSetEvent = option. Ev
				# Ev.AwaitForData(Key)
				loop:
					Data := Ev.Await()
					if(Data(0) = Key):
						set Return = option. Data(1)
						break
			Return.G()
			

	Set<public>(Key:agent, Data:logic):void=
		if. set DataMap[Key] = Data
		if(DataSetEvent := MDataSetEvent?):
			DataSetEvent.Signal((Key, Data))

	Remove<public>(Key:agent)<transacts>:void=
		set DataMap = DataMap.WithRemoved(Key)
		
	Clear<public>()<transacts>:void=
		set DataMap = map{}

#gen
#map_t1_t2
#agent
#int
#id-edf26d1e-51a5-4e14-9c51-279da34d6851
map_agent_int<public> := class:
	var DataMap:[agent]int = map{}
	var MDataSetEvent:?event(tuple(agent, int)) = false

	Get<public>(Key:agent)<decides><transacts>:int=
		DataMap[Key]

	GetErr<public>(Key:agent)<decides><transacts>:int=
		if(Data := DataMap[Key]):
			Data
		else:
			FailError[]
			Err()

	GetMap<public>()<transacts>:[agent]int=
		DataMap

	GetOrAwait<public>(Key:agent)<suspends>:int=
		if(Data := DataMap[Key]):
			Data
		else:
			if(DataSetEvent := MDataSetEvent?):
				var Return:?int = false
				loop:
					Data := DataSetEvent.Await()
					if(Data(0) = Key):
						set Return = option. Data(1)
						break
				Return.G()
				# DataSetEvent.AwaitForData(Key)
			else:
				Ev := event(tuple(agent, int)){}
				set MDataSetEvent = option. Ev
				# Ev.AwaitForData(Key)
				var Return:?int = false
				loop:
					Data := Ev.Await()
					if(Data(0) = Key):
						set Return = option. Data(1)
						break
				Return.G()
			

	Set<public>(Key:agent, Data:int):void=
		if. set DataMap[Key] = Data
		if(DataSetEvent := MDataSetEvent?):
			DataSetEvent.Signal((Key, Data))

	Remove<public>(Key:agent)<transacts>:void=
		set DataMap = DataMap.WithRemoved(Key)
#id-edf26d1e-51a5-4e14-9c51-279da34d6851

#gen
#map_t1_t2
#agent
#string
#id-de5e0ca8-4fa5-4c9c-8d8f-1b434d278e86
map_agent_string<public> := class:
	var DataMap:[agent]string = map{}
	var MDataSetEvent:?event(tuple(agent, string)) = false

	Get<public>(Key:agent)<decides><transacts>:string=
		DataMap[Key]

	GetErr<public>(Key:agent)<decides><transacts>:string=
		if(Data := DataMap[Key]):
			Data
		else:
			FailError[]
			Err()

	GetMap<public>()<transacts>:[agent]string=
		DataMap

	GetOrAwait<public>(Key:agent)<suspends>:string=
		if(Data := DataMap[Key]):
			Data
		else:
			if(DataSetEvent := MDataSetEvent?):
				var Return:?string = false
				loop:
					Data := DataSetEvent.Await()
					if(Data(0) = Key):
						set Return = option. Data(1)
						break
				Return.G()
				# DataSetEvent.AwaitForData(Key)
			else:
				Ev := event(tuple(agent, string)){}
				set MDataSetEvent = option. Ev
				# Ev.AwaitForData(Key)
				var Return:?string = false
				loop:
					Data := Ev.Await()
					if(Data(0) = Key):
						set Return = option. Data(1)
						break
				Return.G()
			

	Set<public>(Key:agent, Data:string):void=
		if. set DataMap[Key] = Data
		if(DataSetEvent := MDataSetEvent?):
			DataSetEvent.Signal((Key, Data))

	Remove<public>(Key:agent)<transacts>:void=
		set DataMap = DataMap.WithRemoved(Key)
#id-de5e0ca8-4fa5-4c9c-8d8f-1b434d278e86

# map_agent_t<public>(t:type) := class{
# 	DataMap<public> : [agent]t = map{}
# 	DataCreatedEvent:event(tuple(agent, t)) = event(tuple(agent, t)){}

# 	Get<public>(Player:agent)<decides><transacts>:t={
# 		if(Data := DataMap[Player]){
# 			Data
# 		}else{
# 			FailError[]
# 			Err()
# 		}
# 	}

# 	GetOrAwait<public>(Player:agent)<suspends>:t={
# 		if(Data := DataMap[Player]){
# 			Data
# 		}else{
# 			DataCreatedEvent.AwaitForData(Player)
# 		}
# 	}


# 	Add<public>(Player:agent, Data:t):map_agent_t(t)={
# 		Created := map_agent_t(t){
# 			DataMap := ConcatenateMaps(DataMap, map{Player => Data})
# 			DataCreatedEvent := DataCreatedEvent
# 		}
# 		DataCreatedEvent.Signal((Player, Data))
# 		Created
# 	}

# 	Remove<public>(Player:agent)<transacts>:map_agent_t(t)={
# 		map_agent_t(t){
# 			DataMap := DataMap.WithRemoved(Player)
# 			DataCreatedEvent := DataCreatedEvent
# 		}
# 	}
# }

# map_agent_t2<public>(t:type) := class{
# 	DataMap<public> : [agent]t = map{}
# 	DataCreatedEvent:event(tuple(agent, t)) = event(tuple(agent, t)){}
# }

# 	Get<public>(Player:agent)<decides><transacts>:t={
# 		if(Data := DataMap[Player]){
# 			Data
# 		}else{
# 			FailError[]
# 			Err()
# 		}
# 	}
# }



# X := type {_X:subtype(my_type_t2)}

# my_type_t<public>(t:type) := class{
# 	MyType<public>:t
# }

# my_type_t2<public> := class(my_type_t(my_type_t2)){
	
# }

# my_class<public> := class (){
# 	var Value:int = 0
# }

# main<public> := class{
# 	Instance :my_type_t(my_class) = my_type_t(my_class){
# 		MyType := my_class{}
# 	}

# 	Start<public>():void={
# 		set Instance.MyType.Value = 1
# 		Print("{Instance.MyType.Value}")
# 		set Instance.MyType.Value = 4
# 		Print("{Instance.MyType.Value}")
# 	}
# }