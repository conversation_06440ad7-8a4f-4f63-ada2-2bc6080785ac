using { /Fortnite.com/AI }
using { /Fortnite.com/Characters }
using { /Fortnite.com/Devices }
using { /Fortnite.com/Devices/CreativeAnimation }
using { /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes }
using { /Fortnite.com/FortPlayerUtilities }
using { /Fortnite.com/Game }
using { /Fortnite.com/Playspaces }
using { /Fortnite.com/Teams }
using { /Fortnite.com/UI }
using { /Fortnite.com/Vehicles }
using { /UnrealEngine.com/Temporary }
using { /UnrealEngine.com/Temporary/Curves }
using { /UnrealEngine.com/Temporary/Diagnostics }
using { /UnrealEngine.com/Temporary/SpatialMath }
using { /UnrealEngine.com/Temporary/UI }
using { /Verse.org/Assets }
using { /Verse.org/Colors }
using { /Verse.org/Colors/NamedColors }
using { /Verse.org/Concurrency }
using { /Verse.org/Native }
using { /Verse.org/Random }
using { /Verse.org/Simulation }
using { /Verse.org/Simulation/Tags }
using { /Verse.org/Verse }
using { VArrowSystem }
using { VChestsSystem }
using { VCinematic }
using { VCreaturesSystem }
using { VCustomBillboard }
using { VGiga }
using { VGiga.Pool }
using { VLocalization }
using { VNotifications }
using { VPopup }
using { VQuestSystem }
using { VResourcesSystem }
using. VMinigames

house_manager_devic<public> := class(creative_device):
    @editable ClaimTriggerParent:creative_prop = creative_prop{}
    @editable ClaimTrigger:trigger_device = trigger_device{}
    @editable RebirthTriggerParent:creative_prop = creative_prop{}
    @editable RebirthTrigger:trigger_device = trigger_device{}
    @editable Unlockables:[]house_unlockable = array{}
    @editable HideOnUnlocked:[]hide_on_unlock = array{}
    @editable HouseTeleport<public>:teleporter_device = teleporter_device{}
    @editable HouseTeleportEntryMinigame<public>:teleporter_device = teleporter_device{}
    # @editable Area:area_box_devic = area_box_devic{}

    @editable PcTrigger:trigger_device = trigger_device{}
    @editable MAppInvestment:?app_investment = false

    # @editable CameraSequenceParent:creative_prop = creative_prop{}
    # @editable CameraSequence:cinematic_sequence_device = cinematic_sequence_device{}
    # @editable ShirtCreatorsManager:minigames_shirt_manager_devic = minigames_shirt_manager_devic{}

    # @editable PlayerReference:player_reference_device = player_reference_device{}
    @editable PlayerCheckPoint<public>:player_checkpoint_device = player_checkpoint_device{}
    @editable PopupSellHouse:popup_dialog_device = popup_dialog_device{}
    # @editable Area:area_box_devic = area_box_devic{}
    @editable AreaProp:area_box_editable = area_box_editable{}
    @editable MGoldGainUpgrader:?gold_gain_upgrader_devic = false
    @editable MGoldCritUpgrader:?gold_crit_upgrader_devic = false
    @editable MGoldCritAmountUpgrader:?gold_crit_amount_upgrader_devic = false

    @editable MUnlocksCounter:?house_unlocks_counter = false
    
    # @editable MSkillsUpgrader:?skills_upgrader_devic = false
    # @editable MarketingMutator:mutator_zone_device = mutator_zone_device{}
    var MButtonToGranters:?btns_conditional_buttons_to_granters = false
    # var ConButtonsForInvRemoval:[]conditional_button_device = array{}


    OnBegin<override>()<suspends>:void=
        Sleep(0.0)
        AreaProp.Init()
    
    Init(A:house_manager_args):house_manager=
        var UnlockablesMap : [string]house_unlockable_manager = map{}
        HouseResetEvent:event() = event(){}
        OwnerBarriers := GetDevicesInArea(barrier_device, AreaProp)
        ButtonToGranters := btns_conditional_buttons_to_granters_c(Self, AreaProp, true, ?CustomGrantAction := option. TakeWeaponIfFull)
        # LPrint("ButtonToGranters.Length: {ButtonToGranters.Buttons.Length}")
        set MButtonToGranters = option. ButtonToGranters


        # THIS HAS TO BE LAST
        UnlockableManagers := for(U:Unlockables):
            UManager := U.Init(A.Balance.Presets, A.CustomBillboardSpawner, A.ResourceManager, A.TriggersPool, HouseResetEvent, A.Loc, A.ArrowSystem, A.Notifs)
            if. set UnlockablesMap[U.Id] = UManager
            UManager

        for(Id->UnlockableOptions:A.Balance.Presets.Map):
            if(not UnlockablesMap[Id]):
                LPrint("Possible error: Unlockable Id: {Id} not found in house manager editables")

        ShowEndTimeTriggers := for(Trigger:tag_open_end_time_trigger{}.GetAll(Self)
            Pos := Trigger.GetTransform().Translation
            AreaProp.IsInside[Pos]
        ):
            Trigger
        ShowEndTimeTrigger := ShowEndTimeTriggers[0] or Err()

        Manager := house_manager:
            D := Self 
            A := A
            Unlockables := UnlockableManagers
            UnlockablesMap := UnlockablesMap
            HouseResetEvent := HouseResetEvent
            PopupSellHouse := PopupSellHouse
            OwnerBarriers := OwnerBarriers
            ButtonToGranters := ButtonToGranters
            Container := A.Container
            ShowEndTimeTrigger := ShowEndTimeTrigger
        .Init()
        Manager
        
    TakeWeaponIfFull(Agent:agent, Granter:item_granter_device)<suspends>:void=
        if(ButtonToGranters := MButtonToGranters?):


            # # reset stacks
            # for(ConButton:ConButtonsForInvRemoval):
            # 	HasCount := ConButton.GetItemCount(Agent, 0)
            # 	if(HasCount > 0
            # 		CountInStack := ConButton.GetItemCountRequired(0)
            # 		Stacks := Ceil(HasCount/CountInStack)
            # 		Remaining := Mod[HasCount, CountInStack]
            # 		ToRemove := if(Remaining > 0). Remaining else. CountInStack
            # 	):
            # 		# LPrint("CountInStack {CountInStack}")
            # 		# LPrint("Stacks to add {Stacks}")
            # 		set InventoryCount += Stacks
            # 		set MAnyHasButton = option. (ConButton, ToRemove, CountInStack)

            # 		if(ConButton.IsHoldingItem[Agent, 0]):
            # 			set MHoldingButton = option. (ConButton, ToRemove, CountInStack)



            var InventoryCount :int= 0
            var MHoldingButton:?tuple(conditional_button_device, int, int) = false
            var MAnyHasButton:?tuple(conditional_button_device, int, int) = false

            # LPrint("ConButtonsForInvRemoval {ConButtonsForInvRemoval.Length}")
            # for(ConButton:ConButtonsForInvRemoval):
            # 	HasCount := ConButton.GetItemCount(Agent, 0)
            # 	if(HasCount > 0
            # 		CountInStack := ConButton.GetItemCountRequired(0)
            # 		Stacks := Ceil(HasCount/CountInStack)
            # 		Remaining := Mod[HasCount, CountInStack]
            # 		ToRemove := if(Remaining > 0). Remaining else. CountInStack
            # 	):
            # 		# LPrint("CountInStack {CountInStack}")
            # 		# LPrint("Stacks to add {Stacks}")
            # 		set InventoryCount += Stacks
            # 		set MAnyHasButton = option. (ConButton, ToRemove, CountInStack)

            # 		if(ConButton.IsHoldingItem[Agent, 0]):
            # 			set MHoldingButton = option. (ConButton, ToRemove, CountInStack)
                        
            
            # LPrint("InventoryCount {InventoryCount}")
            if(InventoryCount >= 5):
                if(HoldingButton := (MHoldingButton? or MAnyHasButton?)):
                    HoldingButton(0).SetItemCountRequired(0, HoldingButton(1))
                    # Sleep(0.0)
                    HoldingButton(0).Activate(Agent)
                    # Sleep(0.0)
                    HoldingButton(0).SetItemCountRequired(0, HoldingButton(2))

                    # spawn. ActivateLater()
                # else if(AnyHasButton := MAnyHasButton?):
                # 	AnyHasButton.Activate(Agent)

            Granter.GrantItem(Agent)
        else:
            Granter.GrantItem(Agent)
            LError()

    # ActivateLater()<suspends>:void=
    # 	Sleep(1.0)
    # 	if(ButtonToGranters := MButtonToGranters?):
    # 		set ButtonToGranters.IgnoreActivate = false
        
#gen
#con
#id-f3065d60-110a-4559-b829-bae7446cbff5
house_manager_args_c(PContainer:vcontainer,
    PBalance:houses_balance,
    PHighlightPropAsset:creative_prop_asset,
    PHighlightPropAssetFans:creative_prop_asset,
    PResourceManager:resources_manager,
    PLoc:i_localization,
    PTriggersPool:trigger_device_pool,
    PCheckIfPlayerCanAcquire:agent->logic,
    PArrowSystem:arrow_system,
    PAmountOfUnlockablesToFinish:int,
    PNotifs:notifications_system,
    PCustomBillboardSpawner:custom_billboard_spawner,
    PSaveSystem:save_system,
    PMRebirthCinematic:?cinematic_player
)<transacts>:house_manager_args=
    house_manager_args:
        Container := PContainer
        Balance := PBalance
        HighlightPropAsset := PHighlightPropAsset
        HighlightPropAssetFans := PHighlightPropAssetFans
        ResourceManager := PResourceManager
        Loc := PLoc
        TriggersPool := PTriggersPool
        CheckIfPlayerCanAcquire := PCheckIfPlayerCanAcquire
        ArrowSystem := PArrowSystem
        AmountOfUnlockablesToFinish := PAmountOfUnlockablesToFinish
        Notifs := PNotifs
        CustomBillboardSpawner := PCustomBillboardSpawner
        SaveSystem := PSaveSystem
        MRebirthCinematic := PMRebirthCinematic
#id-f3065d60-110a-4559-b829-bae7446cbff5
house_manager_args := struct:
    Container<public>:vcontainer
    Balance<public> :houses_balance
    HighlightPropAsset<public> :creative_prop_asset
    HighlightPropAssetFans<public> :creative_prop_asset
    ResourceManager<public> :resources_manager
    Loc<public>:i_localization
    TriggersPool<public>:trigger_device_pool
    CheckIfPlayerCanAcquire<public>:agent->logic
    ArrowSystem<public>:arrow_system
    AmountOfUnlockablesToFinish<public>:int
    Notifs<public>:notifications_system
    CustomBillboardSpawner<public>:custom_billboard_spawner
    SaveSystem<public>:save_system
    MRebirthCinematic<public>:?cinematic_player
    
    
#gen
#con
#id-aa4ae235-2bad-4b44-ae4e-49b407c0e051
unlocked_unlocked_event_data_c(PAgent:agent,
    PUnlockable:unlockable_id_options,
    PWasLoaded:logic,
    PUnlockableId:string
)<transacts>:unlocked_unlocked_event_data=
    unlocked_unlocked_event_data:
        Agent := PAgent
        Unlockable := PUnlockable
        WasLoaded := PWasLoaded
        UnlockableId := PUnlockableId
#id-aa4ae235-2bad-4b44-ae4e-49b407c0e051
unlocked_unlocked_event_data<public> := struct<internal>:
    Agent<public>:agent
    Unlockable<public>:unlockable_id_options
    WasLoaded<public>:logic
    UnlockableId<public>:string
    
house_manager<public> := class<unique><internal>(i_init_async):
    D<public>:house_manager_devic
    A:house_manager_args
    Unlockables:[]house_unlockable_manager
    HouseClaimed:event(tuple(agent,house_manager)) = event(tuple(agent,house_manager)){}
    AgentRebirthEvent<public>:event(agent) = event(agent){}
    UnlockableUnlockedEvent<public> : event(unlocked_unlocked_event_data) = event(unlocked_unlocked_event_data){}
    UnlockablesMap : [string]house_unlockable_manager
    PopupSellHouse:popup_dialog_device
    OwnerBarriers:[]barrier_device
    ButtonToGranters:btns_conditional_buttons_to_granters
    Container:vcontainer
    var Popup :?popup= false

    HouseUnclaimedEvent<public>:event() = event(){}
    HouseClaimedEvent<public>:event(agent) = event(agent){}
    HouseResetEvent<public>:event()
    UnlockAvailableToBuyEv<public>:event() = event(){}

    AllUnlocksBoughtEv<public>:event() = event(){}

    var ClaimTriggerInitTr:transform = transform{}
    var RebirthTriggerInitTr:transform = transform{}

    
    var<internal> PcTriggerInitTr<public>:transform = transform{}
    
    var<internal> MOwner<public>:?agent = false

    var VisibleUnlockables:[]house_unlockable_manager = array{}
    var UnlockedUnlockables:[]house_unlockable_manager = array{}

    var PlayerEvents :?player_events_manager_devic= false

    var PartialUnlockedCounter:[string]int = map{}
    var PartialUnlockedMax:[string]int = map{}

    var ResetablesOnReset:[]resetable = array{}

    ShowEndTimeTrigger:trigger_device

    var ShowEndTimeTriggerInitTr:transform = transform{}

    Init():house_manager=
        set Popup = A.Container.ResolveOp[popup] or Err()
        set ResetablesOnReset = for(R:A.Container.ResolveAllInArea[resetable, D.AreaProp]):
            R
        set PcTriggerInitTr = D.PcTrigger.GetTransform()
        set ClaimTriggerInitTr = D.ClaimTriggerParent.GetTransform()
        set RebirthTriggerInitTr = D.RebirthTriggerParent.GetTransform()
        A.Container.Register(Self)
        for(Unlockable:Unlockables):
            Unlockable.UnlockedEvent.Subscribe1(OnUnlockableUnlocked)
        
        for(Unlockable:Unlockables):
            # LPrint("Unlockable {Unlockable.UnlockableOptions.Name} partials:")
            for(PartialId:Unlockable.UnlockableOptions.ShowAfterUnlocking):
                # LPrint("PartialId {PartialId} adding")
                NewVal := PartialUnlockedMax[PartialId] + 1 or 1
                if. set PartialUnlockedMax[PartialId] = NewVal
                else:
                    LError()
        # check for bugs
        for(Unlockable:Unlockables
            not IsUnlockPossibleToUnlock[Unlockable]
        ):
            LErrorPrint("Unlockable: {Unlockable.UnlockableOptions.Name}, Id: {Unlockable.D.Id} is not possible to unlock")
        set ShowEndTimeTriggerInitTr = ShowEndTimeTrigger.GetTransform()
        

        if(UnlocksCounter := D.MUnlocksCounter?):
            UnlockIdsForCounter:[]string = for(Id->UnlockableOptions:A.Balance.Presets.Map
                UnlockableOptions.UseForCounter?
            ):
                Id
            UnlocksCounter.InitManual(UnlockIdsForCounter, UnlockableUnlockedEvent)
        # for(PartialId->Count:PartialUnlockedMax):
        # 	LPrint("Partial {PartialId} max = {Count}")
            

        set PlayerEvents = A.Container.ResolveErrOp(player_events_manager_devic) 

        # D.ShirtCreatorsManager.Init(D.CameraSequenceParent, D.CameraSequence, D.Area, HouseResetEvent, Loc)
        Self

    IsUnlockPossibleToUnlock(Unlockable:house_unlockable_manager)<decides><transacts>:void=
        UnlockId := Unlockable.D.Id
        if(not PartialUnlockedMax[UnlockId], UnlockId <> "ustart"):
            Fail[]


    OnUnlockableUnlocked(Data:unlocked_event_data):void=
        spawn. OnUnlockableUnlockedAsync(Data)

    OnUnlockableUnlockedAsync(Data:unlocked_event_data)<suspends>:void=
        Agent := Data.Agent
        Unlockable := Data.Unlockable
        set UnlockedUnlockables += array{Unlockable}

        UnlockableUnlockedEvent.Signal(unlocked_unlocked_event_data_c(Agent, Unlockable.UnlockableOptions, Data.WasLoaded, Unlockable.D.Id))

        if(not Unlockable.UnlockableOptions.AdditionalData.ArrayFirst[IsExternalUpgradeResource]):
            GoldGainPerSec := Unlockable.UnlockableOptions.GoldGainPerSec
            FansGainPerSec := Unlockable.UnlockableOptions.FansGainPerSec
            if(FansGainPerSec > 0):
                A.ResourceManager.IncreaseFansPerSec(Agent, FansGainPerSec)
            
            if(GoldGainPerSec > 0):
                A.ResourceManager.IncreaseGoldPerSec(Agent, GoldGainPerSec)


        if(Unlockable.UnlockableOptions.EventDataAfterUnlocking.ArrayFirst[IsPiggyUnlock]
            Piggy := Container.ResolveAllInArea[VMinigames.use_button_for_gold, D.AreaProp][0]
            Owner := MOwner?
        ):
            Piggy.Show(Owner)

        if(QuestShowString := Unlockable.UnlockableOptions.EventDataAfterUnlocking.ArrayFirst[IsQuestShow]
            QuestIdToShow := QuestShowString.Split(';')[1]
            Owner := MOwner?
        ):
            if(QuestSystem := A.Container.Resolve[quest_system]):
                QuestSystem.ShowQuest(Owner, QuestIdToShow)
        
        #TODO add moving back these props on house reset one day
        for(ToHide:D.HideOnUnlocked
            ToHide.Id = Data.Unlockable.D.Id
        ):
            if(not ToHide.HideOnlyWhenUnlockLoadedFromSave? or Data.WasLoaded?):
                HidePos := ToHide.Prop.GetTransform().Translation.AddZ(-5000.0)
                if. ToHide.Prop.TeleportTo[HidePos, Rotation0]

        for(UnlockId:Unlockable.UnlockableOptions.ShowAfterUnlocking):
        #     UnlockUnlockable(UnlockId)
        
        # for(UnlockId:Unlockable.UnlockableOptions.ShowAfterUnlockingPartial):
            NewVal := PartialUnlockedCounter[UnlockId] + 1 or 1
            if. set PartialUnlockedCounter[UnlockId] = NewVal
            
            # if(Maxx := PartialUnlockedMax[UnlockId]):
            # 	LPrint("Partial {UnlockId}, cur = {NewVal}, max = {Maxx}")
            if(NewVal = PartialUnlockedMax[UnlockId]):
                UnlockUnlockable(UnlockId)
    
    IsPiggyUnlock<public>(Text:string)<decides><transacts>:void=
        Text = "piggy"

    IsQuestShow<public>(Text:string)<decides><transacts>:void=
        Text.Contains["showquest"]

    UnlockUnlockable(UnlockId:string)<suspends>:void=
        if(NextUnlockable := UnlockablesMap[UnlockId]):
            ShowUnlockableTrigger(NextUnlockable)
        else if(UnlockId = "dirtbike"):
            Bikes := D.GetDevicesInArea(vehicle_spawner_dirtbike_device, D.AreaProp)
            for(Bike:Bikes):
                Bike.Enable()
                Bike.RespawnVehicle()
        else:
            LErrorPrint("Unlock not found: \"{UnlockId}\" (check for bad spaces)")
        

    
    InitAsync<override>(Containerr:vcontainer)<suspends>:void=
        loop:
            HouseUnclaimedEvent.Signal()
            HideSellShop()
            MaybeOwner := D.ClaimTrigger.TriggeredEvent.Await()
            
            if(Owner := MaybeOwner?):
                CanTake := A.CheckIfPlayerCanAcquire(Owner)
                if(CanTake?):
                    HouseClaimedEvent.Signal(Owner)
                    OnClaimedAsync(Owner)
                    ResetHouse()

    ReRegisterOwnerReference():void=
        #fix for join in progress
        spawn. ReRegisterOwnerReferenceAsync()

    ReRegisterOwnerReferenceAsync()<suspends>:void=
        #fix for join in progress
        if(O := MOwner?):
            Sleep(1.0)
            # D.PlayerReference.Clear()
            Sleep(1.0)
            # D.PlayerReference.Register(O)
        
    StartOfHouse(Owner:agent)<suspends>:void=
        set MOwner = option. Owner
        # if (S := A.MUpgradableSpawner?):
        # 	S.SetOwner(Owner)
        if (GoldGainUpgrader := D.MGoldGainUpgrader?):
            GoldGainUpgrader.InitForAgent(Owner)
        # if (SkillsUpgrader := D.MSkillsUpgrader?):
        # 	SkillsUpgrader.InitForAgent(Owner)
        if (Upgrader := D.MGoldCritUpgrader?):
            Upgrader.InitForAgent(Owner)
        if (Upgrader := D.MGoldCritAmountUpgrader?):
            Upgrader.InitForAgent(Owner)
        
        if (AppInvestment := D.MAppInvestment?):
            AppInvestment.AgentClaimed(Owner)
            
        ButtonToGranters.SetOwner(option. Owner)
        D.PlayerCheckPoint.Register(Owner)
        D.ClaimTrigger.Disable()
        if. D.ClaimTriggerParent.TeleportTo[ClaimTriggerInitTr.WithTranslationZAndScale(-500.0, 0.001)]
        D.ClaimTriggerParent.Hide()
        for(Barrier:OwnerBarriers):
            Barrier.AddToIgnoreList(Owner)
        
        set StartTime = GetSimulationElapsedTime()
        # D.PlayerReference.Register(Owner)
        IsCoop := Container.Resolve[VCoop.coop].CoopAdmin? and true or false 
        for(Unlockable:Unlockables):
            Unlockable.RegisterOwner(option. Owner, IsCoop)
        
        # ChestManagerDevices := GetDevicesInArea(chest_manager_devic, D.Area)
        # for(ChestDev:ChestManagerDevices):
        # 	Chest := ChestDev.Init(Container)
        # 	Chest.SetOwnerForNotif(option. Owner)
        # 	Chest.ResetForJoinInProgress()

        Sleep(0.0)
        # if(Char := Owner.GetFortCharacter[]):
        # 	Char.Hide()

    UnlockFreeUnlocks()<suspends>:void=
        if(Unlockable := UnlockablesMap["ustart"]):
            ShowUnlockableTrigger(Unlockable)
        else:
            LPrint("ERROR: ustart unlock id not found")

        # free unlocks aren't saved
        # for(FreeUnlock:Unlockables
        # 	FreeUnlock.UnlockableOptions.GoldCost = 0
        # 	FreeUnlock.UnlockableOptions.FansCost = 0
        # ):
        # 	Print("{FreeUnlock.UnlockableOptions.Name}")
        # 	FreeUnlock.LoadBuyRequestedEvent.Signal()
            
        loop:
            Print("{VisibleUnlockables.Length}")
            if (Free := VisibleUnlockables.MinBy[CompareUnlocksManagersByGold]
                Print("{Free.UnlockableOptions.Name}")
                Free.UnlockableOptions.GoldCost = 0
                Free.UnlockableOptions.FansCost = 0
            ):
                Free.LoadBuyRequestedEvent.Signal()
            else:
                break
                
    LoadHouse(Owner:agent):void=
        if(LoadedHouse := A.SaveSystem.GetHouseMap[Owner]):
            for(Key:LoadedHouse):
                if(ToUnlock := Unlockables.FirstDataUnlockableString[UnlockableHasName, Key]):
                # if(ToUnlock := Unlockables.FirstData[UnlockableHasName, Key]):
                    Key
                    ToUnlock.LoadBuyRequestedEvent.Signal()
                else:
                    LErrorPrint("Unlockable with id '{Key}' is missing!!!")


        # SavedLevels := A.GetSavedUnlocksLevels(Owner)
        # if (GoldUnlocksLevel :=  SavedLevels[0]
        # 	GoldUnlocksLevel > 0
        # ):
        # 	for(Lvl:= 0..GoldUnlocksLevel-1):
        # 		if(Cheapest := VisibleUnlockables.MinBy[CompareUnlocksManagersByGold]):
        # 			Cheapest.LoadBuyRequestedEvent.Signal()
        # 		else:
        # 			LError()
        
    UnlockableHasName(Unlockable:house_unlockable_manager, Name:string)<decides><transacts>:void=
        Unlockable.D.Id = Name
        
    ShowUnlockableTrigger(Unlockable:house_unlockable_manager)<suspends>:void=
        if (not Unlockable.UnlockableOptions.AdditionalData.Find[unlockable_additional_data.HideOnly],
            not UnlockedUnlockables.Find[Unlockable]
        ):
            Unlockable.ShowTrigger(A.HighlightPropAsset, A.HighlightPropAssetFans)
            
            if(Unlockable.UnlockableOptions.AdditionalData.ArrayFirst[IsShowUpdate]):
                # ShowSellShop()
                
                # if. ShowEndTimeTrigger.TeleportTo[ShowEndTimeTriggerInitTr]
                set EndTime = GetSimulationElapsedTime()
                # AllUnlocksBoughtEv.Signal()
            
            set VisibleUnlockables += array{Unlockable}
            spawn. RemoveWhenUnlockedAsync(Unlockable)

    # OnShowBuildTimeTrigger()<suspends>:void=
    # 	loop:
    # 		MAg := ShowEndTimeTrigger.TriggeredEvent.Await()
    # 		if(MAg? = MOwner?):
    # 			TimeTook := EndTime - StartTime
    # 			TimeTookString := TimeToString(TimeTook, ?RemoveMilis := false)
    # 			A.PopupEndTime.SetDescriptionText("{Loc.G(Agent, "Whole bank unlocked in")}: {TimeTookString}".ToMessage())
    # 			A.PopupEndTime.SetButtonText("Ok".ToMessage(), 2)
    # 			A.PopupEndTime.ShowAwaitForButtonOrExitAndHideWithTimout(Agent, PlayerRemoved, 10.0)

        

    RemoveWhenUnlockedAsync(Unlockable:house_unlockable_manager)<suspends>:void=
        race:
            Unlockable.ResetEvent.Await()
            Unlockable.UnlockedEvent.Await()

        set VisibleUnlockables = VisibleUnlockables.RemoveAllElements(Unlockable)
    

    var StartTime:float = 0.0
    var EndTime:float = 0.0
    var AmountOfUnlocked :int= 0

    AutoBuy(Owner:agent)<suspends>:void=
        race:
            PlayerEvents.G().PlayerRemovedOncePerGame.AwaitFor(Owner)
            loop:
                Sleep(0.1)
                if(Unlock := VisibleUnlockables[0]):
                    Unlock.FakeAutoBuy()

    OnClaimedAsync(Owner:agent)<suspends>:void=
        LPrint("CLAIM")
        race:
            block:
                PlayerEvents.G().PlayerRemovedOncePerGame.AwaitFor(Owner)
                return
            StartOfHouse(Owner)
        LPrint("StartOfHouse Done")
        # return
        # Sleep(0.0)
        
        # if(LastUnlockables := MLastUnlockables?
        # 	LastUnlockableGold := LastUnlockables(0)
        # 	LastUnlockableFans := LastUnlockables(1)
        # ):

        if(A.Balance.TestAutoUnlock?):
            spawn. AutoBuy(Owner)
        race:
            PlayerEvents.G().PlayerRemovedOncePerGame.AwaitFor(Owner)
            block:
                LPrint("house before GetPlayerDataMapOrAwait(Owner)")
                ResData := A.ResourceManager.GetPlayerDataMapOrAwait(Owner)
                LPrint("house after GetPlayerDataMapOrAwait(Owner)")
                Sleep(0.0)
                loop:
                    PlayerGold :int= race:
                        ResData.GetGoldChangeEvent().Await()
                        block:
                            Sleep(10.0)
                            ResData.GetGold()
                    var MCheapestUnlock :?house_unlockable_manager= false
                    for(Unlockable:VisibleUnlockables
                        # not Unlockable.IsShowingArrowToTrigger?
                        Unlockable.CanShowArrow?
                        Cost := Unlockable.UnlockableOptions.GoldCost
                        Cost > 0
                        (not A.Balance.ShowArrowToUnlockOnlyWhenEnoughCash? or Cost <= PlayerGold)
                        (Cost < MCheapestUnlock?.UnlockableOptions.GoldCost) or not MCheapestUnlock?
                    ):
                        set MCheapestUnlock = option. Unlockable


                    for(Unlockable:VisibleUnlockables
                        Unlockable.IsShowingArrowToTrigger?
                        Unlockable.UnlockableOptions.GoldCost > 0
                        Unlockable.UnlockableOptions.GoldCost > PlayerGold
                    ):
                        Unlockable.HideArrowToTrigger()

                    if(CheapestUnlock := MCheapestUnlock?
                        not CheapestUnlock.IsShowingArrowToTrigger?
                    ):
                        CheapestUnlock.ShowArrowToTrigger()
                        UnlockAvailableToBuyEv.Signal()

            # loop{
            # 	D.MarketingMutator.AgentEntersEvent.AwaitFor(Owner)
            # 	race{
            # 		D.MarketingMutator.AgentExitsEvent.AwaitFor(Owner)
            # 		loop{
            # 			ResourceManager.GiveFansWithMultiplier(Owner, 1.0, 1.0)
            # 			Sleep(1.0)
            # 		}
            # 	}
            # }
            # loop{
            # 	Bonus := D.ShirtCreatorsManager.TotalMoneyBonusChanged.ChangedEvent.Await()
            # 	ResourceManager.SetBonusMultiplier(Owner, Bonus)
            # }
            loop:
                UnlockData := UnlockableUnlockedEvent.Await()
                set AmountOfUnlocked += 1
                # LPrint("{UnlockTuple.Unlockable.Name}, cur unlocks: {AmountOfUnlocked}, required: {A.AmountOfUnlockablesToFinish}")
                # if(AmountOfUnlocked >= A.AmountOfUnlockablesToFinish):
                # 	ShowSellShop()
                
            # loop: make block a loop to keep house claimed
            block:
                loop:
                    # if(A.Balance.TestShowRebirthInstantly?):
                    # 	ShowSellShop()
                    D.RebirthTrigger.TriggeredEvent.AwaitForOption(Owner)
                    ResetAccepted :logic= #race:
                        block:
                            TimeSec := GetSimulationElapsedTime() - StartTime
                            # MOk := Popup.G().ShowOkCancel(Owner, "Do you want to reset the island?".ToMessage())
                            MOk := Popup.G().ShowOkCancel(Owner, "Gratulacje!\nWybudowałeś nową siedzibę Banku PEKAO S.A.\nCzy chcesz zacząć od nowa w trybie bonusowym (+1 punkt za każdą wykonaną czynność)?".ToMessage())
                            
                            # PopupSellHouse.SetTitleText("{A.Loc.G(Owner, "Rebirth Reset")}".ToMessage())
                            # # PctIncrease := A.ResourceManager.GetNextRebirthMultiplierPct(Owner)
                            # # PopupSellHouse.SetDescriptionText("{A.Loc.G(Owner, "You've completed the game in")} {TimeToString(TimeSec, ?ShowUnits := true)}! {A.Loc.G(Owner, "Make a screenshot for the leaderboard! Do you want to reset all resources, castle and get +{PctIncrease}% gain bonus?")}".ToMessage())
                            # PopupSellHouse.SetDescriptionText("{A.Loc.G(Owner, "You've completed the game in")} {TimeToString(TimeSec, ?ShowUnits := true)}! {A.Loc.G(Owner, "! Do you want to reset the island?")}".ToMessage())
                            # PopupSellHouse.SetButtonText("{A.Loc.G(Owner, "Cancel")}".ToMessage(), 0)

                            # PopupSellHouse.Show(Owner)
                            # ClickedIdWAgent := PopupSellHouse.RespondingButtonEvent.Await()
                            # ClickedId := ClickedIdWAgent(1)
                            # # Print("ClickedId: {ClickedId}")
                            # if(ClickedId = 1). true else. false
                            MOk? and true or false
                        
                        # block:
                        # 	PopupSellHouse.DismissedEvent.Await()
                        # 	false
                        # block:
                        # 	PopupSellHouse.TimeOutEvent.Await()
                        # 	false
                        # block:
                        # 	Sleep(10.0)
                        # 	PopupSellHouse.Hide(Owner)
                        # 	false
                        # block:
                        # 	PlayerEvents.G().PlayerEliminatedCharacter.AwaitFor(Owner) # THIS IMPORTANT TO HANDLE IF CAN DIE!!!!
                        # 	PopupSellHouse.Hide(Owner)
                        # 	false
                        
                    
                    Print("ResetAccepted {ResetAccepted.ToString()}")
                    if(ResetAccepted?):
                        AllUnlocksBoughtEv.Signal()
                        Print("spawn RunRebirthCinematic")
                        spawn. RunRebirthCinematic(Owner)
                        # break
                    
                


                # Old way: instant reset after trigger
                # ResetHouse()
                # AgentRebirthEvent.Signal(Owner)
                # A.ResourceManager.ResetDataIncreaseMultiplier(Owner, true)
                # Old way: instant reset after trigger



                # StartOfHouse(Owner) uncomment to keep house claimed
                # UnlockFreeUnlocks() uncomment to keep house claimed

            #this should be last
            block:
                UnlockFreeUnlocks()
                LoadHouse(Owner)
                Sleep(Inf)


    RunRebirthCinematic(Owner:agent)<suspends>:void=
        LPrint("RunRebirthCinematic")
        
        if(Cinematic := A.MRebirthCinematic?):
            LPrint("Cinematic.Play")
            Cinematic.Play(Owner)
            LPrint("AwaitFor")
            Cinematic.CinematicEndedEvent.AwaitFor(Owner) #bug infinite on player death

            LPrint("ResetHouse")
            ResetHouse()
            AgentRebirthEvent.Signal(Owner)
            A.ResourceManager.ResetDataIncreaseMultiplier(Owner, true)

        Sleep(0.0)

    ResetHouse():void=
        set PartialUnlockedCounter = map{}
        for(Resetable:ResetablesOnReset):
            Resetable.Reset()

        # if(S := A.MUpgradableSpawner?):
        # 	S.Reset()
        set AmountOfUnlocked = 0
        ButtonToGranters.SetOwner(false)
        HouseResetEvent.Signal()

        for(Barrier:OwnerBarriers):
            Barrier.RemoveAllFromIgnoreList()

        set MOwner = false
        # D.PlayerReference.Clear()
        
        set UnlockedUnlockables = array{}
        for(Unlockable:Unlockables):
            Unlockable.RegisterOwner(false, false)
            Unlockable.HideProp()
        
        Bikes := D.GetDevicesInArea(vehicle_spawner_dirtbike_device, D.AreaProp)
        for(Bike:Bikes):
            Bike.Disable()
            Bike.DestroyVehicle()
        
        D.ClaimTrigger.Enable()
        if. D.ClaimTriggerParent.TeleportTo[ClaimTriggerInitTr]
        D.ClaimTriggerParent.Show()
        HideSellShop()

        # ChestManagerDevices := GetDevicesInArea(chest_manager_devicE, D.Area)
        # for(ChestDev:ChestManagerDevices):
        # 	Chest := ChestDev.Init(Container)
        # 	Chest.SetOwnerForNotif(false)

    # ShowSellShop():void=
    # 	if. D.RebirthTriggerParent.TeleportTo[RebirthTriggerInitTr]
    # 	D.RebirthTriggerParent.Show()

    HideSellShop():void=
        if. ShowEndTimeTrigger.TeleportTo[ShowEndTimeTriggerInitTr.WithTranslationZAndScale(-500.0, 0.001)]
        # if. D.RebirthTriggerParent.TeleportTo[RebirthTriggerInitTr.WithTranslationZAndScale(-500.0, 0.001)]
        # D.RebirthTriggerParent.Hide()
        
    # Stage1Async(Owner:agent)<suspends>:void={
    #     race{
    #         PlayerRemovedEvent.AwaitFor(Owner)
    #         block{
    #             Stage1Async(Owner:agent)
    #         }
    #     }
    # }

IsSpawnerUpgrade<public>(Text:string)<decides><transacts>:void=
    Text = "upgrade_spawner"

IsExternalUpgradeResource<public>(Text:unlockable_additional_data)<decides><transacts>:void=
    Text = unlockable_additional_data.ExternalResources
IsShowUpdate<public>(Text:unlockable_additional_data)<decides><transacts>:void=
    Text = unlockable_additional_data.ShowUpdatePopup