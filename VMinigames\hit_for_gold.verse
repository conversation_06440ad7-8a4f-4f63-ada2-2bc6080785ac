using. /Fortnite.com/Devices
using. /UnrealEngine.com/Assets
using. /UnrealEngine.com/Temporary/Diagnostics
using. /Verse.org/Simulation
using. VAudio
using. VGiga
using. VResourcesSystem


hit_for_gold := class(auto_creative_device, i_init_async):
	var Resources:?resources_manager = false
	var Rewards :?gm_balance_rewards= false
	@editable MAudio<public>:?audio_player_device = false

	InitAsync<override>(Container:vcontainer)<suspends>:void=
		set Rewards = Container.ResolveOp[gm_balance_rewards] or Err()
		set Resources = Container.ResolveOp[resources_manager] or Err()
		Manipulators := hit_for_gold_tag{}.GetAll(Self)

		for(Manipulator : Manipulators):
			spawn{ HandleManipulator(Manipulator) }

	HandleManipulator(Manipulator:prop_manipulator_device)<suspends>:void=
		Pos := Manipulator.GetTransform().Translation
		loop:
			Agent := Manipulator.DamagedEvent.Await()
			Resources.G().GiveGoldWithMultiplier(Agent, Rewards.G().HitOreRatio, Rewards.G().HitOreTimeSec, ?ShowNotification := true)
			spawn. CancelFx(SpawnParticleSystem(Modele.Piggy.Blow_up_Piggy, Pos))
			if(Audio := MAudio?):
				Audio.Play(Agent)


	CancelFx(ToCancel:cancelable)<suspends>:void=
		Sleep(5.0)
		ToCancel.Cancel()
		