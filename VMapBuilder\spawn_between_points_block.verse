using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using.  VGiga 
using. VPropSpawner
spawn_between_points_block_block_devic<public> := class(creative_device):
	@editable Datas:[]spawn_between_points_block_data = array{}

	Init<public>(Container:player_events_manager_devic,
		PropSpawner:prop_spawner,
		SpawnAtStart:logic
	):spawn_between_points_block=
		Manager := spawn_between_points_block:
			D := Self
			PropSpawner := PropSpawner
		.Init(SpawnAtStart)
		Manager


spawn_between_points_block<public> := class():
	D<public>:spawn_between_points_block_block_devic
	PropSpawner<public>:prop_spawner
	var SpawnedProps:[]creative_prop_unique = array{}

	Init<public>(SpawnAtStart:logic):spawn_between_points_block=
		if(SpawnAtStart?):
			RespawnAll()
		Self

	DespawnAll<public>():void=
		for(Prop:SpawnedProps):
			Prop.Dispose()

	RespawnAll<public>():void=
		for(Data:D.Datas
			Path:Data.Paths
			StartPos := Path.StartProp.GetTransform().Translation
			EndPos := Path.EndProp.GetTransform().Translation
			ToEndPos := -StartPos + EndPos
			AmountToSpawn := Ceil[ToEndPos.Length()/ 96.0]
			ToEndPosDividedVec := ToEndPos.Divide(AmountToSpawn.F())
			StartId := if(Path.SkipSpawningStart?). 1 else. 0
			EndId := AmountToSpawn- if(Path.SkipSpawningEnd?). 2 else. 1
			X := StartId..EndId
			PropAsset := Data.PropsToSpawn.GetRandom[]
			Pos := if(Data.Snap96Grid?):
				ToSnap := StartPos + (ToEndPosDividedVec).Mul(X.F())
				Rx := Round[ToSnap.X]
				Ry := Round[ToSnap.Y]
				Rz := Round[ToSnap.Z]
				vector3:
					X := Rx - Mod[Rx, 96] * 1.0
					Y := Ry - Mod[Ry, 96] * 1.0
					Z := Rz - Mod[Rz, 96] * 1.0
			else:
				StartPos + (ToEndPosDividedVec).Mul(X.F())
			Scale := Vector3One * GetRandomFloat(Path.MinScale, Path.MaxScale)
			Rotation := rotation{}
		):
			spawn. SpawnAndAddToList(PropAsset,
				transform:
					Translation := Pos
					Rotation := Rotation
					Scale := Scale
			)

		
	SpawnAndAddToList(PropAsset:creative_prop_asset, Tr:transform)<suspends>:void=
		MProp := PropSpawner.Spawn(PropAsset, Tr)
		if(Prop := MProp?):
			set SpawnedProps = array. Prop

		


spawn_between_points_block_data := class<concrete>():
	@editable Paths:[]spawn_between_points_block_data_path = array{}
	@editable PropsToSpawn:[]creative_prop_asset = array{}
	@editable Angles90Only:logic = false
	@editable Snap96Grid:logic = false

spawn_between_points_block_data_path := class<concrete>():
	@editable StartProp:creative_prop = creative_prop{}
	@editable EndProp:creative_prop = creative_prop{}
	@editable SkipSpawningStart:logic = false	
	@editable SkipSpawningEnd:logic = false
	@editable SpawnMode:spawn_between_points_block_path_mode = spawn_between_points_block_path_mode.AmountToSpawn
	@editable AmountToSpawn:int = 1
	@editable DistancePerSpawnSquared:float = 100.0
	@editable MinScale:float = 1.0
	@editable MaxScale:float = 1.0


spawn_between_points_block_path_mode := enum:
	AmountToSpawn
	DistancePerSpawn

	
	
	