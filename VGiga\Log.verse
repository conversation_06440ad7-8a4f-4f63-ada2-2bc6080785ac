using { /Verse.org/Simulation }
using { /UnrealEngine.com/Temporary/UI }
using { /UnrealEngine.com/Temporary/Diagnostics }
using { /UnrealEngine.com/Temporary/SpatialMath }
using { /Fortnite.com/Devices }

# <#> Quick logs
#     Usage:
#         L().Print("hello world")
#         L().Error()

# # this mess below is required for static class instance
giga_log_channel := class(log_channel){}

LError<public>()<transacts>:void =
    Logg := log{Channel := giga_log_channel}
    Print("Error in:")
    Logg.PrintCallStack()

LErrorPrint<public>(Msg:string)<transacts>:void =
    Logg := log{Channel := giga_log_channel}
    Print("Error in: {Msg}")
    Logg.PrintCallStack()

LPrint<public>(Msg:string)<transacts>:void =
	Print(Msg)
    # Logg := log{Channel := giga_log_channel}
    # Logg.Print(Msg)
    # Logg.PrintCallStack()


LPrintStack<public>()<transacts>:void=
    Logg := log{Channel := giga_log_channel}
    Logg.PrintCallStack()


i_logger<public> := interface:
    Log<public>(Text:string):void

empty_logger<public> := class<concrete>(i_logger):
    Log<override>(Text:string):void={}
    


giga_logger<public> := class<concrete>(i_logger){
    var LogBillboard : ?billboard_device = false
    var LogArray : []string = array{}
    var KLog:?log = false
    var LogBillboardId :int= 0
    
    Log<override>(Text:string):void={
        LogToBillboard(Text)
    }

    LogToBillboard<public>(Text:string):void={
        set LogArray += array{"{LogBillboardId}:{Text}"}
        if(LogArray.Length > 12, NewArray := LogArray.RemoveElement[0]){
            set LogArray = NewArray
        }
        if(LegitLogBillboard := LogBillboard?){
            LegitLogBillboard.SetText(Join(LogArray, " ").ToMessage())
        }
        set LogBillboardId += 1
    }

    SetBillboard<public>(Billboard:billboard_device):void={
        set LogBillboard = option{Billboard}
    }
}

SpawnPropResultToString<public>(Success:spawn_prop_result)<transacts>:string={
    case(Success){
        spawn_prop_result.UnknownError => "UnknownError"
        spawn_prop_result.InvalidSpawnPoint => "InvalidSpawnPoint"
        spawn_prop_result.SpawnPointOutOfBounds => "SpawnPointOutOfBounds"
        spawn_prop_result.InvalidAsset => "InvalidAsset"
        spawn_prop_result.TooManyProps => "TooManyProps"
        spawn_prop_result.Ok => "OkSpawn"
    }
}
PrintSpawnPropResult<public>(Success:spawn_prop_result)<transacts>:void={
    case(Success){
        spawn_prop_result.UnknownError => LPrint("UnknownError")
        spawn_prop_result.InvalidSpawnPoint => LPrint("InvalidSpawnPoint")
        spawn_prop_result.SpawnPointOutOfBounds => LPrint("SpawnPointOutOfBounds")
        spawn_prop_result.InvalidAsset => LPrint("InvalidAsset")
        spawn_prop_result.TooManyProps => LPrint("TooManyProps")
        spawn_prop_result.Ok => LPrint("OkSpawn")
    }
}