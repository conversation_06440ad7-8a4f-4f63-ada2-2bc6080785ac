using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/UI
using. /Fortnite.com/UI
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
notifications_feed_generated := class:
	ImageGold_7:texture_block
	TextFull_7:text_block
	TextRes_7:text_block
	Image_7:texture_block
	Overlay_7:overlay
	ImageGold_6:texture_block
	TextFull_6:text_block
	TextRes_6:text_block
	Image_6:texture_block
	Overlay_6:overlay
	ImageGold_5:texture_block
	TextFull_5:text_block
	TextRes_5:text_block
	Image_5:texture_block
	Overlay_5:overlay
	ImageGold_4:texture_block
	TextFull_4:text_block
	TextRes_4:text_block
	Image_4:texture_block
	Overlay_4:overlay
	ImageGold_3:texture_block
	TextFull_3:text_block
	TextRes_3:text_block
	Image_3:texture_block
	Overlay_3:overlay
	ImageGold_2:texture_block
	TextFull_2:text_block
	TextRes_2:text_block
	Image_2:texture_block
	Overlay_2:overlay
	ImageGold_1:texture_block
	TextFull_1:text_block
	TextRes_1:text_block
	Image_1:texture_block
	Overlay_1:overlay
	TextFull:text_block
	TextRes:text_block
	ImageGold:texture_block
	Image:texture_block
	Overlay:overlay
	StackBox:stack_box
	NotificationsFeed:canvas

TextFull_7TextVar<localizes>:message =  "Woofffffffd +3fffffffdfsdf"
TextRes_7TextVar<localizes>:message =  "🌲"
TextFull_6TextVar<localizes>:message =  "Woofffffffd +3fffffffdfsdf"
TextRes_6TextVar<localizes>:message =  "🌲"
TextFull_5TextVar<localizes>:message =  "Woofffffffd +3fffffffdfsdf"
TextRes_5TextVar<localizes>:message =  "🌲"
TextFull_4TextVar<localizes>:message =  "Woofffffffd +3fffffffdfsdf"
TextRes_4TextVar<localizes>:message =  "🌲"
TextFull_3TextVar<localizes>:message =  "Woofffffffd +3fffffffdfsdf"
TextRes_3TextVar<localizes>:message =  "🌲"
TextFull_2TextVar<localizes>:message =  "Woofffffffd +3fffffffdfsdf"
TextRes_2TextVar<localizes>:message =  "🌲"
TextFull_1TextVar<localizes>:message =  "Woofffffffd +3fffffffdfsdf"
TextRes_1TextVar<localizes>:message =  "🌲"
TextFullTextVar<localizes>:message =  "Woofffffffd +3fffffffdfsdf"
TextResTextVar<localizes>:message =  "🌲"
make_notifications_feed_generated():notifications_feed_generated=

	ImageGold_7 :texture_block= texture_block:
		DefaultImage := VResourcesSystem.Assets.TTextures.T_GoldCoin
		DefaultDesiredSize := vector2:
			X := 38.000000
			Y := 38.000000
	TextFull_7 :text_block= text_block:
		DefaultText := TextFull_7TextVar
		DefaultTextColor := color:
			R := 0.947917
			G := 0.947917
			B := 0.947917
		DefaultTextOpacity := 1.000000
		DefaultShadowOffset := option. vector2:
			X := 2.000000
			Y := 2.000000
		DefaultShadowColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
	TextRes_7 :text_block= text_block:
		DefaultText := TextRes_7TextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
		DefaultShadowOffset := option. vector2:
			X := 2.000000
			Y := 2.000000
		DefaultShadowColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
	Image_7 :texture_block= texture_block:
		DefaultImage := VGiga.Assets.GigaTextures.T_horizontal_background_fade_notifs_ui
		DefaultTint := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
		DefaultDesiredSize := vector2:
			X := 350.000000
			Y := 50.000000
	Overlay_7 :overlay= overlay:
		Slots := array:
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Top
				Widget := Image_7
			overlay_slot:
				Padding := margin:
					Left := 24.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := TextRes_7
			overlay_slot:
				Padding := margin:
					Left := 80.000000
					Right := 30.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := TextFull_7
			overlay_slot:
				Padding := margin:
					Left := 24.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := ImageGold_7
	ImageGold_6 :texture_block= texture_block:
		DefaultImage := VResourcesSystem.Assets.TTextures.T_GoldCoin
		DefaultDesiredSize := vector2:
			X := 38.000000
			Y := 38.000000
	TextFull_6 :text_block= text_block:
		DefaultText := TextFull_6TextVar
		DefaultTextColor := color:
			R := 0.947917
			G := 0.947917
			B := 0.947917
		DefaultTextOpacity := 1.000000
		DefaultShadowOffset := option. vector2:
			X := 2.000000
			Y := 2.000000
		DefaultShadowColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
	TextRes_6 :text_block= text_block:
		DefaultText := TextRes_6TextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
		DefaultShadowOffset := option. vector2:
			X := 2.000000
			Y := 2.000000
		DefaultShadowColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
	Image_6 :texture_block= texture_block:
		DefaultImage := VGiga.Assets.GigaTextures.T_horizontal_background_fade_notifs_ui
		DefaultTint := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
		DefaultDesiredSize := vector2:
			X := 350.000000
			Y := 50.000000
	Overlay_6 :overlay= overlay:
		Slots := array:
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Top
				Widget := Image_6
			overlay_slot:
				Padding := margin:
					Left := 24.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := TextRes_6
			overlay_slot:
				Padding := margin:
					Left := 80.000000
					Right := 30.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := TextFull_6
			overlay_slot:
				Padding := margin:
					Left := 24.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := ImageGold_6
	ImageGold_5 :texture_block= texture_block:
		DefaultImage := VResourcesSystem.Assets.TTextures.T_GoldCoin
		DefaultDesiredSize := vector2:
			X := 38.000000
			Y := 38.000000
	TextFull_5 :text_block= text_block:
		DefaultText := TextFull_5TextVar
		DefaultTextColor := color:
			R := 0.947917
			G := 0.947917
			B := 0.947917
		DefaultTextOpacity := 1.000000
		DefaultShadowOffset := option. vector2:
			X := 2.000000
			Y := 2.000000
		DefaultShadowColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
	TextRes_5 :text_block= text_block:
		DefaultText := TextRes_5TextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
		DefaultShadowOffset := option. vector2:
			X := 2.000000
			Y := 2.000000
		DefaultShadowColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
	Image_5 :texture_block= texture_block:
		DefaultImage := VGiga.Assets.GigaTextures.T_horizontal_background_fade_notifs_ui
		DefaultTint := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
		DefaultDesiredSize := vector2:
			X := 350.000000
			Y := 50.000000
	Overlay_5 :overlay= overlay:
		Slots := array:
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Top
				Widget := Image_5
			overlay_slot:
				Padding := margin:
					Left := 24.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := TextRes_5
			overlay_slot:
				Padding := margin:
					Left := 80.000000
					Right := 30.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := TextFull_5
			overlay_slot:
				Padding := margin:
					Left := 24.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := ImageGold_5
	ImageGold_4 :texture_block= texture_block:
		DefaultImage := VResourcesSystem.Assets.TTextures.T_GoldCoin
		DefaultDesiredSize := vector2:
			X := 38.000000
			Y := 38.000000
	TextFull_4 :text_block= text_block:
		DefaultText := TextFull_4TextVar
		DefaultTextColor := color:
			R := 0.947917
			G := 0.947917
			B := 0.947917
		DefaultTextOpacity := 1.000000
		DefaultShadowOffset := option. vector2:
			X := 2.000000
			Y := 2.000000
		DefaultShadowColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
	TextRes_4 :text_block= text_block:
		DefaultText := TextRes_4TextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
		DefaultShadowOffset := option. vector2:
			X := 2.000000
			Y := 2.000000
		DefaultShadowColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
	Image_4 :texture_block= texture_block:
		DefaultImage := VGiga.Assets.GigaTextures.T_horizontal_background_fade_notifs_ui
		DefaultTint := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
		DefaultDesiredSize := vector2:
			X := 350.000000
			Y := 50.000000
	Overlay_4 :overlay= overlay:
		Slots := array:
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Top
				Widget := Image_4
			overlay_slot:
				Padding := margin:
					Left := 24.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := TextRes_4
			overlay_slot:
				Padding := margin:
					Left := 80.000000
					Right := 30.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := TextFull_4
			overlay_slot:
				Padding := margin:
					Left := 24.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := ImageGold_4
	ImageGold_3 :texture_block= texture_block:
		DefaultImage := VResourcesSystem.Assets.TTextures.T_GoldCoin
		DefaultDesiredSize := vector2:
			X := 38.000000
			Y := 38.000000
	TextFull_3 :text_block= text_block:
		DefaultText := TextFull_3TextVar
		DefaultTextColor := color:
			R := 0.947917
			G := 0.947917
			B := 0.947917
		DefaultTextOpacity := 1.000000
		DefaultShadowOffset := option. vector2:
			X := 2.000000
			Y := 2.000000
		DefaultShadowColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
	TextRes_3 :text_block= text_block:
		DefaultText := TextRes_3TextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
		DefaultShadowOffset := option. vector2:
			X := 2.000000
			Y := 2.000000
		DefaultShadowColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
	Image_3 :texture_block= texture_block:
		DefaultImage := VGiga.Assets.GigaTextures.T_horizontal_background_fade_notifs_ui
		DefaultTint := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
		DefaultDesiredSize := vector2:
			X := 350.000000
			Y := 50.000000
	Overlay_3 :overlay= overlay:
		Slots := array:
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Top
				Widget := Image_3
			overlay_slot:
				Padding := margin:
					Left := 24.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := TextRes_3
			overlay_slot:
				Padding := margin:
					Left := 80.000000
					Right := 30.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := TextFull_3
			overlay_slot:
				Padding := margin:
					Left := 24.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := ImageGold_3
	ImageGold_2 :texture_block= texture_block:
		DefaultImage := VResourcesSystem.Assets.TTextures.T_GoldCoin
		DefaultDesiredSize := vector2:
			X := 38.000000
			Y := 38.000000
	TextFull_2 :text_block= text_block:
		DefaultText := TextFull_2TextVar
		DefaultTextColor := color:
			R := 0.947917
			G := 0.947917
			B := 0.947917
		DefaultTextOpacity := 1.000000
		DefaultShadowOffset := option. vector2:
			X := 2.000000
			Y := 2.000000
		DefaultShadowColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
	TextRes_2 :text_block= text_block:
		DefaultText := TextRes_2TextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
		DefaultShadowOffset := option. vector2:
			X := 2.000000
			Y := 2.000000
		DefaultShadowColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
	Image_2 :texture_block= texture_block:
		DefaultImage := VGiga.Assets.GigaTextures.T_horizontal_background_fade_notifs_ui
		DefaultTint := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
		DefaultDesiredSize := vector2:
			X := 350.000000
			Y := 50.000000
	Overlay_2 :overlay= overlay:
		Slots := array:
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Top
				Widget := Image_2
			overlay_slot:
				Padding := margin:
					Left := 24.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := TextRes_2
			overlay_slot:
				Padding := margin:
					Left := 80.000000
					Right := 30.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := TextFull_2
			overlay_slot:
				Padding := margin:
					Left := 24.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := ImageGold_2
	ImageGold_1 :texture_block= texture_block:
		DefaultImage := VResourcesSystem.Assets.TTextures.T_GoldCoin
		DefaultDesiredSize := vector2:
			X := 38.000000
			Y := 38.000000
	TextFull_1 :text_block= text_block:
		DefaultText := TextFull_1TextVar
		DefaultTextColor := color:
			R := 0.947917
			G := 0.947917
			B := 0.947917
		DefaultTextOpacity := 1.000000
		DefaultShadowOffset := option. vector2:
			X := 2.000000
			Y := 2.000000
		DefaultShadowColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
	TextRes_1 :text_block= text_block:
		DefaultText := TextRes_1TextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
		DefaultShadowOffset := option. vector2:
			X := 2.000000
			Y := 2.000000
		DefaultShadowColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
	Image_1 :texture_block= texture_block:
		DefaultImage := VGiga.Assets.GigaTextures.T_horizontal_background_fade_notifs_ui
		DefaultTint := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
		DefaultDesiredSize := vector2:
			X := 350.000000
			Y := 50.000000
	Overlay_1 :overlay= overlay:
		Slots := array:
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Top
				Widget := Image_1
			overlay_slot:
				Padding := margin:
					Left := 24.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := TextRes_1
			overlay_slot:
				Padding := margin:
					Left := 80.000000
					Right := 30.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := TextFull_1
			overlay_slot:
				Padding := margin:
					Left := 24.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := ImageGold_1
	TextFull :text_block= text_block:
		DefaultText := TextFullTextVar
		DefaultTextColor := color:
			R := 0.947917
			G := 0.947917
			B := 0.947917
		DefaultTextOpacity := 1.000000
		DefaultShadowOffset := option. vector2:
			X := 2.000000
			Y := 2.000000
		DefaultShadowColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
	TextRes :text_block= text_block:
		DefaultText := TextResTextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
		DefaultShadowOffset := option. vector2:
			X := 2.000000
			Y := 2.000000
		DefaultShadowColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
	ImageGold :texture_block= texture_block:
		DefaultImage := VResourcesSystem.Assets.TTextures.T_GoldCoin
		DefaultDesiredSize := vector2:
			X := 38.000000
			Y := 38.000000
	Image :texture_block= texture_block:
		DefaultImage := VGiga.Assets.GigaTextures.T_horizontal_background_fade_notifs_ui
		DefaultTint := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
		DefaultDesiredSize := vector2:
			X := 350.000000
			Y := 50.000000
	Overlay :overlay= overlay:
		Slots := array:
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Top
				Widget := Image
			overlay_slot:
				Padding := margin:
					Left := 24.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := ImageGold
			overlay_slot:
				Padding := margin:
					Left := 24.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := TextRes
			overlay_slot:
				Padding := margin:
					Left := 80.000000
					Right := 30.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := TextFull
	StackBox :stack_box= stack_box:
		Orientation := orientation.Vertical
		Slots := array:
			stack_box_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := Overlay
			stack_box_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := Overlay_1
			stack_box_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := Overlay_2
			stack_box_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := Overlay_3
			stack_box_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := Overlay_4
			stack_box_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := Overlay_5
			stack_box_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := Overlay_6
			stack_box_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := Overlay_7
	NotificationsFeed :canvas= canvas:
		Slots := array:
			canvas_slot:
				Offsets := margin:
					Left := 276.274292
					Top := -208.216217
					Right := 348.354370
					Bottom := 372.384369
				Anchors := anchors:
					Minimum := vector2:
						X := 0.500000
						Y := 0.500000
					Maximum := vector2:
						X := 0.500000
						Y := 0.500000
				SizeToContent := true
				Widget := StackBox


	TextFull_7.SetShadowOpacity(0.532000)
	TextRes_7.SetShadowOpacity(0.532000)
	TextFull_6.SetShadowOpacity(0.532000)
	TextRes_6.SetShadowOpacity(0.532000)
	TextFull_5.SetShadowOpacity(0.532000)
	TextRes_5.SetShadowOpacity(0.532000)
	TextFull_4.SetShadowOpacity(0.532000)
	TextRes_4.SetShadowOpacity(0.532000)
	TextFull_3.SetShadowOpacity(0.532000)
	TextRes_3.SetShadowOpacity(0.532000)
	TextFull_2.SetShadowOpacity(0.532000)
	TextRes_2.SetShadowOpacity(0.532000)
	TextFull_1.SetShadowOpacity(0.532000)
	TextRes_1.SetShadowOpacity(0.532000)
	TextFull.SetShadowOpacity(0.532000)
	TextRes.SetShadowOpacity(0.532000)
	notifications_feed_generated:
		ImageGold_7 := ImageGold_7
		TextFull_7 := TextFull_7
		TextRes_7 := TextRes_7
		Image_7 := Image_7
		Overlay_7 := Overlay_7
		ImageGold_6 := ImageGold_6
		TextFull_6 := TextFull_6
		TextRes_6 := TextRes_6
		Image_6 := Image_6
		Overlay_6 := Overlay_6
		ImageGold_5 := ImageGold_5
		TextFull_5 := TextFull_5
		TextRes_5 := TextRes_5
		Image_5 := Image_5
		Overlay_5 := Overlay_5
		ImageGold_4 := ImageGold_4
		TextFull_4 := TextFull_4
		TextRes_4 := TextRes_4
		Image_4 := Image_4
		Overlay_4 := Overlay_4
		ImageGold_3 := ImageGold_3
		TextFull_3 := TextFull_3
		TextRes_3 := TextRes_3
		Image_3 := Image_3
		Overlay_3 := Overlay_3
		ImageGold_2 := ImageGold_2
		TextFull_2 := TextFull_2
		TextRes_2 := TextRes_2
		Image_2 := Image_2
		Overlay_2 := Overlay_2
		ImageGold_1 := ImageGold_1
		TextFull_1 := TextFull_1
		TextRes_1 := TextRes_1
		Image_1 := Image_1
		Overlay_1 := Overlay_1
		TextFull := TextFull
		TextRes := TextRes
		ImageGold := ImageGold
		Image := Image
		Overlay := Overlay
		StackBox := StackBox
		NotificationsFeed := NotificationsFeed
