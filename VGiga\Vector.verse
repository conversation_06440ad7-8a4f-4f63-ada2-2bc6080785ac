using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }

(V:vector3).WithX<public>(Value:float)<transacts>:vector3={
    vector3{X := Value, Y := V.Y, Z := V.Z}
}
(V:vector3).WithY<public>(Value:float)<transacts>:vector3={
    vector3{X := V.X, Y := Value, Z := V.Z}
}
(V:vector3).WithZ<public>(Value:float)<transacts>:vector3={
    vector3{X := V.X, Y := V.Y, Z := Value}
}

# (Tag:tag).GetAll(ObjToCastTo:t where t:type):[]t=
# 	return for(I:GetCreativeObjectsWithTag(Tag), Casted := t[I]):
# 		Casted