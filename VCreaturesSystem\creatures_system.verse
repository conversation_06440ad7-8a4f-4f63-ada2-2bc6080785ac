
using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using {/Verse.org/Random}
using { VGiga }
using { VGiga.Defaults }
using{VQuestSystem}



creatures_system<public> := class(auto_creative_device, i_init):
	@editable AreaSpawnersDevice:area_box_devic = area_box_devic{}	
	
	var Container<public>:?player_events_manager_devic = false
	CreatureEliminatedEvent<public>:event(device_ai_interaction_result) = event(device_ai_interaction_result){}
	CreatureEliminatedByAgentEvent<public>:event(agent) = event(agent){}

	Init<override>(C:vcontainer):void=
		set Container = option. C.ResolveErr(player_events_manager_devic)
		CreatureSpawners := GetDevices(creature_spawner_device)
		for(Spawner:CreatureSpawners):
			Spawner.EliminatedEvent.Subscribe(OnCreatureEliminated)

		GuardSpawners := GetDevices(guard_spawner_device)
		for(Spawner:GuardSpawners):
			Spawner.EliminatedEvent.Subscribe(OnCreatureEliminated)
			# Spawner.SpawnedEvent.Subscribe(OnSpawnedEvent)
		# spawn. EnableSpawnersAsync(CreatureSpawners)
		Self

	# EnableSpawnersAsync(Spawners:[]creature_spawner_device)<suspends>:void=
		# Sleep(3.0)
		# for(Spawner:Spawners):
		# 	Spawner.Enable()
			

		

	# OnSpawnedEvent(Agent:agent):void={
	# 	spawn. OnSpawnedEventAsync(Agent)
	# }
	# OnSpawnedEventAsync(Agent:agent)<suspends>:void={
	# 	Fort := Agent.WaitForFortCharacterActive()
	# 	if. Fort.TeleportTo[Vector3(0.0,0.0,100.0), rotation{}]
	# }
	OnCreatureEliminated(Result:device_ai_interaction_result):void=
		CreatureEliminatedEvent.Signal(Result)
		if(Source := Result.Source?):
			CreatureEliminatedByAgentEvent.Signal(Source)
			Container.G().MakePlayerInCombat(Source)
		

	
	GetKillCreaturesQuestCompleter<public>():simple_quest_completer=
		simple_quest_completer:
			TypeId := "kill creature"
			Event := CreatureEliminatedByAgentEvent