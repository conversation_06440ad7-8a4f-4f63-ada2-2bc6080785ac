### normal device with manager WIT<PERSON> DATA
#gen
#device_cast
#DEVICENAME
#p_data

### normal device with manager NO DATA
#gen
#device_cast_nodata
#DEVICENAME

### safer map
#gen
#map_t1_t2
#agent
#p_data

### GetDeviceWithTag (or prop), GetDevicesInAreaWithTag
<#
upgradable_spawner_spawn_map_tag_vertical := class(tag_comparable):
	GetComparable<override>()<transacts>:comparable=
		"upgradable_spawnersmtv"
	
# usage:
# ItemGranters := GetDevicesInAreaWithTag(item_granter_device, Area, buttons_to_granters_tag{})
#>