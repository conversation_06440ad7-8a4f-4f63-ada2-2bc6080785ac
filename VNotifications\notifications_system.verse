using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
    
notifications_system<public> := class(auto_creative_device, i_init_per_player_async):
    @editable MTopMessageDevice:?hud_message_device = false
    @editable MBottomMessageDevice:?hud_message_device = false

    @editable CenterMessageDevice:?hud_message_device = false

    var TopTaken :logic= false
    var TopTaken2 :logic= false
    CancelTop2Event:event() := event(){}
    PlayersNotif:[player]notifications_ui = map{}

    ShowCenterNotification<public>(Agent:agent, Message:message, TimeToHide:float):void=
        if(Device := CenterMessageDevice?):
            # TopMessageDevice.Hide(Agent)
            # Sleep(0.0)
            Device.Show(Agent, Message, ?DisplayTime := TimeToHide)
        else:
            LErrorPrint("missing center msg device in notifications system device")
            
    ShowBigTopNotification<public>(Agent:agent, Message:message, TimeToHide:float):void=
        if(TopMessageDevice := MTopMessageDevice?):
            # TopMessageDevice.Hide(Agent)
            # Sleep(0.0)
            TopMessageDevice.Show(Agent, Message, ?DisplayTime := TimeToHide)
        else:
            LErrorPrint("missing top msg device in notifications system device")
    ShowBigBottomNotification<public>(Agent:agent, Message:message, TimeToHide:float):void=
        if(BottomMessageDevice := MBottomMessageDevice?):
            # TopMessageDevice.Hide(Agent)
            # Sleep(0.0)
            BottomMessageDevice.Show(Agent, Message, ?DisplayTime := TimeToHide)
        else:
            LErrorPrint("missing top msg device in notifications system device")
            
    # MakeMessage<localizes>(txt: string) : message = "{txt}"
    ShowTopNotificationEvents:map_agent_event_show_notif_data = map_agent_event_show_notif_data{}

    ShowPerisistentNotificationEvents:map_agent_event_show_notif_data_persistent = map_agent_event_show_notif_data_persistent{}

    ShowTopNotificationWithTime<public>(Agent:agent, Message:message, TimeToHide:float):void=
        ShowTopNotificationEvents.Signal(Agent, show_notif_data:
            Message :=Message
            TimeToHide := TimeToHide
        )
        
    ShowTopNotification<public>(Agent:agent, Message:message):void=
        ShowTopNotificationEvents.Signal(Agent, show_notif_data_c(Message))

    ShowPerisistentNotificationDisposable<public>(Agent:agent, Message:message):i_disposable=
        DisposeEvent := event(){}
        ShowPerisistentNotificationEvents.Signal(Agent, show_notif_data_persistent_c(Message, DisposeEvent))
        disposable_as_event_c(DisposeEvent)

    InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
        Print("notifications_system InitPlayerAsync"); Sleep(0.0)
        ShowTopNotificationEvent := ShowTopNotificationEvents.Add(Agent)
        ShowPerisistentNotificationEvent := ShowPerisistentNotificationEvents.Add(Agent)
        NotifUi := notifications_ui_c(Agent)

        Agent.AddToPlayerUi(NotifUi.Canvas)
        race:
            PlayerRemoved.Await()
            loop:
                Data := ShowTopNotificationEvent.Await()
                if(not TopTaken?):
                    spawn. ShowAndHideTopNotif(NotifUi, Data)
                else:
                    spawn. ShowAndHideTopNotif2(NotifUi, Data)
            block:
                var Data:show_notif_data_persistent = 
                    ShowPerisistentNotificationEvent.Await()
                loop:
                    ShowPersistentNotif(NotifUi, Data)
                    var MNewData :?show_notif_data_persistent = false
                    set MNewData = race:
                        Sleep(300.0)
                            .ToFalse(MNewData)
                        Data.DisposeEvent.Await()
                            .ToFalse(MNewData)
                        ShowPerisistentNotificationEvent.Await()
                            .ToOption()
                    
                    if(NewData := MNewData?):
                        set Data = NewData
                    else:
                        NotifUi.OverlayBottomNotif.Hide()
                        set Data = ShowPerisistentNotificationEvent.Await()
                        
        
        Agent.RemoveFromPlayerUi(NotifUi.Canvas)

        ShowTopNotificationEvents.Remove(Agent)
        ShowPerisistentNotificationEvents.Remove(Agent)

    ShowAndHideTopNotif(NotifUi:notifications_ui, Data:show_notif_data)<suspends>:void=
        set TopTaken = true
        NotifUi.SetTopNotifText(Data.Message)
        NotifUi.TopNotifText.Show()
        Sleep(Data.TimeToHide)
        NotifUi.TopNotifText.Hide()
        set TopTaken = false

    ShowAndHideTopNotif2(NotifUi:notifications_ui, Data:show_notif_data)<suspends>:void=
        CancelTop2Event.Signal()
        set TopTaken2 = true
        NotifUi.SetTopNotifText2(Data.Message)
        NotifUi.TopNotifText2.Show()
        race:
            CancelTop2Event.Await()
            Sleep(Data.TimeToHide)
        NotifUi.TopNotifText2.Hide()
        set TopTaken2 = false

    ShowPersistentNotif(NotifUi:notifications_ui, Data:show_notif_data_persistent)<suspends>:void=
        NotifUi.SetBottomNotifText(Data.Message)
        NotifUi.OverlayBottomNotif.Show()


show_notif_data_c<public>(PMessage:message
)<transacts>:show_notif_data=
    show_notif_data:
        Message := PMessage

#gen
#con
#id-612330b8-d2ab-42b1-b3c1-1a6e7044e082
show_notif_data_persistent_c(PMessage:message,
    PDisposeEvent:event()
)<transacts>:show_notif_data_persistent=
    show_notif_data_persistent:
        Message := PMessage
        DisposeEvent := PDisposeEvent
#id-612330b8-d2ab-42b1-b3c1-1a6e7044e082
show_notif_data_persistent<public> := struct:
    Message<public>:message
    DisposeEvent<public>:event()

show_notif_data<public> := struct:
    Message<public>:message
    TimeToHide<public>:float = 4.0

notifications_ui_c(Agent:agent):notifications_ui=
    TopNoticationText := text_block:
        DefaultJustification := text_justification.Center
        DefaultTextColor := NamedColors.White
        DefaultShadowOffset := option. Vector2One
    
    TopNoticationText.SetShadowOpacity(1.0)


    TopNoticationText2 := text_block:
        DefaultJustification := text_justification.Center
        DefaultTextColor := NamedColors.Yellow
        DefaultShadowOffset := option. Vector2One
    
    TopNoticationText2.SetShadowOpacity(1.0)


    BottomNotifText := text_block:
        DefaultJustification := text_justification.Center
        DefaultTextColor := NamedColors.White
        DefaultShadowOffset := option. Vector2One
    
    BottomNotifText.SetShadowOpacity(1.0)


    OverlayBottomNotif := overlay:
        Slots := array:
            overlay_slot:
                HorizontalAlignment := horizontal_alignment.Fill
                VerticalAlignment := vertical_alignment.Fill
                Widget := color_block:
                    DefaultColor := NamedColors.Black
                    DefaultOpacity := 0.5
                    
            overlay_slot:
                HorizontalAlignment := horizontal_alignment.Fill
                VerticalAlignment := vertical_alignment.Center
                Widget := BottomNotifText
                Padding := margin:
                    Left := 30.0
                    Top := 30.0
                    Right := 30.0
                    Bottom := 30.0
    OverlayBottomNotif.Hide()

    Canvas := canvas:
        Slots := array:
            canvas_slot:
                Anchors := AnchorsPctSize(0.5,1.0, 0.44,0.1)
                Alignment := Vector2(0.5,0.5)
                Widget := TopNoticationText
            canvas_slot:
                Anchors := AnchorsPctSize(0.5,1.0, 0.56,0.1)
                Alignment := Vector2(0.5,0.5)
                Widget := TopNoticationText2
            canvas_slot:
                Anchors := AnchorsPctSize(0.5,1.0, 0.8,0.09)
                Alignment := Vector2(0.5,0.5)
                Widget := OverlayBottomNotif

    XMax := 8
    TextBlocks := for(
            X:=0..XMax
            Y:=0..XMax
            Start := 0.25
            ToFill := 1.0 - Start
            FillPerElement := ToFill/(XMax + 1)
        ):
            TextBlock := text_block:
                DefaultJustification := text_justification.Center
                DefaultTextColor := NamedColors.White

            TextBlock.SetShadowOpacity(1.0)

            Canvas.AddWidget(canvas_slot:
                Anchors := AnchorsPctSize of:
                    Start + FillPerElement * X,
                    0.1,
                    Start + FillPerElement * Y,
                    0.1
                Alignment := Vector2(0.5,0.5)
                Widget := TextBlock
            )

            tuple_text_float_c(TextBlock, -1.0)
        
    notifications_ui:
        Canvas := Canvas
        TopNotifText := TopNoticationText
        TopNotifText2 := TopNoticationText2
        BottomNotifText := BottomNotifText
        OverlayBottomNotif := OverlayBottomNotif
        Agent := Agent
        RandomTextBlocksWithHideTime := TextBlocks 

#gen
#con
#id-b7f79c74-b657-4f5d-9965-b45c67994292
tuple_text_float_c(PTextBlock:text_block,
    PHideTime:float
)<transacts>:tuple_text_float=
    tuple_text_float:
        TextBlock := PTextBlock
        HideTime := PHideTime
#id-b7f79c74-b657-4f5d-9965-b45c67994292
tuple_text_float := class:
    TextBlock:text_block
    var HideTime:float

notifications_ui := class:
    Canvas:canvas
    TopNotifText:text_block
    TopNotifText2:text_block
    BottomNotifText:text_block
    OverlayBottomNotif:overlay
    RandomTextBlocksWithHideTime:[]tuple_text_float
    Agent:agent

    SetTopNotifText(Message:message):void=
        # TextMoneyGain.SetText("+{}% {Loc.G(Agent, "MONEY/FANS GAIN")}".ToMessage())
        TopNotifText.SetText(Message)
        
    SetTopNotifText2(Message:message):void=
        TopNotifText2.SetText(Message)

    SetBottomNotifText(Message:message):void=
        BottomNotifText.SetText(Message)

    SetRandomTextBlock(Message:message, HideTime:float):void=
        if(RandText := RandomTextBlocksWithHideTime.GetRandom[]):
            set RandText.HideTime = HideTime
            RandText.TextBlock.SetText(Message)

#gen
#map_t1_event_t2
#agent
#show_notif_data_persistent
#id-9b5b0dc4-1461-4012-9d1d-b030f11c90a5
map_agent_event_show_notif_data_persistent<public> := class():
    var Events:[agent]event(show_notif_data_persistent) = map{}

    Signal<public>(Key:agent, Val:show_notif_data_persistent):void=
        if(Ev := Events[Key]):
            Ev.Signal(Val)
        else. LError()

    Get<public>(Key:agent)<decides><transacts>:event(show_notif_data_persistent)=
        if(Ev := Events[Key]):
            Ev
        else:
            FailError[]
            Err()

    Add<public>(Key:agent)<transacts>:event(show_notif_data_persistent)=
        if(Ev2 := Events[Key]):
            LError()
            return Ev2
        Ev := event(show_notif_data_persistent){}
        if. set Events[Key] = Ev
        Ev

    Add<public>(Key:agent, Ev:event(show_notif_data_persistent))<transacts>:void=
        if(Ev2 := Events[Key]):
            LError()
        else:
            if. set Events[Key] = Ev

    Remove<public>(Key:agent)<transacts>:void=
        set Events = Events.WithRemoved(Key)
#id-9b5b0dc4-1461-4012-9d1d-b030f11c90a5

#gen
#map_t1_event_t2
#agent
#show_notif_data
#id-70bb854a-f503-4f16-ac65-9786d0d3555b
map_agent_event_show_notif_data<public> := class():
    var Events:[agent]event(show_notif_data) = map{}

    Signal<public>(Key:agent, Val:show_notif_data):void=
        if(Ev := Events[Key]):
            Ev.Signal(Val)
        else. LError()

    Get<public>(Key:agent)<decides><transacts>:event(show_notif_data)=
        if(Ev := Events[Key]):
            Ev
        else:
            FailError[]
            Err()

    Add<public>(Key:agent)<transacts>:event(show_notif_data)=
        if(Ev2 := Events[Key]):
            LError()
            return Ev2
        Ev := event(show_notif_data){}
        if. set Events[Key] = Ev
        Ev

    Add<public>(Key:agent, Ev:event(show_notif_data))<transacts>:void=
        if(Ev2 := Events[Key]):
            LError()
        else:
            if. set Events[Key] = Ev

    Remove<public>(Key:agent)<transacts>:void=
        set Events = Events.WithRemoved(Key)
#id-70bb854a-f503-4f16-ac65-9786d0d3555b