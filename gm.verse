using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }
using { VMatchmakingDisabler }
using{VHouseSystem}
using { VMapCode }
using{VResourcesSystem}
using{VLocalization }
using{VGiga.Pool}
using{VPrefabs}
using{VQuestSystem}
using. VArrowSystem
using. VCinematic

gm_device := class(creative_device, i_init_per_player_async):
    @editable GmPrefabs:gm_prefabs_devic = gm_prefabs_devic{}
    @editable MGmQuests:?gm_quests_device = false

    var MyMap:[int]string = map{}
    var IncreasePlayerDmg :logic= false
    OnBegin<override>()<suspends>:void=
        Sleep(1.0)
        PrefabsBalance := gm_balance:
            GainRatio := 1.0

            #GainRatio := 4000.0
            # TestBalance := true

            Rewards := gm_balance_rewards:
                # TimeSec are used to also give points

                RaceParkourRatio := 0.4
                # TimeSec is in devices as tracks have different lengths (MEstimatedSecondsToFinishForReward)

                RaceParkourRatioCoin := 0.4
                # TimeSec is in devices as tracks have different lengths (MGoldRewardPerCoinTimeSec)

                GlassBridgeCompleteRatio := 0.4
                MGlassBridgeCompleteTimeSec := option. 150.0

                GlassBridgePlateRatio := 0.4
                MGlassBridgePlateTimeSec := option. 5.0
                # there are 9 plates, reward is much bigger, 5 * 9 = 45

                PiggyRatio := 0.33
                PiggyTimeSec := 10.0 # but always 0 points

                FruitRatio := 0.8
                FruitTimeSec := 4.0 # but always 1 point

                HitOreRatio := 0.3
                HitOreTimeSec := 0.5  # but always 0 points

        Container := GmPrefabs.Container
        set IncreasePlayerDmg = false
        Container.Register(cinematic_players_config:
            #SkipCinematics := true
            SkipCinematics := false
        )

        set PrefabsBalance.HouseBalance = houses_balance:
            # TestShowRebirthInstantly := true
            # TestAutoUnlock := true
            
            Presets := gm_balance_houses{}.HouseBalance.Presets
            ShowArrowToCaptureHouseAtPlayerStart := false
            
        Container.Register(delete_save_buttons{})
        # set PrefabsBalance.HouseBalance = houses_balance:
        # 	TestShowRebirthInstantly := false
        # 	Presets := gm_balance_houses{}.HouseBalance.Presets
        # 	ShowArrowToCaptureHouseAtPlayerStart := false

        # PrefabsBalance.FillBalanceSpawnersFromHouse(
        # 	SpawnDelayInUefn := array{4.0, 3.0, 3.0, 3.0, 3.0}, #this is from spawners
        # 	# ArrayToPrefixHouseGains := array{10} #insert first zombie gain here
        # )
        # set PrefabsBalance.HouseBalance.SpawnInHouseAtStart = true
        set PrefabsBalance.ResourcesManagerBalance.FansEnabled = false
        # Ev := event(agent){}
        # set PrefabsBalance.QuestBalance.Quests = array{quest_data{
        # 	MaxProgress := 1
        # 	CompleteEvent := option. Ev
        # 	Id := "test"
        # 	Name := "quest name"
        # 	GoldReward := 5
        # }}
        # spawn. FinishTestQuest(Ev)

        if(GmQuests := MGmQuests?):
            GmQuests.Init(PrefabsBalance)
        
        set PrefabsBalance.MatchmakingDisableAfterMinutes = 60.0 * 2000 + 30.0
        # if. PrefabsBalance.QuestBalance.Quests[0].MaxProgress
        LocalizationMap:= gm_localization{} # https://docs.google.com/spreadsheets/d/1duvTb2ugmwjYyreimR5glU4RthyKiV-cXiZTZFtwUEg/edit?usp=sharing
        GmPrefabs.Container.Register(LocalizationMap)
        # localization is auto
        # localization_selection_ui is auto
        GmPrefabs.Container.Register(Self)
        GmPrefabs.Container.Register(notification_on_card_unlock{})
        MGmPref := GmPrefabs.InitPrefabsAsync(PrefabsBalance)


        # Texttt :string= "ą"
        
        # for(X->Char:Texttt):
        # 	if. case(Char):
        # 			0oc4 => block:
        # 				LPrint("OKKKK")
        # 				Char2 := Texttt[X+1]
        # 				LPrint("OKKKK 2")
        # 				case(Char2):
        # 					0o84 => LPrint("84")
        # 					0o85 => LPrint("85")
        # 					_ => LPrint("Not found")
        # 	Print("{X}")
        # 	Print("{Char = 0oC4 and "Char = 0oC4" or "Char <> 0oC4"}")
        # 	Print("{Char = 0oc4 and "Char = 0oc4" or "Char <> 0oc4"}")
        # 	Print("{Char = 0o85 and "Char = 0o85" or "Char <> 0o85"}")

        if(GmPref := MGmPref.VError[]):
            # gm:
            # 	GmPrefabs := GmPref
            # 	D := Self
            # .InitAsync(GmPref.Container)
            
            GmPref.InitAsync() # HAS TO BE LAST, inits container


    InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
        Print("GM InitPlayerAsync"); Sleep(0.0)
    # HandleQuestsOnHouseClaimed(GmPref:gm_prefabs)<suspends>:void=
        Container := GmPrefabs.Container
        if(IncreasePlayerDmg?):
            Increaser := Container.Resolve[infinite_increase_player_damage] or Err()
            Increaser.ApplyToAgent(Agent, 1000.0)
        # QuestsSystem := Container.ResolveErr(quest_system)
        # Houses := Container.ResolveErr(houses_manager)
        # ArrowSystem := Container.ResolveErr(arrow_system)
        # Phone := Container.ResolveErr(VPhone.phones)

notification_on_card_unlock := class(i_init_async):
    CardUnlockedMessage<localizes>:message = "Wzór karty odblokowana w banku!"

    InitAsync<override>(Container:vcontainer)<suspends>:void=
        HousesManager := Container.Resolve[houses_manager] or Err()
        Notifications := Container.Resolve[VNotifications.notifications_system] or Err()
        Coop := Container.Resolve[VCoop.coop] or Err()
        PlayerEvents := Container.Resolve[player_events_manager_devic] or Err()

        loop:
            UnlockData := HousesManager.UnlockableUnlockedEvent.Await()
            if(UnlockData.UnlockableId.Contains["card"]):
                if(Coop.IsCoop[]):
                    for(Player->Data:PlayerEvents.RegisteredPlayers):
                            Notifications.ShowCenterNotification(Player, CardUnlockedMessage, 5.0)
                else:
                    Notifications.ShowCenterNotification(UnlockData.Agent, CardUnlockedMessage, 5.0)

            
        # Resources := Container.ResolveErr(VResourcesSystem.resources_manager)
        # Resources.GiveGold(Agent, ********)


        # # sync:
        # 		block:  
        # 			Houses.HouseClaimedEvent.AwaitFor(Agent)
        # 			Sleep(0.1)
        # 			loop:
        # 				Quest := QuestsSystem.QuestGotEv.Await()
        # 				if(Quest.Agent = Agent):
        # 					QData := Quest.Data

        # 					if(QData.TypeId = "register account" or QData.TypeId = "make deposit"):
        # 						if(not Houses.GetAgentHouse[Agent]):
        # 							Houses.HouseClaimedEvent.AwaitFor(Agent)
        # 						if(House := Houses.GetAgentHouse[Agent]):
        # 							PcLocation := House.PcTriggerInitTr.Translation
        # 							MArrow := ArrowSystem.ShowArrow(Agent, PcLocation)
        # 							spawn. QuestFinishedDisposeArrow(MArrow, PlayerRemoved, Quest.QuestFinishedEvent)
        # 						else:
        # 							LError()

    QuestFinishedDisposeArrow(MArrow:?i_disposable, PlayerRemoved:event(), QuestFinishedEvent:event())<suspends>:void=
        race:
            PlayerRemoved.Await()
            QuestFinishedEvent.Await()
        if(Arrow := MArrow?):
            Arrow.Dispose()
        
        
    # HandlePlayerQuestArrow(Quest:active_quest_data, ArrowSystem:arrow_system, PlayerRemoved:event())<suspends>:void=
    # 	QData := Quest.Data
    # 	if(QData.TypeId = "register account" or QData.TypeId = "make deposit"):
    # 		House := Houses.GetAgentHouse[Agent] or block:
    # 			Houses.HouseClaimedEvent.AwaitFor(Agent)

# quests_guide := class:

        


        # Events:[]event() = for(X := 0..10){
        # 	event(){}
        # }

        # sync{
        # 	block{
        # 		Sleep(10.0)
        # 		if(Event := Events.GetRandom[]){
        # 			Event.Signal()
        # 		}
        # 	}
        # 	Events.Race()
        # }
        # var Arr :[]type{_()<suspends>:int}= for(X := 0..10){
        # 	PrintSleep
        # }
        # set Arr += array. EndIn10

        # Arr2 :[]int= for(X := 0..10){
        # 	1
        # }

        # Arr3 :[](int->int)= for(X := 0..10){
        # 	GetVoid
        # }

        # for(group)
        # TT :type{_()<suspends>:void} = PrintSleep
        # Arr.RaceMethods()
    

# 	FinishTestQuest(Ev:event(agent))<suspends>:void={
# 		Sleep(600.0)
# 		for(Player:GetPlayspace().GetPlayers()){
# 			Ev.Signal(Player)
# 		}
# 	}

# 	EndIn10()<suspends>:int={
# 		Sleep(10.0)
# 		2
# 	}

# 	PrintSleep()<suspends>:int={
# 		loop{
# 			Sleep(2.0)
# 			Print("YOO")
# 		}
# 		1
# 	}
# }

# gm := class(i_init_per_player_async){
# 	GmPrefabs:gm_prefabs
#     D:gm_device

#     InitAsync(Container:player_events_manager_devic)<suspends>:void={
# 		Container.Register(Self)
#     }

# 	InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
# 		# if(GrantFromKills := GmPrefabs.MGrantFromKills?):
# 			# GrantFromKills.SetGoldPerCreatureKill(Agent, 10)
# 		PlayerRemoved.Await()
# }
