using { /Fortnite.com/Devices }
using { /Verse.org/Simulation }
using { /UnrealEngine.com/Temporary/Diagnostics }
using { /Verse.org/Random }
using {VGiga}
using { /UnrealEngine.com/Temporary/SpatialMath }


prop_spawner<public> := class(auto_creative_device, i_init):
	Init<override>(Container:vcontainer):void=
		FreeSpawnersDevices := GetDevices(spawner_devic_for_pool)
		if(FreeSpawnersDevices.Length = 0):
			LError()
		
		LPrint("FreeSpawners: {FreeSpawnersDevices.Length}")

		set FreeSpawners = for(I->Obj:FreeSpawnersDevices):
			Obj.Init(empty_logger{})
		
	var FreeSpawners : []spawner_for_pool = array{}

	DespawnAll()<suspends>:void=
		for(I->Spawner:FreeSpawners):
			Spawner.DespawnAll()
			if(Mod[I,50] = 0):
				Sleep(0.0)

	Spawn<public>(Asset:creative_prop_asset, Position:vector3)<suspends>:?creative_prop_unique=
		return Spawn(transform{Translation := Position, Rotation := rotation{}, Scale := Vector3One}, Asset)

	Spawn<public>(Asset:creative_prop_asset, Position:vector3, Rotation:rotation)<suspends>:?creative_prop_unique=
		return Spawn(transform{Translation := Position, Rotation := Rotation, Scale := Vector3One}, Asset)
	
	Spawn<public>(Asset:creative_prop_asset, Tran:transform)<suspends>:?creative_prop_unique=
		return Spawn(Tran, Asset)

	Spawn<private>(Transform:transform, Asset:creative_prop_asset)<suspends>:?creative_prop_unique=
		# Logger.Log("FreeSpawners.Length {FreeSpawners.Length}")
		if(Spawner := FreeSpawners[FreeSpawners.Length - 1]):
			# LPrint("SPAWN {Spawner.SpawnedCount}")
			set Spawner.SpawnedCount += 1
			if{Spawner.IsSpawnerFull[]
				Val := FreeSpawners.RemoveElement[FreeSpawners.Length - 1]
				set FreeSpawners = Val
			}
			MaybeProp := Spawner.Spawn(Asset, Transform)
			if(Prop := MaybeProp?):
				return option. creative_prop_unique{Transform := Transform, Asset := Asset, Spawner := Spawner, Prop := Prop, PropSpawner := Self}
			
		else:
			LError()
			LPrint("Run out of the teleportes pool!")
		
		return false

	
	DisposeSpawned<public>(Prop:creative_prop_unique):void=
		Spawner := Prop.Spawner
		# if(not Prop.Prop.IsValid[]):
		# 	LErrorPrint("Prop not valid, already disposed")
			
		if(Prop.Prop.IsValid[]):
			Prop.Prop.Dispose()
		
		# this is correct
		# if spawner is full then removing just one item makes it free
		if(Spawner.IsSpawnerFull[]):
			set FreeSpawners += array{Spawner}
			
		set Spawner.SpawnedCount -= 1
		# if(Mod[Spawner.SpawnedCount,10] = 0){
		#     LPrint("DisposeSpawned, Count: {Spawner.SpawnedCount}")
		# }
	# RespawnSpawned(Unique:creative_prop_unique):logic={
	#     if(Unique.Prop.IsValid[]){
	#         return false
	#     }else{
	#         Unique.Prop.Dispose()
	#         Result := SpawnProp(Unique.Asset, Unique.Transform)
	#         if(Prop := Result(0)?){
	#             set Unique.Prop = Prop
	#             return true
	#         }else{
	#             PrintSpawnPropResult(Result(1))
	#             LError() 
	#             return false 
	#         }
	#     }
	# }

	# RespawnSpawned(Prefab:props_manager):logic={
	#     var AnyRespawned:logic = false 
	#     if(Uniques := Prefab.CreativePropUnique?){
	#         for(Unique:Uniques){
	#             Respawned := RespawnSpawned(Unique)
	#             if(Respawned?){
	#                 set AnyRespawned = true
	#             }
	#         }q
	#     }
	#     AnyRespawned
	# }

creative_prop_unique<public> := class<unique>(i_disposable):
	var Prop<public>:creative_prop = creative_prop{}
	var Asset:creative_prop_asset = DefaultCreativePropAsset
	PropSpawner<public>:prop_spawner
	var Spawner<public>:spawner_for_pool
	var Transform <public>:transform = transform{}

	Dispose<override>():void=
		PropSpawner.DisposeSpawned(Self)

	IsValid<public>()<decides><transacts>:void=
		Prop.IsValid[]

# (MProp:?creative_prop_unique).PrintResult():void=
# 	if(Prop = Prop?)