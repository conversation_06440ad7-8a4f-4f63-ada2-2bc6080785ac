using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/UI
using. /Fortnite.com/UI
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
phone_achievements_generated := class:
	ButtonMore:button_quiet
	Image_111ColorBlock:color_block
	Overlay_87:overlay
	ButtonExitt:button_quiet
	Image_69ColorBlock:color_block
	Overlay_329:overlay
	ImageDone:texture_block
	TextReward:text_block
	Image_378:texture_block
	StackBoxReward:stack_box
	TextQuestDesc:text_block
	Image_159:texture_block
	SingleQuestOverlay:overlay
	StackBoxQuestsList:stack_box
	Image_67:texture_block
	Overlay_50:overlay
	PhoneAchievements:canvas

ButtonMoreTextVar<localizes>:message =  ""
ButtonExittTextVar<localizes>:message = ""
TextRewardTextVar<localizes>:message =  "1000"
TextQuestDescTextVar<localizes>:message =  "Claim your first Bank!"
make_phone_achievements_generated():phone_achievements_generated=

	ButtonMore :button_quiet= button_quiet:
		DefaultText := ButtonMoreTextVar
	Image_111ColorBlock :color_block= color_block:
		DefaultColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultOpacity := 0.000000
		DefaultDesiredSize := vector2:
			X := 334.174744
			Y := 0.010000
	Overlay_87 :overlay= overlay:
		Slots := array:
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := Image_111ColorBlock
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := ButtonMore
	ButtonExitt :button_quiet= button_quiet:
		DefaultText := ButtonExittTextVar
	Image_69ColorBlock :color_block= color_block:
		DefaultColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
		DefaultOpacity := 0.000000
		DefaultDesiredSize := vector2:
			X := 70.562408
			Y := 70.562408
	Overlay_329 :overlay= overlay:
		Slots := array:
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Top
				Widget := Image_69ColorBlock
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Top
				Widget := ButtonExitt
	ImageDone :texture_block= texture_block:
		DefaultImage := VPhone.Assets.VTextures.T_PhoneQuestDone
		DefaultDesiredSize := vector2:
			X := 256.000000
			Y := 64.000000
	TextReward :text_block= text_block:
		DefaultText := TextRewardTextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
	Image_378 :texture_block= texture_block:
		DefaultImage := VResourcesSystem.Assets.TTextures.T_GoldCoin
		DefaultDesiredSize := vector2:
			X := 40.000000
			Y := 40.000000
	StackBoxReward :stack_box= stack_box:
		Orientation := orientation.Horizontal
		Slots := array:
			stack_box_slot:
				HorizontalAlignment := horizontal_alignment.Right
				VerticalAlignment := vertical_alignment.Bottom
				Widget := Image_378
			stack_box_slot:
				HorizontalAlignment := horizontal_alignment.Right
				VerticalAlignment := vertical_alignment.Bottom
				Widget := TextReward
	TextQuestDesc :text_block= text_block:
		DefaultText := TextQuestDescTextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
	Image_159 :texture_block= texture_block:
		DefaultImage := VPhone.Assets.VTextures.T_PhoneQuestBg
		DefaultDesiredSize := vector2:
			X := 512.000000
			Y := 128.000000
	SingleQuestOverlay :overlay= overlay:
		Slots := array:
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := Image_159
			overlay_slot:
				Padding := margin:
					Left := 77.000000
					Top := 22.000000
					Right := 30.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Top
				Widget := TextQuestDesc
			overlay_slot:
				Padding := margin:
					Right := 75.000000
					Bottom := 18.000000
				HorizontalAlignment := horizontal_alignment.Right
				VerticalAlignment := vertical_alignment.Bottom
				Widget := StackBoxReward
			overlay_slot:
				Padding := margin:
					Right := 12.000000
					Bottom := 10.000000
				HorizontalAlignment := horizontal_alignment.Right
				VerticalAlignment := vertical_alignment.Bottom
				Widget := ImageDone
	StackBoxQuestsList :stack_box= stack_box:
		Orientation := orientation.Vertical
		Slots := array:
			stack_box_slot:
				Padding := margin:
					Bottom := -10.000000
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := SingleQuestOverlay
	Image_67 :texture_block= texture_block:
		DefaultImage := VPhone.Assets.VTextures.T_Achievements
		DefaultDesiredSize := vector2:
			X := 512.000000
			Y := 1024.000000
	Overlay_50 :overlay= overlay:
		Slots := array:
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := Image_67
			overlay_slot:
				Padding := margin:
					Top := 170.000000
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Top
				Widget := StackBoxQuestsList
			overlay_slot:
				Padding := margin:
					Top := 100.000000
					Right := 51.000000
				HorizontalAlignment := horizontal_alignment.Right
				VerticalAlignment := vertical_alignment.Top
				Widget := Overlay_329
			overlay_slot:
				Padding := margin:
					Bottom := 180.000000
				HorizontalAlignment := horizontal_alignment.Center
				VerticalAlignment := vertical_alignment.Bottom
				Widget := Overlay_87
	PhoneAchievements :canvas= canvas:
		Slots := array:
			canvas_slot:
				Alignment := vector2:
					X := 0.500000
					Y := 0.500000
				Offsets := margin:
					Right := 512.000000
					Bottom := 1024.000000
				Anchors := anchors:
					Minimum := vector2:
						X := 0.500000
						Y := 0.500000
					Maximum := vector2:
						X := 0.500000
						Y := 0.500000
				SizeToContent := false
				Widget := Overlay_50


	phone_achievements_generated:
		ButtonMore := ButtonMore
		Image_111ColorBlock := Image_111ColorBlock
		Overlay_87 := Overlay_87
		ButtonExitt := ButtonExitt
		Image_69ColorBlock := Image_69ColorBlock
		Overlay_329 := Overlay_329
		ImageDone := ImageDone
		TextReward := TextReward
		Image_378 := Image_378
		StackBoxReward := StackBoxReward
		TextQuestDesc := TextQuestDesc
		Image_159 := Image_159
		SingleQuestOverlay := SingleQuestOverlay
		StackBoxQuestsList := StackBoxQuestsList
		Image_67 := Image_67
		Overlay_50 := Overlay_50
		PhoneAchievements := PhoneAchievements
