using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/UI
using. /Fortnite.com/UI
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
invest2_generated := class:
	ImageColorBlock:color_block
	TextInvestReturn:text_block
	BtnMoreTime:button_quiet
	TextTime:text_block
	BtnLessTime:button_quiet
	StackBox:stack_box
	BtnMoreCash:button_quiet
	TextCash:text_block
	BtnLessCash:button_quiet
	StackBox_355:stack_box
	StackBox_288:stack_box
	Image1ColorBlock:color_block
	ButtonInvest:button_quiet
	StackBox_119:stack_box
	ButtonExit:button_quiet
	Image_69ColorBlock:color_block
	Overlay_329:overlay
	Bg:texture_block
	Overlay_50:overlay
	invest2:canvas

TextInvestReturnTextVar<localizes>:message =  "Get $1500 back after 1 minute"
BtnMoreTimeTextVar<localizes>:message = ""
TextTimeTextVar<localizes>:message =  "1min"
BtnLessTimeTextVar<localizes>:message = ""
BtnMoreCashTextVar<localizes>:message = ""
TextCashTextVar<localizes>:message =  "$1000"
BtnLessCashTextVar<localizes>:message = ""
ButtonInvestTextVar<localizes>:message =  ""
ButtonExitTextVar<localizes>:message = ""
make_invest2_generated():invest2_generated=

	ImageColorBlock :color_block= color_block:
		DefaultColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
		DefaultOpacity := 0.001000
		DefaultDesiredSize := vector2:
			X := 334.174744
			Y := 2.178161
	TextInvestReturn :text_block= text_block:
		DefaultText := TextInvestReturnTextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
		DefaultJustification := text_justification.Center
	BtnMoreTime :button_quiet= button_quiet:
		DefaultText := BtnMoreTimeTextVar
	TextTime :text_block= text_block:
		DefaultText := TextTimeTextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
	BtnLessTime :button_quiet= button_quiet:
		DefaultText := BtnLessTimeTextVar
	StackBox :stack_box= stack_box:
		Orientation := orientation.Horizontal
		Slots := array:
			stack_box_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := BtnLessTime
			stack_box_slot:
				HorizontalAlignment := horizontal_alignment.Center
				VerticalAlignment := vertical_alignment.Center
				Distribution := option. 1.0 
				Widget := TextTime
			stack_box_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := BtnMoreTime
	BtnMoreCash :button_quiet= button_quiet:
		DefaultText := BtnMoreCashTextVar
	TextCash :text_block= text_block:
		DefaultText := TextCashTextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
	BtnLessCash :button_quiet= button_quiet:
		DefaultText := BtnLessCashTextVar
	StackBox_355 :stack_box= stack_box:
		Orientation := orientation.Horizontal
		Slots := array:
			stack_box_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := BtnLessCash
			stack_box_slot:
				HorizontalAlignment := horizontal_alignment.Center
				VerticalAlignment := vertical_alignment.Center
				Distribution := option. 1.0 
				Widget := TextCash
			stack_box_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := BtnMoreCash
	StackBox_288 :stack_box= stack_box:
		Orientation := orientation.Vertical
		Slots := array:
			stack_box_slot:
				Padding := margin:
					Bottom := 43.000000
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := StackBox_355
			stack_box_slot:
				Padding := margin:
					Bottom := 60.000000
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := StackBox
			stack_box_slot:
				Padding := margin:
					Top := 23.000000
				HorizontalAlignment := horizontal_alignment.Center
				VerticalAlignment := vertical_alignment.Top
				Widget := TextInvestReturn
			stack_box_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := ImageColorBlock
	Image1ColorBlock :color_block= color_block:
		DefaultColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
		DefaultOpacity := 0.001000
		DefaultDesiredSize := vector2:
			X := 334.174744
			Y := 1.810750
	ButtonInvest :button_quiet= button_quiet:
		DefaultText := ButtonInvestTextVar
	StackBox_119 :stack_box= stack_box:
		Orientation := orientation.Vertical
		Slots := array:
			stack_box_slot:
				Padding := margin:
					Bottom := 45.000000
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := ButtonInvest
			stack_box_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := Image1ColorBlock
	ButtonExit :button_quiet= button_quiet:
		DefaultText := ButtonExitTextVar
	Image_69ColorBlock :color_block= color_block:
		DefaultColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
		DefaultOpacity := 0.000000
		DefaultDesiredSize := vector2:
			X := 70.562408
			Y := 70.562408
	Overlay_329 :overlay= overlay:
		Slots := array:
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Top
				Widget := Image_69ColorBlock
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Top
				Widget := ButtonExit
	Bg :texture_block= texture_block:
		DefaultImage := VPhone.Assets.VTextures.T_Deposit2
		DefaultDesiredSize := vector2:
			X := 512.000000
			Y := 1024.000000
	Overlay_50 :overlay= overlay:
		Slots := array:
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := Bg
			overlay_slot:
				Padding := margin:
					Top := 100.000000
					Right := 51.000000
				HorizontalAlignment := horizontal_alignment.Right
				VerticalAlignment := vertical_alignment.Top
				Widget := Overlay_329
			overlay_slot:
				Padding := margin:
					Bottom := 170.000000
				HorizontalAlignment := horizontal_alignment.Center
				VerticalAlignment := vertical_alignment.Bottom
				Widget := StackBox_119
			overlay_slot:
				Padding := margin:
					Left := 89.000000
					Top := 380.000000
					Right := 89.000000
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Top
				Widget := StackBox_288
	invest2 :canvas= canvas:
		Slots := array:
			canvas_slot:
				Alignment := vector2:
					X := 0.500000
					Y := 0.500000
				Offsets := margin:
					Right := 512.000000
					Bottom := 1024.000000
				Anchors := anchors:
					Minimum := vector2:
						X := 0.500000
						Y := 0.500000
					Maximum := vector2:
						X := 0.500000
						Y := 0.500000
				SizeToContent := false
				Widget := Overlay_50


	invest2_generated:
		ImageColorBlock := ImageColorBlock
		TextInvestReturn := TextInvestReturn
		BtnMoreTime := BtnMoreTime
		TextTime := TextTime
		BtnLessTime := BtnLessTime
		StackBox := StackBox
		BtnMoreCash := BtnMoreCash
		TextCash := TextCash
		BtnLessCash := BtnLessCash
		StackBox_355 := StackBox_355
		StackBox_288 := StackBox_288
		Image1ColorBlock := Image1ColorBlock
		ButtonInvest := ButtonInvest
		StackBox_119 := StackBox_119
		ButtonExit := ButtonExit
		Image_69ColorBlock := Image_69ColorBlock
		Overlay_329 := Overlay_329
		Bg := Bg
		Overlay_50 := Overlay_50
		invest2 := invest2
