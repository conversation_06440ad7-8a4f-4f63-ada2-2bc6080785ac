using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga
using. VGiga2
using. VResourcesSystem
using. VCustomBillboard
using. VNotifications
using. VPopup

buyable := class(auto_creative_device, i_init_async):
    @editable Id:string = ""
    @editable TokenCost:int = 0
    @editable MBuildingParent:?creative_prop = false
    @editable MVfxActivateChannel:?channel_device = false
    @editable BuyVolume:?volume_device = false
    @editable MNameBillboard:?custom_billboard_editable = false
    @editable CostBillboard:?custom_billboard_editable = false
    # @editable MCosmeticVfx:?vfx_powerup_device= false

    var Resources:?resources_manager = false
    var MOwner:?agent = false

    BoughtEv:event(agent) = event(agent){}

    var Loc:i_localization = empty_i_localization{}
    var Popup:?popup = false

    IsCosmetic()<decides><transacts>:void=
        TokenCost > 0
    
    StartBankArr :arr_char= arr_char:
        Arr := "Start your first Bank!"
        
    InitAsync<override>(Container:vcontainer)<suspends>:void=
        set Loc = Container.Resolve_i_localization()
        set Popup = Container.ResolveOp[popup] or Err()
        Notifs := Container.Resolve[notifications_system] or Err()
        Cosmetic :logic= IsCosmetic[] and true or false
        set  Resources = Container.ResolveErrOp(resources_manager)
        if(Cosmetic?):
            MVfxActivateChannel.G()
        # else:
        # 	MBuildingParent.G()


        if(Id = ""):
            LErrorPrint("Building id not set")
        if(Balance := BuyablesBalance.Map[Id]):
            PlayerEvents := Container.ResolveErr(player_events_manager_devic)
            # InitTr :?transform= option. MBuildingParent.V[].GetTransform()
            # HideTr :?transform= option. InitTr?.WithTranslationZAndScale(-900.0, 0.001)

            InitTrTrigger := BuyVolume.G().GetTransform()
            HideTrTrigger := InitTrTrigger.WithTranslationZAndScale(-900.0, 0.001)
            # RewardBillboard.G().SetText("${Balance.GoldRewardPerSec}/s")
            if(NameBillboard := MNameBillboard?):
                NameBillboard.SetText(Id)
            if(Cosmetic?):
                # CostBillboard.G().SetTextGreen("#{Balance.CostTokens}")
                CostBillboard.G().SetTextGreen("#{TokenCost}")
            else:
                CostBillboard.G().SetTextGreen("${Balance.Cost.ToShortNumberString()}")

            var IncreasedCost:int = Balance.Cost
            loop:
                    # if. MBuildingParent.V[].TeleportTo[HideTr.V[]]
                    # if. BuyVolume.G().TeleportTo[InitTrTrigger]

                Agent := BuyVolume.G().AgentEntersEvent.Await()
                if(not MOwner? or Agent <> MOwner?):
                    # DoYouWantToBuyTr := Loc.G(Agent, DoYouWantToBuy)
                    # ForTr := Loc.G(Agent, For)
                    # AndGetTr := Loc.G(Agent, AndGet)
                    # TokensTr := Loc.G(Agent, Tokens)
                    Msg := DoYouWantToButForMsg(Loc, Agent, Id, Cosmetic, TokenCost, IncreasedCost,Balance.GoldRewardPerSec)
                    MOk := Popup.G().ShowOkCancel(Agent, Msg)

                    if(MOk?):
                        # TookResources := if(Cosmetic?):
                        # 	Resources.G().TryToTakeTokens(Agent, TokenCost)
                        # else:
                        TookResources := Resources.G().TryToTakeGold(Agent, IncreasedCost)

                        if(TookResources?):
                            BoughtEv.Signal(Agent)
                            if(Cosmetic?):
                                MVfxActivateChannel.G().Transmit(option. Agent)
                            else:
                                if(Owner := MOwner?
                                    player[Owner].IsActive[]
                                ):
                                    Resources.G().DecreaseGoldPerSec(Owner, Balance.GoldRewardPerSec)
                                    Resources.G().GiveGoldText(Owner, IncreasedCost, "{Id} {Loc.G(Agent,"biz sold")}")

                                if:
                                    Val := Floor[IncreasedCost * 1.2]
                                    set IncreasedCost = Val
                                CostBillboard.G().SetTextGreen("${IncreasedCost.ToShortNumberString()}")
                                Info := BoughtBusinessMsgPol(Id, Balance.GoldRewardPerSec)
                                    # if(Loc.IsEng[Agent]):
                                # 	BoughtBusinessMsg(Id, Balance.GoldRewardPerSec)
                                # else:
                                    # BoughtBusinessMsgPol(Id, Balance.GoldRewardPerSec)
                                Notifs.ShowTopNotification(Agent, Info)

                                # if. MBuildingParent.V[].TeleportTo[InitTr.V[]]
                                # if. BuyVolume.G().TeleportTo[HideTrTrigger]
                                # NameBillboard.G().SetText("")
                                # CostBillboard.G().SetText("")
                                Resources.G().IncreaseGoldPerSec(Agent, Balance.GoldRewardPerSec)
                                set MOwner = option. Agent
                                # Sleep(Inf)
                else:
                    Notifs.ShowTopNotification(Agent, ToMsg(Loc.G(Agent, "You already own this business!")))
        else:
            LErrorPrint("Balance for buyable: {Id} not found")

    # BoughtBusinessMsg<localizes>(BusinessName:string, Amount:int)<transacts>:message = "{BusinessName} business bought! +{Amount} Gold/sec"
    # BoughtBusinessMsgPol<localizes>(BusinessName:string, Amount:int)<transacts>:message = "{BusinessName} biznes kupiony! +${Amount}/sek"
    # BoughtBusinessMsg<localizes>(BusinessName:string, Amount:int)<transacts>:message = "Business bought! +{Amount} Gold/sec"
    BoughtBusinessMsgPol<localizes>(BusinessName:string, Amount:int)<transacts>:message = "Biznes kupiony! +${Amount}/sek"

DoYouWantToButForMsg(Loc:i_localization, Agent:agent, Id:string, Cosmetic:logic, Tokens:int, Cost:int, GoldPerSec:int):message = 
    # if(Loc.IsEng[Agent]):
    # 	ToMsg("Do you want to buy {Loc.G(Agent, Id)} for {Cosmetic? and "{Tokens} tokens"  or "${Cost} and earn ${GoldPerSec}/sec"}?")
    # else:
    # 	ToMsg("Czy chcesz kupić {Loc.G(Agent, Id)} za {Cosmetic? and "{Tokens} tokenów"  or "${Cost} i zarabiać ${GoldPerSec}/sek"}?")
    if(Loc.IsEng[Agent]):
        ToMsg("Do you want to buy {Cosmetic? and "cosmetic" or "business"} for {Cosmetic? and "{Tokens} tokens"  or "${Cost} and earn ${GoldPerSec}/sec"}?")
    else:
        ToMsg("Czy chcesz kupić {Cosmetic? and "kosmetyk" or "biznes"} za {Cosmetic? and "{Tokens} tokenów"  or "${Cost} i zarabiać ${GoldPerSec}/sek"}?")

# LocalizeMsg(Loc:localization, Agent:agent):message = 
# 	if(Loc.IsEng[Agent]):
# 		""
# 	else:
# 		""
        
    
    


# DoYouWantToBuy:arr_char=arr_char:
# 	Arr := "Do you want to buy"
# For:arr_char=arr_char:
# 	Arr := "for"
# AndGet:arr_char=arr_char:
# 	Arr := "and get"
# Tokens:arr_char=arr_char:
# 	Arr := "tokens"
    
    