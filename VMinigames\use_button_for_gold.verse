﻿using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Assets
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga
using. VResourcesSystem 
using. VLocalization
using. VAudio
using. VCustomBillboard

use_button_for_gold<public> := class(auto_creative_device, i_init_async, resetable):
	@editable Button:?button_device = false
	@editable CostBillboard:?custom_billboard_editable = false
	@editable InteractionDevice:?skilled_interaction_device = false
	# @editable ChannelFail:?channel_device = false

	PiggyBrokenEv<public>:event(agent) = event(agent){}
	
	var Resources:?resources_manager = false
	var Loc:i_localization = empty_i_localization{}
	var InitTr:transform = transform{}
	# var InitScale:vector3 = vector3{}
	var Audio:?audio = false
	var Balance :?gm_balance= false

	ResetEv:event() = event(){}
	Reset<override>():void=
		ResetEv.Signal()

	ShowEv:event() = event(){}
	Show<public>(Agent:agent):void=
		ShowEv.Signal()

	OnBegin<override>()<suspends>:void=
		Sleep(0.1)
		set InitTr = Button.G().GetTransform()
		InteractionDevice.G()
		# set InitScale = Button.G().GetTransform().Scale

	GetScaledTr(Param:float):transform=
		Scale := Lerp(InitTr.Scale.X, 2.0, Param)
		InitTr.WithScale(InitTr.Scale * Scale)

	# GetTr()<transacts>:transform=
	# 	Button.G().GetTransform()
		

	ScalePigAsync(Param:float)<suspends>:void=
		NewTr := GetScaledTr(Param)
		Button.G().MoveTo(NewTr, 0.3)

	ScalePig(Param:float):void=
		NewTr := GetScaledTr(Param)
		if. Button.G().TeleportTo[NewTr]

	SpawnGold(Agent:agent)<suspends>:void=
		Audio.G().GotCash.Play(Agent)
		Tr := GetScaledTr(1.0)
		Cancel := SpawnParticleSystem(Modele.Piggy.Blow_up_Piggy, Tr.Translation + Vector3Up * 100.0)
		Sleep(2.0)
		Cancel.Cancel()
		
		
	InitAsync<override>(Container:vcontainer)<suspends>:void=
		set Balance = Container.ResolveOp[gm_balance] or Err()
		set Audio = Container.ResolveOp[audio] or Err()
		set Loc = Container.Resolve_i_localization()
		set Resources = Container.ResolveOp[resources_manager] or Err()
		
		loop:
			if. Button.G().TeleportTo[InitTr.WithTranslationZ(-9999.0)]

			ShowEv.Await()
			race:
				ResetEv.Await()
				loop:
					spawn. ScalePigAsync(1.0)
					CostBillboard.G().SetText("!!!")
					var Agent :agent= Button.G().InteractedWithEvent.Await()
					if(InteractionDevice.G().BeginInteraction[Agent]):
						SkilledResult :?agent= race:
							block:
								Ag := InteractionDevice.G().InteractionSucceededEvent.Await()
								option. Ag
							block:
								Ag := InteractionDevice.G().InteractionFailedEvent.Await()
								false

						Sleep(0.1) # wait for popup to hide
						# SkilledResult := race: 
						# 	block:
						# 		MAg := ChannelGood.G().ReceivedTransmitEvent.Await()
						# 		(MAg, 0)
						# 	block:
						# 		MAg := ChannelPerfect.G().ReceivedTransmitEvent.Await()
						# 		(MAg, 1)
						if(Ag := SkilledResult?):
							set Agent = Ag
							spawn. SpawnGold(Agent)
							Resources.G().GiveGoldWithMultiplier(Agent, Balance.G().Rewards.PiggyTimeSec, 1.0 + Balance.G().Rewards.PiggyRatio, "Piggy", ?ShowNotification := true)
						
						if. Button.G().TeleportTo[InitTr.WithScale(InitTr.Scale * 0.01)]
						set Agent = FillingPiggyAsync(Agent)
						
	FillingPiggyAsync(Agent2:agent)<suspends>:agent=
		var Agent :agent= Agent2
		PiggyBrokenEv.Signal(Agent2)
		MaxClicks := 4
		Cost := Resources.G().CalculateGoldWithMultiplier(Agent, Balance.G().Rewards.PiggyTimeSec, 1.0)
		var CurCostLeft:int = Cost
		
		Sleep(1.0) # wait for popup to hide
		for(X := 0..MaxClicks-1):
			CostBillboard.G().SetText("${CurCostLeft.ToShortNumberString()}")
			spawn. ScalePigAsync(X*1.0/MaxClicks)
			loop:
				Ag := Button.G().InteractedWithEvent.Await()
				if(AmountToTake :int= Min(CurCostLeft, Ceil(Cost/MaxClicks))):
					Took := Resources.G().TryToTakeGold(Ag, AmountToTake)
					if(Took?): # "{Loc.G(Agent,"Piggy")}")
						set CurCostLeft -= AmountToTake
						Audio.G().GotCashSmall.Play(Agent)
						set Agent = Ag
						break
		Agent
		

			