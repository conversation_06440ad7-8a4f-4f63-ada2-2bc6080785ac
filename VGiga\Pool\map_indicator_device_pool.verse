using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}

map_indicator_device_pool_devic<public> := class(auto_creative_device, i_init):
    @editable Area:area_box_devic = area_box_devic{}
    var FreeCreativeDevices<public> : []map_indicator_device = array{}
    var AllCreativeDevices : []map_indicator_device = array{}

    var PlayerEvents :?player_events_manager_devic= false
    var ShowToAll :logic= false
    
    Init<override>(Container:vcontainer):void=
        set PlayerEvents = Container.ResolveOp[player_events_manager_devic] or Err()
        set FreeCreativeDevices = for(Obj : Self.GetDevicesInArea(map_indicator_device, Area)):
            Obj
        set AllCreativeDevices = FreeCreativeDevices

    GetAllFree<public>()<transacts>:[]map_indicator_device=
        FreeCreativeDevices
    
    GetAll<public>()<transacts>:[]map_indicator_device=
        AllCreativeDevices

    SetShowToAll<public>():void=
        set ShowToAll = true

    Rent<public>()<decides><transacts>:map_indicator_device=
        if(CreativeDevice := FreeCreativeDevices[0]
            FreeDev := FreeCreativeDevices.RemoveElement[0]
            set FreeCreativeDevices = FreeDev
        ):
            return CreativeDevice
        else:
            FailError[]
            return map_indicator_device{}

    Return<public>(CreativeDevice:map_indicator_device):void=
        set FreeCreativeDevices += array{CreativeDevice}

    RentDisposable<public>()<decides><transacts>:pooled_map_indicator_device=
        Device := Rent[]
        pooled_map_indicator_device:
            Device := Device
            MyPool := Self


pooled_map_indicator_device<public> := class(i_disposable):
    MyPool<public>:map_indicator_device_pool_devic
    Device<public>:map_indicator_device

    Dispose<override>():void=
        MyPool.Return(Device)

    ForAgent<public>(Agent:agent):pooled_map_indicator_device_for_agent=
        if(MyPool.ShowToAll?):
            for(Ag->Data:MyPool.PlayerEvents.G().RegisteredPlayers):
                for(X:=0..10):
                    Device.ActivateObjectivePulse(Ag)
        else:
            for(X:=0..10):
                Device.ActivateObjectivePulse(Agent)

        pooled_map_indicator_device_for_agent:
            MyPool := MyPool
            Device := Device
            Agent := Agent
    

pooled_map_indicator_device_for_agent<public> := class(i_disposable):
    MyPool<public>:map_indicator_device_pool_devic
    Device<public>:map_indicator_device
    Agent<public>:agent

    Dispose<override>():void=
        if(MyPool.ShowToAll?):
            for(Ag->Data:MyPool.PlayerEvents.G().RegisteredPlayers):
                Device.DeactivateObjectivePulse(Ag)
        else:
            Device.DeactivateObjectivePulse(Agent)

        MyPool.Return(Device)