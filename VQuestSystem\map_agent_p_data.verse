#gen
#map_t1_t2_priv
#agent
#p_data
#id-777b1291-e4d8-4c56-a992-d97e57227487
using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }
map_agent_p_data := class:
	var DataMap:[agent]p_data = map{}
	var MDataSetEvent:?event(tuple(agent, p_data)) = false

	Get(Key:agent)<decides><transacts>:p_data=
		DataMap[Key]

	GetErr(Key:agent)<decides><transacts>:p_data=
		if(Data := DataMap[Key]):
			Data
		else:
			FailError[]
			Err()

	GetMap()<transacts>:[agent]p_data=
		DataMap

	GetOrAwait<public>(Key:agent)<suspends>:p_data=
		if(Data := DataMap[Key]):
			Data
		else:
			if(DataSetEvent := MDataSetEvent?):
				var Return:?p_data = false
				loop:
					Data := DataSetEvent.Await()
					if(Data(0) = Key):
						set Return = option. Data(1)
						break
				Return.G()
				# DataSetEvent.AwaitForData(Key)
			else:
				Ev := event(tuple(agent, p_data)){}
				set MDataSetEvent = option. Ev
				var Return:?p_data = false
				# Ev.AwaitForData(Key)
				loop:
					Data := Ev.Await()
					if(Data(0) = Key):
						set Return = option. Data(1)
						break
				Return.G()
			

	Set(Key:agent, Data:p_data):void=
		if. set DataMap[Key] = Data
		if(DataSetEvent := MDataSetEvent?):
			DataSetEvent.Signal((Key, Data))

	Remove(Key:agent)<transacts>:void=
		set DataMap = DataMap.WithRemoved(Key)
#id-777b1291-e4d8-4c56-a992-d97e57227487