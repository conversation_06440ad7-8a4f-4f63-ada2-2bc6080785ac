using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga
using. VGiga.Pool

popup<public> := class(auto_creative_device, i_init, i_init_per_player_async):
	@editable PopupDevice1ButtonPool:?popup_dialog_device_pool_editable = false
	@editable PopupDevice2ButtonPool:?popup_dialog_device_pool_editable = false

	AgentToPopup1:map_agent_popup_dialog_device = map_agent_popup_dialog_device{}
	AgentToPopup2:map_agent_popup_dialog_device = map_agent_popup_dialog_device{}

	var PlayerEvents:?player_events_manager_devic = false
	var Loc:i_localization = empty_i_localization{}

	MsgOk<localizes>:message = "Ok"
	# MsgCancel<localizes>:message = "Cancel"
	MsgAnuluj<localizes>:message = "Anuluj"

	Init<override>(Container:vcontainer):void=
		set PlayerEvents = Container.ResolveOp[player_events_manager_devic] or Err()
		set Loc = Container.Resolve_i_localization()
		PopupDevice1ButtonPool.G()
		PopupDevice2ButtonPool.G()

	InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
		if(AgentToPopup1.Get[Agent]):
			return
		if(Popup := PopupDevice1ButtonPool.G().RentDisposable[]
			Popup2 := PopupDevice2ButtonPool.G().RentDisposable[]
		):
			AgentToPopup1.Set(Agent, Popup.Device)
			AgentToPopup2.Set(Agent, Popup2.Device)
			PlayerRemoved.Await()
			AgentToPopup1.Remove(Agent)
			AgentToPopup2.Remove(Agent)
			Popup.Dispose()
			Popup2.Dispose()
		else:
			LError()


	Show<public>(Agent:agent, PopupText:arr_char, ButtonText:arr_char, PlayerRemoved:event())<suspends>:logic=
		if(Popup := AgentToPopup1.Get[Agent]):
			Popup.SetDescriptionText(Loc.GMsg(Agent, PopupText))
			Popup.SetButtonText(Loc.GMsg(Agent, ButtonText), 0)
			Id := Popup.ShowAwaitForButtonOrExitAndHideWithTimout(Agent, PlayerRemoved, 10.0)
			Id? = 1 and true or false
		else:
			LError()
			false

	ShowOk<public>(Agent:agent, PopupText:arr_char)<suspends>:logic=
		PlayerRemoved := PlayerEvents.G().GetPlayerRemovedEv(Agent)
		return Show(Agent, PopupText, ArrCharOk, PlayerRemoved)

	ShowOk<public>(Agent:agent, PopupText:message)<suspends>:logic=
		PlayerRemoved := PlayerEvents.G().GetPlayerRemovedEv(Agent)
		return Show(Agent, PopupText, MsgOk, PlayerRemoved)

	Show<public>(Agent:agent, PopupText:arr_char, ButtonText:arr_char, ButtonText2:arr_char, PlayerRemoved:event())<suspends>:logic=
		if(Popup := AgentToPopup2.Get[Agent]):
			Popup.SetDescriptionText(Loc.GMsg(Agent, PopupText))
			Popup.SetButtonText(Loc.GMsg(Agent, ButtonText), 0)
			Popup.SetButtonText(Loc.GMsg(Agent, ButtonText2), 1)
			Id := Popup.ShowAwaitForButtonOrExitAndHideWithTimout(Agent, PlayerRemoved, 10.0)
			Id? = 1 and true or false
		else:
			LError()
			false

	ShowOkCancel<public>(Agent:agent, PopupText:arr_char)<suspends>:logic=
		PlayerRemoved := PlayerEvents.G().GetPlayerRemovedEv(Agent)
		Show(Agent, PopupText, ArrCharOk, ArrCharCancel, PlayerRemoved)
		
	ShowOkCancel<public>(Agent:agent, PopupText:message)<suspends>:logic=
		PlayerRemoved := PlayerEvents.G().GetPlayerRemovedEv(Agent)
		Show(Agent, PopupText, MsgOk, MsgAnuluj, PlayerRemoved)

	ShowOkCancel<public>(Agent:agent, Text:string)<suspends>:logic=
		PlayerRemoved := PlayerEvents.G().GetPlayerRemovedEv(Agent)
		Str := Loc.G(Agent, Text)
		Show(Agent, Str.ToMessage(), MsgOk, MsgAnuluj, PlayerRemoved)

	Show<public>(Agent:agent, PopupText:message, ButtonText:message, ButtonText2:message, PlayerRemoved:event())<suspends>:logic=
		if(Popup := AgentToPopup2.Get[Agent]):
			Popup.SetDescriptionText(PopupText)
			Popup.SetButtonText(ButtonText, 0)
			Popup.SetButtonText(ButtonText2, 1)
			Id := Popup.ShowAwaitForButtonOrExitAndHideWithTimout(Agent, PlayerRemoved, 10.0)
			LPrint("Id  {Id? or 99}")
			
			Id? = 1 and true or false
		else:
			LError()
			false
	Show<public>(Agent:agent, PopupText:message, ButtonText:message, PlayerRemoved:event())<suspends>:logic=
		if(Popup := AgentToPopup1.Get[Agent]):
			Popup.SetDescriptionText(PopupText)
			Popup.SetButtonText(ButtonText, 0)
			Id := Popup.ShowAwaitForButtonOrExitAndHideWithTimout(Agent, PlayerRemoved, 10.0)
			Id? = 1 and true or false
		else:
			LError()
			false

		

#gen
#map_t1_t2
#agent
#popup_dialog_device
#id-668181e5-a458-4bd3-8d65-af108db8ac72
map_agent_popup_dialog_device<public> := class:
	var DataMap:[agent]popup_dialog_device = map{}
	var MDataSetEvent:?event(tuple(agent, popup_dialog_device)) = false

	Get<public>(Key:agent)<decides><transacts>:popup_dialog_device=
		DataMap[Key]

	GetErr<public>(Key:agent)<decides><transacts>:popup_dialog_device=
		if(Data := DataMap[Key]):
			Data
		else:
			FailError[]
			Err()

	GetMap<public>()<transacts>:[agent]popup_dialog_device=
		DataMap

	GetOrAwait<public>(Key:agent)<suspends>:popup_dialog_device=
		if(Data := DataMap[Key]):
			Data
		else:
			if(DataSetEvent := MDataSetEvent?):
				var Return:?popup_dialog_device = false
				loop:
					Data := DataSetEvent.Await()
					if(Data(0) = Key):
						set Return = option. Data(1)
						break
				Return.G()
				# DataSetEvent.AwaitForData(Key)
			else:
				Ev := event(tuple(agent, popup_dialog_device)){}
				set MDataSetEvent = option. Ev
				# Ev.AwaitForData(Key)
				var Return:?popup_dialog_device = false
				loop:
					Data := Ev.Await()
					if(Data(0) = Key):
						set Return = option. Data(1)
						break
				Return.G()
			

	Set<public>(Key:agent, Data:popup_dialog_device):void=
		if. set DataMap[Key] = Data
		if(DataSetEvent := MDataSetEvent?):
			DataSetEvent.Signal((Key, Data))

	Remove<public>(Key:agent)<transacts>:void=
		set DataMap = DataMap.WithRemoved(Key)
#id-668181e5-a458-4bd3-8d65-af108db8ac72