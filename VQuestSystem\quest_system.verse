using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }
using. VNotifications
using. VResourcesSystem
using. VArrowSystem
using. VCoop

quest_system_c<public>(Container:player_events_manager_devic,
        Balance:quest_system_balance
    ):quest_system=
        Notifs := Container.ResolveErr(notifications_system)
        ResourcesManager := Container.ResolveErr(resources_manager)
        
        Manager := quest_system:
            Balance := Balance
            ResourcesManager := ResourcesManager
            Notifs := Notifs
            Loc := Container.Container.Resolve_i_localization()
            Arrows := Container.Resolve[arrow_system] or Err()
            Save := Container.Resolve[save_system] or Err()
            Coop := Container.Resolve[coop] or Err()
        .Init()
        Container.Register(Manager)
        return Manager

quest_system<public> := class<internal>(i_init_per_player_async):
    Balance<public>:quest_system_balance
    ResourcesManager<public>:resources_manager
    Notifs<public>:notifications_system
    Loc<public>:i_localization
    Save<public>:save_system

    var PlayerDataMap : VQuestSystem.map_agent_p_data = VQuestSystem.map_agent_p_data{}
    var QuestsMap : [string]quest_data = map{}

    Arrows:arrow_system
    Coop:coop

    QuestCompletedEv<public> :event(active_quest_data)= event(active_quest_data){}
    QuestGotEv<public> :event(active_quest_data)= event(active_quest_data){}
    RestartArrowsEv<public> :event(agent)= event(agent){}
    GetActiveQuests<public>(Agent:agent):[string]active_quest_data=
        PlayerDataMap.Get[Agent].ActiveQuests or block:
            LError()
            map{}

    GetCompletedQuests<public>(Agent:agent):[]quest_data=
        QuestIds :[string]logic= PlayerDataMap.Get[Agent].CompletedQuests or block:
            LError()
            map{}
        # LPrint("QuestIds {QuestIds.Length}")
        return for(Id->Val:QuestIds
            Quest := QuestsMap[Id]
        ):
            Quest

    GetQuestsData<public>():[string]quest_data=
        QuestsMap

    Init():quest_system=
        for(Quest:Balance.Quests
            not Quest.CompleteEvent?
        ):
            if(Completer := Balance.QuestCompleters[Quest.TypeId]):
                set Quest.CompleteEvent = option. Completer.GetCompleteEvent()
            else:
                LPrint("ERROR: Quest completer not found, id: {Quest.TypeId}")
        
        for(Quest:Balance.Quests):
            if. set QuestsMap[Quest.Id] = Quest
        
        Self
    
    MakeSingleQuestSlot():single_quest_with_slot=
        Generated := make_single_quest_overlay_generated()
        SingleQuestOverlay := Generated.SingleQuestOverlay
        Slot := 
            stack_box_slot:
                Padding := margin:
                    Bottom := -5.000000
                HorizontalAlignment := horizontal_alignment.Fill
                VerticalAlignment := vertical_alignment.Fill
                Distribution := option. 1.0 
                Widget := SingleQuestOverlay
        single_quest_with_slot:
            Slot := Slot
            Ui := Generated

    (Generated:single_quest_with_slot).UpdateQuestText(ActiveQuest:active_quest_data):void=
        QName := Loc.G(ActiveQuest.Agent, ActiveQuest.Data.Name)
        if(ActiveQuest.Data.MaxProgress > 1):
            Generated.Ui.TextQuestDesc.SetText("{QName} {ActiveQuest.Progress}/{ActiveQuest.Data.MaxProgress}".ToMessage())
        else:
            Generated.Ui.TextQuestDesc.SetText(ToMsg(QName))

        Generated.Ui.TextReward.SetText(ActiveQuest.Data.GoldReward.ToShortNumberString().ToMessage())

    IsQuestActive<public>(Agent:agent, QuestId:string)<decides><transacts>:active_quest_data=
        PlayerDataMap.Get[Agent].FindActiveQuest[QuestId]

    InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
        Print("quest_system InitPlayerAsync"); Sleep(0.0)
        if(Coop.CoopAdmin? <> Agent):
            # AdminData := PlayerDataMap.GetOrAwait(CoopAdmin)
            # # PlayerUi.AddWidget(QuestsUi.Canvas)
            # Agent.AddToPlayerUi(AdminData.QuestsUi.QuestsList)
            # PlayerRemoved.Await()
            # Agent.RemoveFromPlayerUi(AdminData.QuestsUi.QuestsList)
        else:
            QuestsUi := make_quests_list_generated()
            PDataQuests := VQuestSystem.p_data:
                Agent := Agent
                QuestsUi := QuestsUi
            PlayerDataMap.Set(Agent, PDataQuests)
            QuestAddedMethod := event(quest_data){}
            QuestFinished := event(active_quest_data){}
            # QuestsUi := quests_ui_c()
            # QuestsUi.StackBoxQuestsList.RemoveWidget(QuestsUi.SingleQuestOverlay)
            Agent.AddToPlayerUi(QuestsUi.QuestsList)
            if(Coop.IsAdmin[Agent]):
                Coop.AddWidgetToSyncAsAdmin(QuestsUi.QuestsList, widget_syncer_id.quests)
            Coop.SyncWidget(widget_syncer_id.quests)
                
            CancelQuestsEvent:event() = event(){}

            MissionsUnlockedOnEnteringTheMap := Save.GetMissionsUnlocked(Agent)

            race:
                PlayerRemoved.Await()

                # New Quest
                loop:
                    QuestData := QuestAddedMethod.Await()
                    ActiveQuest := PDataQuests.AddQuest(QuestData)
                    QuestGotEv.Signal(ActiveQuest)
                    if(Tracker := QuestData.MTracker?):
                        spawn. HandleQuestTrackerAsync(Agent, ActiveQuest, QuestFinished, Tracker, CancelQuestsEvent, MissionsUnlockedOnEnteringTheMap.Find[QuestData.Id] and true or false)
                    else:
                        spawn. HandleQuestAsync(Agent, ActiveQuest, QuestFinished, QuestsUi, CancelQuestsEvent, MissionsUnlockedOnEnteringTheMap.Find[QuestData.Id] and true or false)

                    # test 2 quests
                    # ActiveQuest2 := PDataQuests.AddQuest(QuestData)
                    # SingleQuestUi2 := quest_ui_c()
                    # QuestsUi.StackQuests.AddWidget(SingleQuestUi2.StackSlot)
                    # spawn. HandleQuestAsync(Agent, ActiveQuest, QuestFinishedMethod, QuestsUi, SingleQuestUi2)
                
                # Finished Quest
                loop:
                    # LPrint("QuestFinished Await")
                    Quest := QuestFinished.Await()
                    Save.AddMissionUnlocked(Agent, Quest.Data.Id)
                    # LPrint("QuestFinished {Quest.Data.Id}")
                    spawn. HandleQuestFinished(Agent, PDataQuests, Quest, QuestAddedMethod) 		
                # First Quest, also on rebirth
                loop:
                    # if(MissionsUnlockedOnEnteringTheMap.Length > 1):
                    # 	for(MissionUnlocked:MissionsUnlockedOnEnteringTheMap):
                    # 		if. set PDataQuests.CompletedQuests[MissionUnlocked] = true

                    # 	for(Id->CompletedQuest:PDataQuests.CompletedQuests
                    # 		QuestData :quest_data= QuestsMap[Id] 
                    # 	):
                    # 		QuestData

                    if(Balance.AssignFirstQuestAtStart?
                    ):
                        for(Quest:Balance.Quests
                            Quest.FirstQuest?
                            # not PDataQuests.CompletedQuests[Quest.Id]
                        ):
                            QuestAddedMethod.Signal(Quest)
                    
                    Sleep(Inf)
                    # this below is old, now player rebirths as leaving the game
                    # ResourcesManager.RebirthEvent.Await()
                    # CancelQuestsEvent.Signal()

            CancelQuestsEvent.Signal()

            PlayerDataMap.Remove(Agent)
            Agent.RemoveFromPlayerUi(QuestsUi.QuestsList)
                # PlayerUi.RemoveWidget(QuestsUi.Canvas)

    ShowQuest<public>(Agent:agent, Id:string):void=
        if(PlayerData := PlayerDataMap.Get[Agent]):
            if(ActiveQuest := PlayerData.FindActiveQuest[Id]):
                ActiveQuest.RequestedToShowOnUiEv.Signal()
            else:
                LErrorPrint("Quest not found: {Id}")
        else:
            LErrorPrint("Player data not found for agent")

    HandleQuestFinished(Agent:agent, PDataQuests:p_data, Quest:active_quest_data, QuestAddedMethod:event(quest_data))<suspends>:void=
        Sleep(0.0)
        PDataQuests.RemoveQuestAndAddAsCompleted(Quest)
        QuestCompletedEv.Signal(Quest)
        if(not Quest.FinishedFromSave?):
            QName := Loc.G(Quest.Agent, Quest.Data.Name)
            QCompleted := Loc.G(Agent, "Quest completed")
            Notifs.ShowTopNotification(Agent, ToMsg("{QCompleted}: {QName}"))
            
            if(Quest.Data.GoldReward > 0):
                ResourcesManager.GiveGoldText(Agent, Quest.Data.GoldReward, QCompleted)
        
        for(NextQuestId:Quest.Data.NextQuestIds):
            if(NextQuest := QuestsMap[NextQuestId]):
                QuestAddedMethod.Signal(NextQuest)
            else:
                LErrorPrint("Next quest not found, id: {NextQuestId}")

        for(NextVisibleQuestIds:Quest.Data.NextVisibleQuestIds
            ActiveQuest := PDataQuests.FindActiveQuest[NextVisibleQuestIds]
        ):
            # Print("request to show quest on ui: {ActiveQuest.Data.Name}")
            ActiveQuest.RequestedToShowOnUiEv.Signal()

        

    HandleQuestAsync(Agent:agent, ActiveQuest:active_quest_data, FinishedEvent:event(active_quest_data), QuestsUi:quests_list_generated, CancelQuestsEvent:event(), IsCompletedFromSave:logic)<suspends>:void=
        if(CompleteEvent := ActiveQuest.Data.CompleteEvent?):

            var MSingleQuestUi :?single_quest_with_slot= false

            LangChangedEv := Loc.GetLanguageChangedEvent()

            var MSpawnedArrow:?i_disposable = false

            race:
                CancelQuestsEvent.Await()
                loop:
                    if(IsCompletedFromSave?):
                        Sleep(Inf)

                    LangChangedEv.AwaitFor(Agent)
                    if(SingleQuestUi := MSingleQuestUi?):
                        SingleQuestUi.UpdateQuestText(ActiveQuest)
                        Coop.SyncWidget(widget_syncer_id.quests)
                block:
                    if(IsCompletedFromSave?):
                        Sleep(Inf)

                    if(not ActiveQuest.Data.ShowInUiInstantly?):
                        ActiveQuest.RequestedToShowOnUiEv.Await()
                    SingleQuestUi := MakeSingleQuestSlot()
                    set MSingleQuestUi = option. SingleQuestUi
                    SingleQuestUi.UpdateQuestText(ActiveQuest)
                    Coop.AddWidgetDynamicSlot(Agent, QuestsUi.StackBoxQuestsList, SingleQuestUi.Slot, widget_syncer_id.quests)

                    Sleep(Inf)
                loop:
                    if(not IsCompletedFromSave?):
                        if(Coop.IsCoop[]):
                            CompleteEvent.Await()
                        else:
                            CompleteEvent.AwaitFor(Agent)
                    # LPrint("CompleteEvenr {ActiveQuest.Data.Id}")
                    
                    if(IsCompletedFromSave?):
                        set ActiveQuest.Progress = ActiveQuest.Data.MaxProgress
                        set ActiveQuest.FinishedFromSave = true
                    else:
                        set ActiveQuest.Progress += ActiveQuest.Data.ProgressIncreasePerEvent

                    if(SingleQuestUi := MSingleQuestUi?):
                        SingleQuestUi.UpdateQuestText(ActiveQuest)
                        Coop.SyncWidget(widget_syncer_id.quests)
                    if(ActiveQuest.Progress >= ActiveQuest.Data.MaxProgress):
                        # LPrint("FinishedEvent Signal {ActiveQuest.Data.Id}")
                        FinishedEvent.Signal(ActiveQuest)
                        # LPrint("ActiveQuest.QuestFinishedEvent.Signal {ActiveQuest.Data.Id}")
                        ActiveQuest.QuestFinishedEvent.Signal()
                        break
                loop:
                    if(IsCompletedFromSave?):
                        Sleep(Inf)
                    # LPrint("ARROW QUEST LOOP {ActiveQuest.Data.Id}")
                    
                    if(Arrow := MSpawnedArrow?):
                        Arrow.Dispose()
                        set MSpawnedArrow = false
                    if(ArrowPos := ActiveQuest.Data.ArrowPosition?):
                        # LPrint("ArrowPos")
                        set MSpawnedArrow = Arrows.ShowArrow(Agent, ArrowPos)
                    else if(ArrowPosGetter := ActiveQuest.Data.ArrowPositionGetter?):
                        # LPrint("Getter")
                        MArrowPos :?vector3 = ArrowPosGetter(Agent)
                        if(ArrowPos := MArrowPos?):
                            # LPrint("Getter Show")
                            set MSpawnedArrow = Arrows.ShowArrow(Agent, ArrowPos)
                        RestartArrowsEv.AwaitFor(Agent)
                        # LPrint("After reset")
                    else:
                        # LPrint("Sleep(Inf)")
                        Sleep(Inf)


            if(Arrow := MSpawnedArrow?):
                Arrow.Dispose()

            if(SingleQuestUi := MSingleQuestUi?):
                Coop.RemoveWidgetDynamicSlot(Agent, QuestsUi.StackBoxQuestsList, SingleQuestUi.Ui.SingleQuestOverlay, widget_syncer_id.quests)

    # UpdateTextOnLangChange(AcitveQuest:active_quest_data, )<suspends>:void=
        

    AgentToStr<localizes>(Agent:agent):message = "Text{Agent}"
    var Idd:int = 0
    
    HandleQuestTrackerAsync(Agent:agent, ActiveQuest:active_quest_data, FinishedEvent:event(active_quest_data), Tracker:tracker_device, CancelQuestEvent:event(), IsCompletedFromSave:logic)<suspends>:void=
        if(CompleteEvent := ActiveQuest.Data.CompleteEvent?):
            Tracker.SetTitleText("{Idd}".ToMessage()) 
            # this is broken for translation ie changes for all players, maybe individual trackers per player would work
            # set Idd += 10
            # spawn. Method(Tracker)
            # Tracker.Assign(Agent)
            race:
                CancelQuestEvent.Await()
                loop:
                    CompleteEvent.AwaitFor(Agent)
                    set ActiveQuest.Progress += ActiveQuest.Data.ProgressIncreasePerEvent
                    for(X:= 1..ActiveQuest.Data.ProgressIncreasePerEvent):
                        Tracker.Increment(Agent)

                    if(ActiveQuest.Progress >= ActiveQuest.Data.MaxProgress):
                        FinishedEvent.Signal(ActiveQuest)
                        ActiveQuest.QuestFinishedEvent.Signal()
                        Tracker.Remove(Agent)
                        break
    # Method(Tracker:tracker_device)<suspends>:void=
    # 	Sleep(GetRandomFloat(10.0,20.0))
    # 	set Idd += 10
    # 	Tracker.SetTitleText("{Idd}".ToMessage())

        
            
        
    

single_quest_with_slot := class:
    Slot:stack_box_slot 
    Ui:single_quest_overlay_generated
    

