using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
using. VNotifications
using. VResourcesSystem
using. VTimer
using. VMinigames

race_track_lobby<public> := class(auto_creative_device, i_init_async):
	@editable
	VolumeLobby:volume_device = volume_device{}

	@editable
	MStartCountdown:?float = false

	@editable
	MCountdownTimer:?timer_device = false

	@editable
	MAdditionalDelayBetweenGames:?float = false

	@editable
	Track:race_track = race_track{}

	InitAsync<override>(Container:vcontainer)<suspends>:void=
		loop:
			if(VolumeLobby.GetAgentsInVolume().Length = 0):
				VolumeLobby.AgentEntersEvent.Await()
			
			if(StartCountDown := MStartCountdown?):
				if(Timerr := MCountdownTimer?):
					Timerr.SetMaxDuration(StartCountDown)
					Timerr.SetActiveDuration(StartCountDown)
					Timerr.ResetForAll()
					Timerr.StartForAll()
				
					Sleep(StartCountDown)

			for(Agent:VolumeLobby.GetAgentsInVolume()):
				Track.StartChannel.G().Transmit(option. Agent)


			AdditionalDelay := MAdditionalDelayBetweenGames? or 0.0
			if(Cooldown := Track.MTimeLimit? + AdditionalDelay):
				if(Timerr := MCountdownTimer?):
					Timerr.SetMaxDuration(Cooldown)
					Timerr.SetActiveDuration(Cooldown)
					Timerr.ResetForAll()
					Timerr.StartForAll()
				
					Sleep(Cooldown)
			else:
				Sleep(1.0)
			
			

		

	# InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
		# Print("race_track_lobby InitPlayerAsync"); Sleep(0.0)
