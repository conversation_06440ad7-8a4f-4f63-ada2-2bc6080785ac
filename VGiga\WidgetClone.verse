using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga


CloneStackBoxSlot<public>(Slot:stack_box_slot)<transacts>:stack_box_slot=
    SlotWidget := Slot.Widget
    NewSlot := stack_box_slot:
        Widget := CloneWidget(SlotWidget)
        Padding := Slot.Padding
        HorizontalAlignment := Slot.HorizontalAlignment
        VerticalAlignment := Slot.VerticalAlignment
        Distribution := Slot.Distribution

CloneOverlaySlot<public>(Slot:overlay_slot)<transacts>:overlay_slot=
    overlay_slot:
        Widget := CloneWidget(Slot.Widget)
        Padding := Slot.Padding
        HorizontalAlignment := Slot.HorizontalAlignment
        VerticalAlignment := Slot.VerticalAlignment

CloneWidget<public>(Widget:widget)<transacts>:widget=
    if(Overlay := overlay[Widget]):
        CloneOverlay(Overlay)
    else if(TextureBlock := texture_block[Widget]):
        CloneTextureBlock(TextureBlock)
    else if(Casted := stack_box[Widget]):
        CloneStackBox(Casted)
    else if(Casted := text_block[Widget]):
        CloneTextBlock(Casted)
    else if(Casted := button[Widget]):
        CloneButton(Casted)
    else if(Casted := color_block[Widget]):
        CloneColorBlock(Casted)
    else if(Casted := button_loud[Widget]):
        CloneButtonLoud(Casted)
    else if(Casted := button_regular[Widget]):
        CloneButtonRegular(Casted)
    else if(Casted := button_quiet[Widget]):
        CloneButtonQuiet(Casted)
    else if(Casted := canvas[Widget]):
        CloneCanvas(Casted)
    else if(Casted := slider_regular[Widget]):
        LErrorPrint("slider not sync implemented yet")
        CloneSliderRegular(Casted)
    else:
        Err()

CloneStackBox(Widget:stack_box)<transacts>:stack_box=
    stack_box:
        Orientation := Widget.Orientation
        Slots := for(Slot:Widget.Slots):
            CloneStackBoxSlot(Slot)
    
CloneTextBlock<public>(Widget:text_block)<transacts>:text_block=
    text_block:
        DefaultText := Widget.DefaultText
        DefaultTextColor := Widget.DefaultTextColor
        DefaultTextOpacity := Widget.DefaultTextOpacity
        DefaultJustification := Widget.DefaultJustification
        DefaultOverflowPolicy := Widget.DefaultOverflowPolicy
        DefaultShadowOffset := Widget.DefaultShadowOffset
        DefaultShadowColor := Widget.DefaultShadowColor
        DefaultShadowOpacity := Widget.DefaultShadowOpacity

CloneTextureBlock<public>(Widget:texture_block)<transacts>:texture_block=
    texture_block:
        DefaultImage := Widget.DefaultImage
        DefaultTint := Widget.DefaultTint
        DefaultHorizontalTiling := Widget.DefaultHorizontalTiling
        DefaultVerticalTiling := Widget.DefaultVerticalTiling
        DefaultDesiredSize := Widget.DefaultDesiredSize

CloneCanvas<public>(Widget:canvas)<transacts>:canvas=
    canvas:
        Slots := for(Slot:Widget.Slots):
            canvas_slot:
                Widget := CloneWidget(Slot.Widget)
                Anchors := Slot.Anchors
                Alignment := Slot.Alignment
                SizeToContent := Slot.SizeToContent
                ZOrder := Slot.ZOrder
                Offsets := Slot.Offsets


CloneButton<public>(Widget:button)<transacts>:button=
    button:
        Slot := button_slot:
            Widget := CloneWidget(Widget.Slot.Widget)

CloneColorBlock<public>(Widget:color_block)<transacts>:color_block=
    color_block:
        DefaultColor := Widget.DefaultColor
        DefaultOpacity := Widget.DefaultOpacity
        DefaultDesiredSize := Widget.DefaultDesiredSize

CloneButtonLoud<public>(Widget:button_loud)<transacts>:button_loud=
    button_loud:
        DefaultText := Widget.DefaultText

CloneButtonRegular<public>(Widget:button_regular)<transacts>:button_regular=
    button_regular:
        DefaultText := Widget.DefaultText

CloneButtonQuiet<public>(Widget:button_quiet)<transacts>:button_quiet=
    button_quiet:
        DefaultText := Widget.DefaultText

CloneOverlay<public>(Widget:overlay)<transacts>:overlay=
    overlay:
        Slots := for(Slot:Widget.Slots):
            NewSlot := CloneOverlaySlot(Slot)
            NewSlot

# CloneProgressBar<public>(Widget:progress_bar)<transacts>:progress_bar=
#     progress_bar:
#         DefaultValue := Widget.DefaultValue
#         DefaultDirection := Widget.DefaultDirection

# CloneSlider<public>(Widget:slider)<transacts>:slider=
#     slider:
#         DefaultValue := Widget.DefaultValue
#         DefaultMinValue := Widget.DefaultMinValue
#         DefaultMaxValue := Widget.DefaultMaxValue
#         DefaultStepSize := Widget.DefaultStepSize

CloneSliderRegular<public>(Widget:slider_regular)<transacts>:slider_regular=
    slider_regular:
        DefaultValue := Widget.DefaultValue
        DefaultMinValue := Widget.DefaultMinValue
        DefaultMaxValue := Widget.DefaultMaxValue
        DefaultStepSize := Widget.DefaultStepSize
