using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
using. VGiga.Pool 
using. VPropSpawner

arrow_system<public> := class(auto_creative_device, i_init):
    # @editable MapIndicatorPool:map_indicator_device_pool_devic = map_indicator_device_pool_devic
    @editable ArrowAsset:creative_prop_asset = DefaultCreativePropAsset
    @editable BeaconsPoolDevice<public>:beacon_device_pool_devic = beacon_device_pool_devic{}
    @editable MapIndicatorPoolDevice<public>:map_indicator_device_pool_devic = map_indicator_device_pool_devic{}
    
    var PropSpawner:?prop_spawner = false

    Init<override>(Container:vcontainer):void=
        set PropSpawner = Container.ResolveErrOp(prop_spawner)
    # Init<public>(Container:player_events_manager_devic,
    # 	PropSpawner:prop_spawner
    # ):arrow_system=
    # 	Manager := arrow_system:
    # 		D := Self
    # 		# MapIndicatorPool := MapIndicatorPool.GetPool()
    # 		PropSpawner := PropSpawner
    # 		BeaconsPoolDevice := BeaconsPoolDevice.GetPool()
    # 		MapIndicatorsPoolDevice := MapIndicatorPoolDevice.GetPool()
        
    # 	Container.Register(Manager)
    # 	Manager
    SetMapIndicatorsToShowToAll<public>():void=
        MapIndicatorPoolDevice.SetShowToAll()

    SetBeaconsToShowToAll<public>():void=
        BeaconsPoolDevice.SetShowToAll()

    ShowBeacon<public>(Agent:agent, Destination:vector3)<suspends>:?i_disposable=
        MChar := WaitForFortCharacterActive2.WithTimeout(Agent, 30.0)
        if(Char := MChar?):
            if(BeaconDisposable := BeaconsPoolDevice.RentDisposable[]):
                BeaconForAgent := BeaconDisposable.ForAgent(Agent)
                if. BeaconForAgent.Device.TeleportTo[Destination, rotation{}]
                return option. BeaconForAgent
            else. LError()
        LError()
        false
    
    ShowArrow<public>(Agent:agent, Destination:vector3)<suspends>:?i_disposable=
        MChar := WaitForFortCharacterActive2.WithTimeout(Agent, 30.0)
        if(Char := MChar?):
            if(IndicatorDisposable := MapIndicatorPoolDevice.RentDisposable[]):
                if. IndicatorDisposable.Device.TeleportTo[Destination, rotation{}]
                IndicatorForAgent := IndicatorDisposable.ForAgent(Agent)
                return option. IndicatorForAgent
            else. LError()
        LError()
        false	
    # ShowArrow<public>(Agent:agent, Destination:vector3)<suspends>:?i_disposable=
    # 	MChar := WaitForFortCharacterActive2.WithTimeout(Agent, 30.0)
    # 	if(Char := MChar?):
    # 		MArrowProp := PropSpawner.Spawn(D.ArrowAsset, Char.GetTransform())
    # 		if(ArrowProp := MArrowProp?):
    # 			Arrow := arrow:
    # 				Agent := Agent
    # 				Prop := ArrowProp
    # 				Destination := Destination
                
    # 			spawn. Arrow.Init()
    # 			return option. Arrow
    # 		else LError()
    # 	false
    

    # InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
    # 	# Agent.WaitForFortCharacterActive()
    # 	# for(X:=0..20):
    # 		# MArrow := ShowArrow(Agent, vector3{})
    # 		# if(Arrow := MArrow?):
    # 		# 	Arrow.Dispose()
    # 	# Sleep(20.0)
    # 	# if(Arrow := MArrow?)
    # 	# 	Arrow.Dispose()
    # 	# 
    # 	PlayerRemoved.Await()
    

    # Init():arrow_system=
    # 	LPrint("INIT ARROW SYSTEM, LENG: MapIndicatorPool.FreeCreativeDevices.Length")
    # 	for(Dev:MapIndicatorPool.FreeCreativeDevices)
    # 		if. Dev.TeleportTo[Vector3(-999999.0,-999999.0,0.0), rotation]
    # 	
    # 	Self
    # 
    
    # ShowArrow(Agent:agent, Position:vector3):arrow_to_point=
    # 	if(Indicator := MapIndicatorPool.Rent[]
    # 		Indicator.TeleportTo[Position, rotation]
    # 	)
    # 		Indicator.ActivateObjectivePulse(Agent)
    # 		arrow_to_point
    # 			MapIndicatorPool := MapIndicatorPool
    # 			MMapIndicatorDevice := option. Indicator
    # 			Agent := Agent
    # 		
    # 	else
    # 		LError()
    # 		arrow_to_point
    # 			MapIndicatorPool := MapIndicatorPool
    # 			MMapIndicatorDevice := false
    # 			Agent := Agent
    # 		
    # 	
    # 
    


# arrow_to_point := class
# 	MapIndicatorPool<public>:map_indicator_device_pool
# 	MMapIndicatorDevice<public>:?map_indicator_device
# 	Agent<public>:agent

# 	Dispose():void=
# 		if(MapIndicatorDevice := MMapIndicatorDevice?)
# 			MapIndicatorDevice.DeactivateObjectivePulse(Agent)
# 			MapIndicatorPool.Return(MapIndicatorDevice)
# 		
# 	


arrow<public> := class<unique>(i_disposable):
    Prop<public>:creative_prop_unique
    Agent<public>:agent
    ArrowSpeed :float= 600.0
    var Destination<public>:vector3

    ArrowDestroyed:event()=event(){}

    Init()<suspends>:void=
        race:
            ArrowDestroyed.Await()
            loop:	
                MChar := WaitForFortCharacterActive2.WithTimeout(Agent, 30.0)
                if(Char := MChar?
                    CharTr := Char.GetTransform().WithRotation(rotation{})
                    # Tr := CharTr.WithRotation(rotation)
                    Tr := CharTr.RotateYawPitchTowards[Destination]
                    Prop.IsValid[]
                ):
                    Dist := Distance(CharTr.Translation, Destination)
                    if. Prop.Prop.TeleportTo[Tr]
                    race:
                        Prop.Prop.MoveTo(Destination, Tr.Rotation, Dist/ArrowSpeed)
                        Sleep(1.5)
                else:
                    Sleep(1.0)

    # SetDestination(Dest:vector3):void=
    #     set Destination = Dest
    # 

    Dispose<override>():void=
        ArrowDestroyed.Signal()
        Prop.Dispose()

# (Map:i_p_data_map(t)).Get(Player:agent where t:type)<decides><transacts>:t=
# 	if(Data := Map.GetDataMap()[Player])
# 		Data
# 	else
# 		FailError[]
# 		Err()
# 	

# 