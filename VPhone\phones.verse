using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
using. VResourcesSystem
using. VQuestSystem
using. VPrefabs
using. VCoop

phones<public> := class(auto_creative_device, i_init_async, i_init_per_player_async):
    @editable RemoteDevice:?signal_remote_manager_device = false
    @editable ItemGranterRemote:?item_granter_device = false

    @editable PopupMenuFirst:?popup_dialog_device = false
    @editable PopupMenu:?popup_dialog_device = false

    @editable PopupRegister1:?popup_dialog_device = false
    @editable PopupRegister2:?popup_dialog_device = false
    @editable PopupRegister3:?popup_dialog_device = false

    @editable PopupInvest1:?popup_dialog_device = false
    @editable PopupInvest2:?popup_dialog_device = false

    @editable PopupAchievements1:?popup_dialog_device = false

    @editable PopupCard:?popup_dialog_device = false
    
    @editable MP_Account_Created:?analytics_device = false
    

    # @editable InputTriggerOpenPhone:?input_trigger_device = false

    @editable ClassSelectorReg2:?class_and_team_selector_device = false
    @editable ClassSelectorReg3:?class_and_team_selector_device = false
    @editable ClassSelectorNone:?class_and_team_selector_device = false

    var AgentToPrimarySignalClickedEv:map_agent_event = map_agent_event{}

    var Loc:i_localization = empty_i_localization{}
    var Resources:?resources_manager = false
    var Quests:?quest_system = false
    var Coop :?coop= false

    RegisteredAccountEv<public>:event(agent) = event(agent){}
    MadeDepositEv<public>:event(agent) = event(agent){}

    PhoneTriggeredEv:event(agent) = event(agent){}

    var Save :?save_system= false
    
    InitAsync<override>(Container:vcontainer)<suspends>:void=
        set Coop = Container.ResolveOp[coop] or Err()
        set Save = Container.ResolveOp[save_system] or Err()
        OpenTriggers := tag_open_phone_trigger_device{}.GetAll(Self)
        for(Trigger:OpenTriggers):
            Trigger.TriggeredEvent.Subscribe(OnPhoneTriggerTriggered)
        set Quests = Container.ResolveOp[quest_system] or Err()
        Balance := Container.Resolve[gm_balance] or Err()
        # if:
        # 	Val := Ceil(Balance.QuestBalance.Quests.Length / QuestsPerPage)
        # 	set QuestsPages = option. Val
        set Loc = Container.Resolve_i_localization()
        Remote := RemoteDevice.G()
        PopupMenuFirst.G()
        ItemGranterRemote.G()
        PopupRegister1.G()
        PopupRegister2.G()
        PopupRegister3.G()
        ClassSelectorReg2.G()
        ClassSelectorReg3.G()
        ClassSelectorNone.G()
        PopupInvest1.G()
        PopupInvest2.G()
        PopupAchievements1.G()
        PopupCard.G()

        set Resources = Container.ResolveErrOp(resources_manager)

        loop:
            Agent := Remote.PrimarySignalEvent.Await()
            if(Ev := AgentToPrimarySignalClickedEv.Get[Agent]):
                Ev.Signal()

    OnPhoneTriggerTriggered(MAgent:?agent):void=
        if(Agent := MAgent?):
            PhoneTriggeredEv.Signal(Agent)
        
    # Timeout(IntToResturn:int, PlayerRemoved:event())<suspends>:?int=
    # 	race:
    # 		block:
    # 			PlayerRemoved.Await()
    # 			false
    # 		block:
    # 			Sleep(10.0)
    # 			option. IntToResturn

    Completed<localizes>:message = "✅"
    MakePlayerQuestsUi():achievements_ui_phone=
        Ui := make_phone_achievements_generated()
        Ui.SingleQuestOverlay.Collapse()
        Panels :[]single_quest_overlay_generated = for(X:= 0..5):
            P := make_single_quest_overlay_generated()
            AddQuestPanel(Ui.StackBoxQuestsList, P.SingleQuestOverlay)
            P

        achievements_ui_phone:
            Ui := Ui
            Panels := Panels

    QuestsPerPage :int= 5
    # var QuestsPages :?int= false
    UpdatePlayerQuestsUi(Agent:agent, PageToModulo:int, Ui:achievements_ui_phone):void=
        AdminOrAgent := Coop.G().AdminOrAgent(Agent)
        ActiveQuests := Quests.G().GetActiveQuests(AdminOrAgent)
        CompletedQuests := Quests.G().GetCompletedQuests(AdminOrAgent)
        
        # 	Page := Ceil(Balance.QuestBalance.Quests.Length / QuestsPerPage)
        MaxPages := Ceil((ActiveQuests.Length + CompletedQuests.Length) / QuestsPerPage) or block:
            LError()
            0
        Page := Mod[PageToModulo, MaxPages] or block:
            LError()
            0
        QuestsStartId := Page * QuestsPerPage
        QuestsEndId := QuestsStartId + QuestsPerPage
        var QuestsCounter :int= 0
        var PanelCounter :int= 0
        for(Panel:Ui.Panels):
            Panel.SingleQuestOverlay.Collapse()

        for(Quest:ActiveQuests):
            if(QuestsCounter >= QuestsStartId 
                QuestsCounter < QuestsEndId
                PanelCounter < QuestsPerPage
                Panel := Ui.Panels[PanelCounter]
                Data := Quest.Data
            ):
                Panel.SingleQuestOverlay.Show()
                Panel.TextReward.SetText(ToMsg(Data.GoldReward))
                QuestName := Loc.G(Agent, Data.ShortName? or Data.Name)
                Panel.TextQuestDesc.SetText(ToMsg(QuestName))
                set PanelCounter += 1
                
                Panel.ImageDone.Hide()
                Panel.TextReward.SetText(ToMsg(Quest.Data.GoldReward))
                Panel.StackBoxReward.Show()

            set QuestsCounter += 1
        
        # LPrint("CompletedQuests {CompletedQuests.Length}")
        for(Data:CompletedQuests):
            if(QuestsCounter >= QuestsStartId 
                QuestsCounter < QuestsEndId
                PanelCounter < QuestsPerPage
                Panel := Ui.Panels[PanelCounter]
            ):
                Panel.SingleQuestOverlay.Show()
                Panel.TextReward.SetText(ToMsg(Data.GoldReward))
                QuestName := Loc.G(Agent, Data.ShortName? or Data.Name)
                Panel.TextQuestDesc.SetText(ToMsg(QuestName))
                set PanelCounter += 1

                Panel.ImageDone.Show()
                Panel.StackBoxReward.Collapse()

            set QuestsCounter += 1


    AddQuestPanel(StackBox:stack_box, Widget:widget):void=
        StackBox.AddWidget(
            stack_box_slot:
                Padding := margin:
                    Bottom := -10.000000
                HorizontalAlignment := horizontal_alignment.Fill
                VerticalAlignment := vertical_alignment.Fill
                Widget := Widget
        )

    AfterRegistration(Agent:agent):void=
        Resources.G().ShowUi(Agent, true)
        ClassSelectorReg3.G().ChangeClass(Agent)
        

    InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
        LPrint("InitPlayerAsync PHONE")
        ClassSelectorNone.G().ChangeClass(Agent)
        Resources.G().HideUi(Agent)
        ItemGranterRemote.G().GrantItem(Agent)
        RemoteClickedEv := event(){}
        AgentToPrimarySignalClickedEv.Add(Agent, RemoteClickedEv)

        var IsRegistered :logic= false
        var SelectedCardId :int= 0

        var RefIsDepositRunning :ref_logic= ref_logic_c(false)

        var MId :?int= false

        IsRegisteredFromSave :logic= Save.G().GetLogicStat(Agent, "PhoneReg", false)
        LPrint("IsRegisteredFromSave: {IsRegisteredFromSave? and "yes" or "no"}")
        if(IsRegisteredFromSave?):
            AfterRegistration(Agent)
            set IsRegistered = true
        
        loop:
            race:
                RemoteClickedEv.Await()
                PhoneTriggeredEv.AwaitFor(Agent)
                # InputTriggerOpenPhone.G().PressedEvent.AwaitFor(Agent)
                block:
                    PlayerRemoved.Await()
                    AgentToPrimarySignalClickedEv.Remove(Agent)
                    return

            if(not IsRegistered?):
                set MId = PopupMenuFirst.G().ShowAwaitForButtonOrExitAndHideWithTimout(Agent, PlayerRemoved, 10.0)
                # Sleep(0.1)
                # if(Id := MId?):
                # 	case (Id):
                # 		0 =>
                # 		1 =>
                set MId = PopupRegister1.G().ShowAwaitForButtonOrExitAndHideWithTimout(Agent, PlayerRemoved, 10.0)
                # Sleep(0.1)

                # if(Id = MId?):
                ClassSelectorReg2.G().ChangeClass(Agent)
                # Reg2Ui := make_ui_phone_reg2_generated()
                # Agent.AddToPlayerUi(Reg2Ui.ui_phone_reg2, true, 20)
                # Reg2Ui.ButtonCard.OnClick().Await()
                # Sleep(0.0)
                # Agent.RemoveFromPlayerUi(Reg2Ui.ui_phone_reg2)
                # Sleep(1.0)
                # Sleep(0.1)

                set MId = PopupRegister2.G().ShowAwaitForButtonOrExitAndHideWithTimout(Agent, PlayerRemoved, 10.0)
                # Sleep(0.1)

                AfterRegistration(Agent)
                Save.G().SetLogicStat(Agent, "PhoneReg", true)
                # Sleep(0.0)
                # if(Id = MId?):
                # Sleep(0.1)
                set MId = PopupRegister3.G().ShowAwaitForButtonOrExitAndHideWithTimout(Agent, PlayerRemoved, 10.0)
                if(P_Account_Created := MP_Account_Created?):
                    P_Account_Created.Submit(Agent)
                else:
                    LPrint("P_Account_Created analytics device missing")
                # if(Id = MId?):
                set IsRegistered = true
                RegisteredAccountEv.Signal(Agent)
                # Resources.G().GiveGoldText(Agent, 10000, "Bonus!") from quests
                spawn. OpenMenuAfterSleep(RemoteClickedEv)

            else:
                set MId = PopupMenu.G().ShowAwaitForButtonOrExitAndHideWithTimout(Agent, PlayerRemoved, 10.0)
                if(Id := MId?):
                    case (Id):
                        1 => # Card selection
                            set MId = PopupCard.G().ShowAwaitForButtonOrExitAndHideWithTimout(Agent, PlayerRemoved, 10.0)
                            if(Id2 := MId?):
                                case (Id2):
                                    1 =>
                                        Resources.G().SetImageCardBg(Agent, VResourcesSystem.Assets.T_PlayerCard3)
                                        # set SelectedCardId = 0
                                        # ClassSelectorReg3.G().ChangeClass(Agent)
                                        # LPrint("SelectedCardId {SelectedCardId}")
                                    2 =>
                                        Resources.G().SetImageCardBg(Agent, VResourcesSystem.Assets.T_PlayerCard2)
                                        # set SelectedCardId = 1
                                        # LPrint("SelectedCardId {SelectedCardId}")
                                    3 =>
                                        Resources.G().SetImageCardBg(Agent, VResourcesSystem.Assets.T_PlayerCard)
                                    _ =>
                        2 =>
                            AchievUi := MakePlayerQuestsUi()
                            Agent.AddToPlayerUi(AchievUi.Ui.PhoneAchievements, true, 6)
                            HandleQuestsUiAsync(Agent, AchievUi)
                            Agent.RemoveFromPlayerUi(AchievUi.Ui.PhoneAchievements)
                        3 =>
                            set MId = PopupInvest1.G().ShowAwaitForButtonOrExitAndHideWithTimout(Agent, PlayerRemoved, 10.0)
                            if(Id2 := MId?):
                                InvestUi := make_invest2_generated()
                                Agent.AddToPlayerUi(InvestUi.invest2, true, 20)
                                Sleep(0.05)
                                MInvestData := Agent.HandleInvestPanel(InvestUi)
                                if(InvestData := MInvestData?):
                                    if(not RefIsDepositRunning.Val?):
                                        Amount := InvestData.Amount
                                        Time := InvestData.Time
                                        EarnAmount := InvestData.EarnAmount
                                        spawn. InvestAsync(Agent, InvestData.EarnAmount, Time, RefIsDepositRunning)
                                    else:
                                        Resources.G().ShowRedHudMessage(Agent, "Deposit has already been made!")
                                Agent.RemoveFromPlayerUi(InvestUi.invest2)
                            # if(Id = MId?):
                            # 	set MId = PopupInvest2.G().ShowAwaitForButtonOrExitAndHide(Agent, PlayerRemoved)
                        _ =>

    HandleQuestsUiAsync(Agent:agent, AchievUi:achievements_ui_phone)<suspends>:void=
        var Page :int= 0
        race:
            loop:
                UpdatePlayerQuestsUi(Agent, Page, AchievUi)
                ShouldBreak :logic= race:
                    block:
                        AchievUi.Ui.ButtonMore.OnClick().Await()
                        false
                    block:
                        Sleep(10.0)
                        true
                if(ShouldBreak?):
                    break
                
                NewPage := Page + 1
                set Page = NewPage
            AchievUi.Ui.ButtonExitt.OnClick().Await()
        
    InvestAsync(Agent:agent, Amount:int, Mins:int, RefIsDepositRunning:ref_logic)<suspends>:void=
        RefIsDepositRunning.Set(true)
        MadeDepositEv.Signal(Agent)
        # TimeInSec :float= Mins * 60.0
        # GrantIntervalInSec :float= 10.0
        
        
        # if(var TotalGrantsLeft :int= Ceil[TimeInSec / GrantIntervalInSec]
        # 	AmountPerGrant :int= Floor[Amount / (TotalGrantsLeft * 1.0)]
        # ):
            # loop:
            # 	if(TotalGrantsLeft = 0):
            # 		break
            # 	set TotalGrantsLeft -= 1
                # Resources.G().GiveGoldText(Agent, AmountPerGrant, "Investment")
                # Sleep(GrantIntervalInSec)

        # else:
        # 	LError()
        MiniPhone := make_mini_phone_timer_generated()
        Agent.AddToPlayerUi(MiniPhone.MiniPhoneTimer)
        var TimeInSec :float= Mins * 60.0
        TimerText := MiniPhone.TimerText
        MiniPhone.TimerText_1.SetText("${Amount.ToShortNumberString()}".ToMessage())
        loop:
            TimerText.SetText(TimeInSec.TimeToStringMinsAndSeconds().ToMessage())
            if(TimeInSec <= 0.0):
                break
            Sleep(1.0)
            set TimeInSec -= 1.0
        Resources.G().GiveGoldText(Agent, Amount, "Investment")
        Agent.RemoveFromPlayerUi(MiniPhone.MiniPhoneTimer)
        RefIsDepositRunning.Set(false)

        
    OpenMenuAfterSleep(Ev:event())<suspends>:void=
        Sleep(0.3)
        Ev.Signal()
    

    (Agent:agent).HandleInvestPanel(Ui:invest2_generated)<suspends>:?invest_data=
        var IdAmount :int= 0
        var IdTime :int= 0
        var FinalAmount :int= Ui.UpdateInvestPanel(Agent, IdAmount, IdTime)
        ResetTimoutEv:event() = event(){}
        race:
            loop:
                Break :logic= race:
                    block:
                        ResetTimoutEv.Await()
                        false
                    block:
                        Sleep(30.0)
                        true
                if(Break?):
                    return false
            block:
                Ui.ButtonExit.OnClick().Await()
                return false
            loop:
                Ui.ButtonInvest.OnClick().Await()
                ResetTimoutEv.Signal()
                Amount := InvestAmount[IdAmount] or Err()
                TookGold := Resources.G().TryToTakeGold(Agent, Amount)
                if(TookGold?):
                    break
            loop:
                Ui.BtnLessCash.OnClick().Await()
                ResetTimoutEv.Signal()
                set IdAmount = DecRotateIndex[IdAmount, InvestAmount.Length] or Err()
                set FinalAmount = Ui.UpdateInvestPanel(Agent,IdAmount, IdTime)
            loop:
                Ui.BtnMoreCash.OnClick().Await()
                ResetTimoutEv.Signal()
                set IdAmount = IncRotateIndex[IdAmount, InvestAmount.Length] or Err()
                set FinalAmount = Ui.UpdateInvestPanel(Agent,IdAmount, IdTime)
            loop:
                Ui.BtnLessTime.OnClick().Await()
                ResetTimoutEv.Signal()
                set IdTime = DecRotateIndex[IdTime, InvestTime.Length] or Err()
                set FinalAmount = Ui.UpdateInvestPanel(Agent,IdAmount, IdTime)
            loop:
                Ui.BtnMoreTime.OnClick().Await()
                ResetTimoutEv.Signal()
                set IdTime = IncRotateIndex[IdTime, InvestTime.Length] or Err()
                set FinalAmount = Ui.UpdateInvestPanel(Agent,IdAmount, IdTime)

        return option. invest_data:
            Amount := InvestAmount[IdAmount]
            Time := InvestTime[IdTime]
            EarnAmount := FinalAmount

    (Ui:invest2_generated).UpdateInvestPanel(Agent:agent, IdAmount:int, IdTime:int):int=
        Amount := InvestAmount[IdAmount] or Err()
        Time := InvestTime[IdTime] or Err()

        Ui.TextTime.SetText("{Time}min".ToMessage())
        Ui.TextCash.SetText("${Amount}".ToMessage())

        # https://www.wolframalpha.com/input?i=quadratic+fit+%7B%7B1%2C+0.10%7D%2C+%7B3%2C+0.36%7D%2C+%7B5%2C+0.65%7D%2C+%7B10%2C+1.40%7D%7D
        x := Time
        Bonus := (0.00153878 * (x*x) + 0.127822*x - 0.0315878) * 3
        Multi := Time + Bonus

        FinalAmount := Ceil[Amount * Multi] or block:
            LError()
            Amount

        if(Loc.GetLanguage(Agent) = lang.Pol):
            Ui.TextInvestReturn.SetText("Otrzymaj ${FinalAmount} po {Time} {Time = 1 and "minucie" or "minutach"}".Wrap(20).ToMessage())
        else:
            Ui.TextInvestReturn.SetText("Get ${FinalAmount} back in {Time} {Time = 1 and "minute" or "minutes"}".Wrap(20).ToMessage())


        return FinalAmount



InvestAmount :[]int= array:
    100
    1000
    10000
    100000

InvestTime :[]int= array:
    1
    3
    5
    10
    20

invest_data := struct():
    Amount<public>:int
    EarnAmount<public>:int
    Time<public>:int

achievements_ui_phone := class:
    Ui:phone_achievements_generated
    Panels:[]VPhone.single_quest_overlay_generated

            

# button_custom<public> := class(text_button_base){}
