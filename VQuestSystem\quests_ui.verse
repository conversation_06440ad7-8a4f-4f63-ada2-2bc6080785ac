
# using {/Verse.org/Simulation}
# using {/Verse.org/Simulation/Tags}
# using {/Verse.org/Assets}
# using {/Verse.org/Verse}
# using {/Verse.org/Random}
# using {/Verse.org/Colors}
# using {/Verse.org/Colors/NamedColors}
# using {/Verse.org/Native}
# using {/Verse.org/Concurrency}
# using {/UnrealEngine.com/Temporary}
# using {/UnrealEngine.com/Temporary/UI}
# using {/UnrealEngine.com/Temporary/SpatialMath}
# using {/UnrealEngine.com/Temporary/Diagnostics}
# using {/UnrealEngine.com/Temporary/Curves}
# using {/Fortnite.com/UI}
# using {/Fortnite.com/Devices}
# using {/Fortnite.com/Devices/CreativeAnimation}
# using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
# using {/Fortnite.com/Vehicles}
# using {/Fortnite.com/Teams}
# using {/Fortnite.com/Playspaces}
# using {/Fortnite.com/Game}
# using {/Fortnite.com/FortPlayerUtilities}
# using {/Fortnite.com/Characters}
# using {/Fortnite.com/AI}
# using { VGiga }

# quests_ui_c():quests_ui={
# 	StackQuests := stack_box{
# 		Orientation := orientation.Vertical
# 		Slots := array{}
# 	}
# 	# Overlay := overlay{
# 	# 	Slots := array{
# 	# 		overlay_slot{
# 	# 			HorizontalAlignment := horizontal_alignment.Fill
# 	# 			VerticalAlignment := vertical_alignment.Fill
# 	# 			Widget := color_block{
# 	# 				DefaultColor := NamedColors.Black
# 	# 				DefaultOpacity := 0.5
# 	# 			}
# 	# 		}
# 	# 		overlay_slot{
# 	# 			HorizontalAlignment := horizontal_alignment.Fill
# 	# 			VerticalAlignment := vertical_alignment.Fill
# 	# 			Widget := StackQuests
# 	# 		}
# 	# 	}
# 	# }

# 	# ButtonLeft := button_regular{
# 	# 	DefaultText := "<-".ToMessage()
# 	# }

# 	# TextMoneyGainBg := color_block{
# 	# 	DefaultColor := NamedColors.Black
# 	# 	DefaultDesiredSize := Vector2(460.0, 50.0)
# 	# }
# 	# TextMoneyGain := text_block{
# 	# 	DefaultJustification := text_justification.Center
# 	# 	DefaultTextColor := NamedColors.White
# 	# }
# 	# TextMoneyGain.SetShadowOffset(option. vector2{X := 1.0, Y := 1.0})
# 	# TextMoneyGain.SetShadowOpacity(1.0)

# 	BtnSize := 0.1
# 	Canvas := canvas{
# 		Slots := array{
# 			canvas_slot{
# 				Anchors := Anchors(0.5,0.5,1.0,1.0)
# 				Alignment := Vector2(0.5,1.0)
# 				# Anchors := Anchors(0.5,0.5,0.95,0.95)
# 				Offsets := Margin(0.0,0.0,0.0,0.0)
# 				# Alignment := Vector2(0.5,1.0)
# 				Widget := StackQuests
# 				SizeToContent := true
# 			}
# 			# canvas_slot{
# 			# 	Anchors := Anchors(0.0,0.3,0.5,0.6)
# 			# 	Alignment := Vector2(0.0,0.0)
# 			# 	Widget := color_block{
# 			# 		DefaultColor := NamedColors.MintCream
# 			# 		DefaultOpacity := 0.7
# 			# 	}
# 			# 	SizeToContent := true
# 			# }
# 			# canvas_slot{
# 			# 	Anchors := Anchors(0.0,0.3,0.7,0.8)
# 			# 	Alignment := Vector2(0.0,0.0)
# 			# 	Widget := color_block{
# 			# 		DefaultColor := NamedColors.MintCream
# 			# 		DefaultOpacity := 0.7
# 			# 	}
# 			# 	SizeToContent := false
# 			# }
# 		}
# 	}
# 	quests_ui{
# 		Canvas := Canvas
# 		StackQuests := StackQuests
# 		# Overlay := Overlay
# 		# TextMoneyGain := TextMoneyGain
# 		# Loc := Loc
# 	}
# }

# quests_ui := class(){
# 	Canvas:canvas
# 	# Overlay:overlay
# 	StackQuests:stack_box
# 	# TextMoneyGain:text_block
# 	# Loc: localization_manager

# 	# SetMoneyGain(Value:float, Agent:agent):void={
# 	# 	if(Rounded := Round[Value]){
# 	# 		TextMoneyGain.SetText("+{Rounded}% {Loc.G(Agent, "MONEY/FANS GAIN")}".ToMessage())
# 	# 	}else{LError()}
# 	# }
# }