using. /Fortnite.com/AI
using. /Fortnite.com/Characters
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Game
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Teams
using. /Fortnite.com/UI
using. /Fortnite.com/Vehicles
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/Curves
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/UI
using. /Verse.org/Assets
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Concurrency
using. /Verse.org/Native
using. /Verse.org/Random
using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Verse
using. VAudio
using. VCustomBillboard
using. VGiga
using. VResourcesSystem



# player invests in an app that then generates money
app_investment<public> := class(auto_creative_device, i_init_async):
    @editable ButtonInvest:?button_device = false
    @editable TransactionsAmountBillboard:?custom_billboard_editable = false

    var Resources:?resources_manager = false
    var MAgentOwner:?agent = false
    var Save :?save_system= false

    var AmountInvestedToReturn:float = 0.0
    var AppTransactions:float = 0.0

    InvestGainMsg<localizes>:message = "Aplikacja"

    InitAsync<override>(Container:vcontainer)<suspends>:void=
        set Resources = Container.ResolveOp[resources_manager] or Err()
        set Save = Container.ResolveOp[save_system] or Err()
        spawn. HandleButtonActivations()
        spawn. HandleInvestmentGains()

    AgentClaimed<public>(Agent:agent):void=
        set MAgentOwner = option. Agent
        if(Val := Save.G().GetFloatStat[Agent, "AppInvestReturn"]):
            set AmountInvestedToReturn = Val
        if(Val := Save.G().GetFloatStat[Agent, "AppTransactions"]):
            set AppTransactions = Val
        UpdateBillboard()

    HandleButtonActivations()<suspends>:void=
        loop:
            Agent := ButtonInvest.G().InteractedWithEvent.Await()
            Res := Resources.G().TryToTakeGold(Agent, 1000)
            if(Res?):
                set AmountInvestedToReturn += 1000.0
                Save.G().SetFloatStat(Agent, "AppInvestReturn", AmountInvestedToReturn)

    HandleInvestmentGains()<suspends>:void=
        loop:
            Sleep(GetRandomFloat(10.0,20.0))
            
            if(Agent := MAgentOwner?
                AmountInvestedToReturn >= 1.0
                MoneyGain :int= Ceil[AmountInvestedToReturn * 0.025]
            ):
                set AmountInvestedToReturn -= MoneyGain * 1.0
                set AppTransactions += Min(9223372036854775.0, AppTransactions + MoneyGain * 25.0)
                Save.G().SetFloatStat(Agent, "AppTransactions", AppTransactions)
                Resources.G().GiveGoldMsg(Agent, MoneyGain, InvestGainMsg, ?ShowNotification:= true)
                UpdateBillboard()

    UpdateBillboard():void=
        if(AppTransactionsInt := Ceil[AppTransactions]):
            TransactionsAmountBillboard.G().SetTextGreen("${AppTransactionsInt.ToShortNumberString()}")
            # AppTransaction := InvestAmount * 0.1
            # set AppTransactions += AppTransaction
            # if(AppTransactionsInt := Ceil[AppTransactions]):
            #     TransactionsAmountBillboard.G().SetTextGreen("${AppTransactionsInt.ToShortNumberString()}")
            # MoneyGain := AppTransaction * 0.04
            # if(Agent := MAgentOwner?, Gain := Ceil[MoneyGain]):
            #     Resources.G().GiveGoldMsg(Agent, Gain, InvestGainMsg, ?ShowNotification := true)
