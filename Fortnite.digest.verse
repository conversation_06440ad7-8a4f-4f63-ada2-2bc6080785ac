# Copyright Epic Games, Inc. All Rights Reserved.
#################################################
# Generated Digest of Verse API
# DO NOT modify this manually!
# Generated from build: ++Fortnite+Release-35.10-CL-42906078
#################################################

using {/Verse.org/Colors}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/Fortnite.com/Playspaces}
using {/Verse.org/Simulation}
using {/UnrealEngine.com/Temporary/UI}
# Module import path: /Fortnite.com/UI
(/Fortnite.com:)UI<public> := module:
    # Button with text message common base class. Displays a button with a custom message string.
    text_button_base<native><public> := class<abstract><epic_internal>(widget):
        # The text to display to the user. Used only during initialization of the widget and not modified by SetText.
        (/Fortnite.com/UI/text_button_base:)DefaultText<localizes><native><public>:message = external {}

        # Subscribable event that fires when the button is clicked.
        OnClick<public>():listenable(widget_message) = external {}

        # Sets the text displayed in the widget.
        SetText<native><public>(InText:message):void

        # Gets the text currently in the widget.
        GetText<native><public>():string

    # Text button with big and loud styling applied.
    button_loud<native><public> := class<final>(text_button_base):

    # Text button with normal styling applied.
    button_regular<native><public> := class<final>(text_button_base):

    # Text button with quiet styling applied.
    button_quiet<native><public> := class<final>(text_button_base):

    @deprecated
    # Deprecated. This function affects all players. Please use `fort_playspace.GetHUDController().ShowElements()`.
    # Shows a set of HUD elements.
    (PlayerUI:player_ui).ShowHUDElements<native><public>(HUDElements:[]hud_element_identifier):void

    @deprecated
    # Deprecated. This function affects all players. Please use `fort_playspace.GetHUDController().HideElements()`.
    # Hides a set of HUD elements.
    (PlayerUI:player_ui).HideHUDElements<native><public>(HUDElements:[]hud_element_identifier):void

    @deprecated
    # Deprecated. This function affects all players. Please use `fort_playspace.GetHUDController().ResetElementVisibility()`.
    # Resets the visibility for a set of HUD elements.
    (PlayerUI:player_ui).ResetHUDElementVisibility<native><public>(HUDElements:[]hud_element_identifier):void

    # A HUD controller that allows for showing and hiding of HUD elements.
    fort_hud_controller<native><public> := interface<epic_internal>:
        # Shows a set of HUD elements for every player. Note: This can be overridden by rules set by 'ForPlayer' functions since player specific rules are prioritized over general rules.
        ShowElements<public>(HUDElements:[]hud_element_identifier):void

        # Hides a set of HUD elements for every player. Note: This can be overridden by rules set by 'ForPlayer' functions since player specific rules are prioritized over general rules.
        HideElements<public>(HUDElements:[]hud_element_identifier):void

        # Resets the visibility for a set of HUD elements for every player. Note: This will not clear player specific rules set by the 'ForPlayer' functions which can only be reset by calling 'ResetElementsForPlayer'.
        ResetElementVisibility<public>(HUDElements:[]hud_element_identifier):void

        # Shows a set of HUD elements for a single player. Note: This overrides general rules set by non-player functions for the given elements. Call 'ResetElementsForPlayer' in order to return the player to general behavior
        ShowElementsForPlayer<public>(Player:player, HUDElements:[]hud_element_identifier):void

        # Hides a set of HUD elements for a single player. Note: This overrides general rules set by non-player functions for the given elements. Call 'ResetElementsForPlayer' in order to return the player to general behavior
        HideElementsForPlayer<public>(Player:player, HUDElements:[]hud_element_identifier):void

        # Resets the player-specific visibility rules of a set of HUD elements for a single player. Note: This will not reset rules that have been set by means other than the 'PerPlayer' functions.
        ResetElementsForPlayer<public>(Player:player, HUDElements:[]hud_element_identifier):void

    # Get the `fort_hud_controller` for the current `fort_playspace`.
    (Playspace:fort_playspace).GetHUDController<native><public>():fort_hud_controller

    creative_hud_identifier_all<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_build_menu<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_crafting_resources<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_elimination_counter<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_equipped_item<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_experience_level<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_experience_supercharged<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_experience_ui<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_health<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_health_numbers<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_hud_info<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_interaction_prompts<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_map_prompts<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_mimimap<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_minimap<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_pickup_stream<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_player_count<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_player_inventory<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_round_info<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_round_timer<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_shield_numbers<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_shileds<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_shields<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_storm_notifications<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_storm_timer<public> := class<final>(hud_element_identifier):

    creative_hud_identifier_team_info<public> := class<final>(hud_element_identifier):

    player_hud_identifier_all<public> := class<final>(hud_element_identifier):

    hud_identifier_world_resource_wood<public> := class<final>(hud_element_identifier):

    hud_identifier_world_resource_stone<public> := class<final>(hud_element_identifier):

    hud_identifier_world_resource_metal<public> := class<final>(hud_element_identifier):

    hud_identifier_world_resource_permanite<public> := class<final>(hud_element_identifier):

    hud_identifier_world_resource_gold_currency<public> := class<final>(hud_element_identifier):

    hud_identifier_world_resource_ingredient<public> := class<final>(hud_element_identifier):

    hud_identifier_visual_sound_effect_weapons<public> := class<final>(hud_element_identifier):

    hud_identifier_visual_sound_effect_loot<public> := class<final>(hud_element_identifier):

    hud_identifier_visual_sound_effect_movement<public> := class<final>(hud_element_identifier):

    hud_identifier_visual_sound_effect_vehicle<public> := class<final>(hud_element_identifier):

    hud_identifier_visual_sound_effect_healing<public> := class<final>(hud_element_identifier):

    hud_identifier_visual_sound_effect_all<public> := class<final>(hud_element_identifier):

    # Used to identify a HUD element.
    hud_element_identifier<native><public> := class<abstract><epic_internal>:

    # Slider with a text value. Displays a slider, its progress bar and value.
    slider_regular<native><public> := class<final>(widget):
        # The value to display to the user. Used only during initialization of the widget and not modified by SetValue.
        DefaultValue<native><public>:float = external {}

        # The minimum value that the slider can haver. Used only during initialization of the widget and not modified by SetMinValue.
        DefaultMinValue<native><public>:float = external {}

        # The maximum value that the slider can haver. Used only during initialization of the widget and not modified by SetMaxValue.
        DefaultMaxValue<native><public>:float = external {}

        # The amount to adjust the value by, when using a controller or keyboard. Used only during initialization of the widget and not modified by SetStepSize.
        DefaultStepSize<native><public>:float = external {}

        # Sets the value of the slider, will clamp the value to be within the sliders minimum and maximum value.
        SetValue<native><public>(InValue:float):void

        # Gets the value of the slider.
        GetValue<native><public>():float

        # Sets the minimum value of the slider, will enforce that the sliders maximum value is always larger than or equal to the minimum value.
        SetMinValue<native><public>(InMinValue:float):void

        # Gets the minimum value of the slider.
        GetMinValue<native><public>():float

        # Sets the maximum value of the slider, will enforce that the sliders maximum value is always larger than or equal to the minimum value.
        SetMaxValue<native><public>(InMaxValue:float):void

        # Gets the maximum value of the slider.
        GetMaxValue<native><public>():float

        # Sets the amount to adjust the value by, when using a controller or keyboard.
        SetStepSize<native><public>(InValue:float):void

        # Gets the amount to adjust the value by.
        GetStepSize<native><public>():float

        # Subscribable event that fires when the value of the slider has changed.
        OnValueChanged<public>():listenable(widget_message) = external {}

    # Text block widget. Displays text to the user.
    text_block<native><public> := class<final>(text_base):
        # The direction the shadow is cast. Used only during initialization of the widget and not modified by SetShadowOffset.
        DefaultShadowOffset<native><public>:?vector2 = external {}

        # The color of the shadow. Used only during initialization of the widget and not modified by SetShadowColor.
        DefaultShadowColor<native><public>:color = external {}

        # The opacity of the shadow. Used only during initialization of the widget and not modified by SetShadowOpacity.
        DefaultShadowOpacity<native><public>:type {_X:float where 0.000000 <= _X, _X <= 1.000000} = external {}

        # Sets the direction the shadow is cast.
        SetShadowOffset<native><public>(InShadowOffset:?vector2):void

        # Gets the direction the shadow is cast.
        GetShadowOffset<native><public>():?vector2

        # Sets the color of the shadow.
        SetShadowColor<native><public>(InColor:color):void

        # Gets the color of the shadow.
        GetShadowColor<native><public>():color

        # Sets the opacity of the shadow.
        SetShadowOpacity<native><public>(InOpacity:type {_X:float where 0.000000 <= _X, _X <= 1.000000}):void

        # Gets the opacity of the shadow.
        GetShadowOpacity<native><public>():type {_X:float where 0.000000 <= _X, _X <= 1.000000}

using {/Verse.org/Assets}
using {/Fortnite.com/AI}
using {/Verse.org/Native}
using {/Verse.org/Simulation/Tags}
using {/Fortnite.com/Game}
using {/Verse.org/SceneGraph}
# Module import path: /Fortnite.com/Devices
Devices<public> := module:
    # Module import path: /Fortnite.com/Devices/CreativeAnimation
    CreativeAnimation<public> := module:
        # A structure for defining Bezier interpolation parameters.
        # See https://en.wikipedia.org/wiki/B%C3%A9zier_curve for more info on Bezier curves.
        cubic_bezier_parameters<native><public> := struct<computes>:
            # X value of the P1 control point. `0.0 <= X0 <= 1.0` or an error will be generated when calling `animation_controller.SetAnimation`.
            X0<native><public>:float = external {}

            # Y value of the P1 control point.
            Y0<native><public>:float = external {}

            # X value of the P2 control point. `0.0 <= X1 <= 1.0 or an error will be generated when calling `animation_controller.SetAnimation`.
            X1<native><public>:float = external {}

            # Y value of the P2 control point.
            Y1<native><public>:float = external {}

        # Module import path: /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
        # Convenience interpolation modes. These built in modes are based on the CSS animation standard: https://www.w3.org/TR/css-easing-1/
        InterpolationTypes<public> := module:
            # `Linear` animations move at a constant speed.
            Linear<public>:cubic_bezier_parameters = external {}

            # `Ease` animations start slowly, speed up, then end slowly. The speed of the animation is slightly slower at the end than the start.
            Ease<public>:cubic_bezier_parameters = external {}

            # `EaseIn` animations start slow, then speed up towards the end.
            EaseIn<public>:cubic_bezier_parameters = external {}

            # `EaseOut` animations start fast, then slow down towards the end.
            EaseOut<public>:cubic_bezier_parameters = external {}

            # `EaseInOut` animations are similar to `Ease` but the start and end animation speed is symmetric.
            EaseInOut<public>:cubic_bezier_parameters = external {}

        # Instead of specifying the actual keyframe positions, we specify the keyframe *deltas*. This allows us to treat the initial position of the prop as keyframe 0 and avoid the question of how to get the prop to its initial location. For a `animation_mode.Loop` animation, the net rotation and translation must both be zero. Each delta is interpreted as a world-space transformation to be concatenated onto the previous transform(s).
        keyframe_delta<native><public> := struct:
            # Target position of the `creative_prop`. This is a world-space coordinate in cm, with the initial position of the `creative_prop` acting as coordinate (0,0).
            DeltaLocation<native><public>:(/UnrealEngine.com/Temporary/SpatialMath:)vector3

            # Target rotation for the `creative_prop`. Rotations are relative to the starting rotation of the `creative_prop`
            DeltaRotation<native><public>:(/UnrealEngine.com/Temporary/SpatialMath:)rotation

            # Target scale for the `creative_prop`. Scale is multiplicative to the starting Scale of the `creative_prop`
            DeltaScale<native><public>:(/UnrealEngine.com/Temporary/SpatialMath:)vector3 = external {}

            # Time in seconds the `creative_prop` should animate between its last frame and this frame.
            Time<native><public>:float

            # Interpolation mode for this `keyframe_delta`. See `InterpolationTypes` for standard interpolation options. See `cubic_bezier_parameters` for authoring custom interpolations. 
            # Default = `InterpolationTypes.Linear`
            Interpolation<native><public>:cubic_bezier_parameters = external {}

        # Animation play modes.
        animation_mode<native><public> := enum:
            # Stop after playing the animation once.
            OneShot
            # Reverse direction after reaching the final `keyframe_delta`, then play the animation in reverse.
            PingPong
            # Play the animation in a loop. This requires the animation ends exactly where it began.
            Loop

        # `animation_controller` states.
        animation_controller_state<native><public> := enum:
            # The target of the animation is not an animatable prop. This could be because:
            #  * It is not a `creative_prop` that can be animated.
            #  * It was disposed or otherwise destroyed.
            #  * It has the 'Register with Structural Grid' option set in UEFN.
            InvalidObject
            # No animation has been successfully set via `animation_controller.SetAnimation`, or that animation has been cleared by a failed call to `animation_controller.SetAnimation`.
            AnimationNotSet
            # Animation has either never started, finished, or was explicitly stopped.
            Stopped
            # Animation is playing.
            Playing
            # Animation is paused.
            Paused

        # Results for `animation_controller.AwaitNextKeyframe` function.
        await_next_keyframe_result<native><public> := enum:
            # The next keyframe has been reached successfully.
            KeyframeReached
            # No animation is currently playing and this function call has returned immediately.
            NotPlaying
            # The animation was canceled either due to the object being destroyed, becoming invalid, or because it was moved via some other API.
            AnimationAborted

        # Used to move and animate the position of `creative_prop` objects.
        #  * See `creative_prop.GetAnimationController` for information on acquiring an instance of an
        #    `animation_controller` for a given `creative_prop`.
        #  * See `SetAnimation` for details on authoring movement and animations.
        animation_controller<native><public> := class<epic_internal>:
            # Suspends at the callsite until the next `keyframe_delta` is finished. This will also return if the animation is aborted or not playing. See `await_next_keyframe_result` if your code needs to take different paths based on why `AwaitNextKeyframe` returned.
            AwaitNextKeyframe<native><public>()<suspends>:await_next_keyframe_result

            # Starts or resumes playback of the animation.
            Play<public>():void = external {}

            # Pauses the animation if it is already playing.
            Pause<public>():void = external {}

            # Stops playback and resets the animation to the first keyframe. Also resets the prop transform. Calling this method is valid while the animation is in the `Playing` or `Paused` states.
            Stop<public>():void = external {}

            # Returns the current state of this `animation_controller`.
            GetState<native><public>()<transacts>:animation_controller_state

            # Succeeds if this `animation_controller`s target is still valid (i.e., the target has not been disposed of either via `Dispose` or through any external system.)
            IsValid<native><public>()<transacts><decides>:void

            # Sets the animation for the `animation_controller`. Animations are processed in the order provided in `Keyframes`. See notes in `keyframe_delta` and `animation_mode` for more details on controlling the animations.
            SetAnimation<public>(Keyframes:[]keyframe_delta, ?Mode:animation_mode):void = external {}

            # Signaled each time a keyframe is reached. Callback(KeyframeIndex:int, InReverse:logic). Note that the KeyframeIndex in the callback is generally in [1, NumDeltaKeyframes] except that in a PingPong animation the final keyframe played in reverse is identified as index 0. This is because SetAnimation takes *delta* keyframes whereas this event notifies the listener that a specific keyframe has been reached.
            KeyframeReachedEvent<native><public>:listenable(tuple(int, logic)) = external {}

            # Signaled when the entire animation is complete. This will only fire for `OneShot` animations.
            MovementCompleteEvent<native><public>:listenable(tuple()) = external {}

            # Signaled when the state has changed. Use `GetState` to get the new state.
            StateChangedEvent<native><public>:listenable(tuple()) = external {}

        # Returns an `animation_controller` used to animate `Prop`. Only a subset of `creative_prop` types can be animated, though this may change in the future. A few examples of props that cannot be animated yet are:
        #  * Walls attached to buildings
        #  * Treasure chests
        #  * Loot llamas
        (Prop:creative_prop).GetAnimationController<public>()<transacts><decides>:animation_controller = external {}

    # Inherit from this to create a custom creative device. Inherited classes will appear in the UEFN content browser the next time Verse compiles. Instances of your derived `creative_device` can then be placed in the island by dragging them from the content browser into the scene.
    creative_device<native><public> := class<concrete>(creative_object_interface):
        # Override to add custom logic when the game experience begins.
        OnBegin<native_callable><public>()<suspends>:void = external {}

        # Override to add custom logic when the game experience ends. Any coroutines spawned inside `OnEnd` may never execute.
        OnEnd<native_callable><public>():void = external {}

        # Returns the transform of the `creative_device` with units in cm.
        GetTransform<override>()<transacts>:(/UnrealEngine.com/Temporary/SpatialMath:)transform = external {}

        # Teleports the `creative_device` to the specified `Position` and `Rotation`.
        TeleportTo<public>(Position:(/UnrealEngine.com/Temporary/SpatialMath:)vector3, Rotation:(/UnrealEngine.com/Temporary/SpatialMath:)rotation)<transacts><decides>:void = external {}

        # Teleports the `creative_device` to the specified location defined by `Transform`, also applies rotation and scale accordingly.
        TeleportTo<public>(Transform:(/UnrealEngine.com/Temporary/SpatialMath:)transform)<transacts><decides>:void = external {}

        # Moves the `creative_device` to the specified `Position` and `Rotation` over the specified time, in seconds. If an animation is currently playing on the `creative_device` it will be stopped and put into the `AnimationNotSet` state.
        MoveTo<public>(Position:(/UnrealEngine.com/Temporary/SpatialMath:)vector3, Rotation:(/UnrealEngine.com/Temporary/SpatialMath:)rotation, OverTime:float)<suspends>:move_to_result = external {}

        # Moves the `creative_device` to the specified `Transform` over the specified time, in seconds. If an animation is currently playing on the `creative_device` it will be stopped and put into the `AnimationNotSet` state.
        MoveTo<public>(Transform:(/UnrealEngine.com/Temporary/SpatialMath:)transform, OverTime:float)<suspends>:move_to_result = external {}

        # Shows this device in the world.
        Show<native><public>()<transacts>:void

        # Hides this device in the world.
        Hide<native><public>()<transacts>:void

    # Base class for creative_device.
    creative_device_base<native><public> := class<abstract><epic_internal>(creative_object):

    # Internal asset for representing creative devices.
    creative_device_asset<native><public> := class<computes><final><epic_internal>:

    @experimental
    # Returns the simulation entity at the root of the experience this `creative_device` is operating within.
    #   * The simulation entity is the rootmost entity in an experience.
    #   * Fails if this `creative_device` is not operating in a context with a valid SimulationEntity.
    (CreativeDevice:creative_device).(/Fortnite.com/Devices:)GetSimulationEntity<native><public>()<transacts><decides>:entity

    creative_object_interface<native><public> := interface<epic_internal>(positional):

    # Base class for creative devices and props.
    creative_object<native><public> := class<epic_internal>(creative_object_interface):
        # Returns the transform of the `creative_object` with units in cm. You must check `creative_object.IsValid` before calling this if there is a possibility the object has been disposed or destroyed by gameplay.
        # Otherwise a runtime error will result.
        GetTransform<override>()<transacts>:(/UnrealEngine.com/Temporary/SpatialMath:)transform = external {}

        # Teleports the `creative_object` to the specified `Position` and `Rotation`.
        TeleportTo<public>(Position:(/UnrealEngine.com/Temporary/SpatialMath:)vector3, Rotation:(/UnrealEngine.com/Temporary/SpatialMath:)rotation)<transacts><decides>:void = external {}

        # Teleports the `creative_object` to the specified location defined by `Transform`, also applies rotation and scale accordingly.
        TeleportTo<public>(Transform:(/UnrealEngine.com/Temporary/SpatialMath:)transform)<transacts><decides>:void = external {}

        # Moves the `creative_object` to the specified `Position` and `Rotation` over the specified time, in seconds. If an animation is currently playing on the `creative_object` it will be stopped and put into the `AnimationNotSet` state.
        MoveTo<public>(Position:(/UnrealEngine.com/Temporary/SpatialMath:)vector3, Rotation:(/UnrealEngine.com/Temporary/SpatialMath:)rotation, OverTime:float)<suspends>:move_to_result = external {}

        # Moves the `creative_object` to the specified `Transform` over the specified time, in seconds. If an animation is currently playing on the `creative_object` it will be stopped and put into the `AnimationNotSet` state.
        MoveTo<public>(Transform:(/UnrealEngine.com/Temporary/SpatialMath:)transform, OverTime:float)<suspends>:move_to_result = external {}

    @available {MinUploadedAtFNVersion := 2930}
    # Generates a `creative_object_interface` for every creative object that has been marked with the specified `Tag`. Will not find anything if called on a default constructed object.
    (InCreativeDevice:creative_device).FindCreativeObjectsWithTag<public>(Tag:tag)<transacts>:generator(creative_object_interface) = external {}

    @available {MinUploadedAtFNVersion := 2930}
    # Generates a `creative_object_interface` for every creative object that has been marked with the specified `Tag`. Will not find anything if called on a default constructed object.
    (InCreativeDevice:creative_device_base).FindCreativeObjectsWithTag<public>(Tag:tag)<transacts>:generator(creative_object_interface) = external {}

    @available {MinUploadedAtFNVersion := 3300}
    # Generates a `creative_object_interface` for every creative object that has been marked with the specified `Tag`. Will not find anything if called on an `npc_behavior` that is not simulating.
    (InNPCBehavior:npc_behavior).FindCreativeObjectsWithTag<native><public>(Tag:tag)<transacts>:generator(creative_object_interface)

    @experimental
    @available {MinUploadedAtFNVersion := 2930}
    # Generates a `creative_object_interface` for every creative object that has been marked with the specified `Tag`. Will not find anything if called on an `entity` that is not in the scene.
    (InEntity:entity).FindCreativeObjectsWithTag<native><public>(Tag:tag)<transacts>:generator(creative_object_interface)

    @deprecated
    # Deprecated, use `FindCreativeObjectsWithTag` instead.
    # Returns an array containing all creative objects which have been marked with the specified `Tag`.
    GetCreativeObjectsWithTag<native><public>(Tag:tag)<transacts>:[]creative_object_interface

    @deprecated
    # Deprecated, use `FindCreativeObjectsWithTag` instead.
    # Returns an array containing creative objects which have tag(s) matching the specified `SearchCriteria`.
    GetCreativeObjectsWithTags<native><public>(SearchCriteria:tag_search_criteria)<transacts>:[]creative_object_interface

    # Returns a queryable `tag_view` which can be used to query the tags on `CreativeObject`.
    (CreativeObject:creative_object_interface).GetTags<native><public>()<transacts>:tag_view

    # Returns the `fort_playspace` that `CreativeObject` belongs to.
    (CreativeObject:creative_object_interface).GetPlayspace<native><public>()<transacts>:fort_playspace

    @experimental
    # Returns the simulation entity at the root of the experience this `creative_object` is operating within.
    #   * The simulation entity is the rootmost entity in an experience.
    #   * Fails if this `creative_object` is not operating in a context with a valid SimulationEntity.
    (CreativeObject:creative_object).(/Fortnite.com/Devices:)GetSimulationEntity<native><public>()<transacts><decides>:entity

    # A Fortnite prop that has been placed or spawned in the island.
    creative_prop<native><public> := class<concrete><final>(creative_object, invalidatable):
        # Destroys the `creative_prop` and remove it from the island.
        Dispose<native><override>():void

        # Succeeds if this object has been disposed of either via `Dispose()` or through an external system.
        IsDisposed<native><final><public>()<transacts><decides>:void

        # Succeeds if this object has not been disposed of either via `Dispose()` or through an external system.
        IsValid<native><override>()<transacts><decides>:void

        # Changes the Mesh used by this instance.
        SetMesh<native><public>(Mesh:mesh)<transacts>:void

        # Changes the Material of the Mesh used by this instance. Optionally can specify which Mesh element index to apply the material to, otherwise defaults to the 0 (default) Mesh element
        SetMaterial<native><public>(Material:material, ?Index:int = external {})<transacts>:void

        # Shows the `creative_prop` in the world and enable collisions.
        Show<native><public>()<transacts>:void

        # Hides the `creative_prop` in the world and disable collisions.
        Hide<native><public>()<transacts>:void

        # Enable/disable whether this prop can be damaged. If disabled, the creative prop will not take damage from attacks.
        var CanBeDamaged<public>:logic = external {}

    # Asset used to spawn `creative_prop` instances.
    creative_prop_asset<native><public> := class<computes><concrete><final><epic_internal>:

    # A default asset to be used when creating an editor-specified `creative_prop_asset` variable.
    DefaultCreativePropAsset<public>:creative_prop_asset = external {}

    # Results for `SpawnProp`.
    spawn_prop_result<native><public> := enum:
        # Success.
        Ok
        # An unknown error occurred. If this happens frequently, contact Epic support.
        UnknownError
        # The spawn point contains NaN or Inf.
        InvalidSpawnPoint
        # The spawn point is outside the island's boundaries.
        SpawnPointOutOfBounds
        # The asset is not a valid `creative_prop`.
        InvalidAsset
        # More props have been spawned than are permitted by the island's rules (currently 100 per script device and 200 total per island).
        TooManyProps

    # Spawns a `creative_prop` at the specified `Position` and `Rotation`. `Position` and `Rotation` units are in cm.
    # Returns tuple:
    #  0: Instance of a `creative_prop`. False if no `creative_prop` could be created. See `spawn_prop_result` for failure cases.
    #  1: Success or failure results.
    SpawnProp<native><public>(Asset:creative_prop_asset, Position:(/UnrealEngine.com/Temporary/SpatialMath:)vector3, Rotation:(/UnrealEngine.com/Temporary/SpatialMath:)rotation)<transacts>:tuple(?creative_prop, spawn_prop_result)

    # Spawns a `creative_prop` at the specified `Transform`. `Units are in cm.
    # Returns tuple:
    #  0: Instance of a `creative_prop`. False if no `creative_prop` could be created. See `spawn_prop_result` for failure cases.
    #  1: Success or failure results.
    SpawnProp<native><public>(Asset:creative_prop_asset, Transform:(/UnrealEngine.com/Temporary/SpatialMath:)transform)<transacts>:tuple(?creative_prop, spawn_prop_result)

    # Payload of `device_event_ai_interaction`.
    device_ai_interaction_result<native><public> := struct<epic_internal>:
        # Optional agent that triggered the interaction
        Source<native><public>:?agent

        # Optional agent targeted by `Source`.
        Target<native><public>:?agent

    # Results for `MoveTo`.
    move_to_result<public> := enum:
        # The destination has been reached.
        DestinationReached
        # The destination will not be reached. See debug log in UEFN for more info.
        WillNotReachDestination

    # Used to select, spawn, and configure a skeletal mesh to play a specific animation.
    animated_mesh_device<public> := class<concrete><final>(creative_device_base):
        # Starts or resumes playback of the animation.
        Play<public>():void = external {}

        # Pauses playback of the animation.
        Pause<public>():void = external {}

        # Starts or resumes reverse playback of the animation.
        PlayReverse<public>():void = external {}

    using {/Verse.org/Simulation}
    using {/Fortnite.com/Game}
    # Used to create a customizable turret that can search for nearby targets.
    automated_turret_device<public> := class<concrete><final>(creative_device_base, healthful, healable):
        GetHealth<override>()<transacts>:float = external {}

        SetHealth<override>(Health:float)<transacts>:void = external {}

        GetMaxHealth<override>()<transacts>:float = external {}

        SetMaxHealth<override>(MaxHealth:float)<transacts>:void = external {}

        Heal<override>(Amount:float):void = external {}

        Heal<override>(Args:healing_args):void = external {}

        HealedEvent<override>():listenable(healing_result) = external {}

        # Triggers when someone enters the activation radius while nobody else is there.
        # Sends the activating `agent`. If the activator is a non-agent then `false` is returned.
        ActivatedEvent<public>:listenable(?agent) = external {}

        # Triggers when the turret is damaged.
        # Sends the triggering `agent`. If the activator is a non-agent then `false` is returned.
        DamagedEvent<public>:listenable(?agent) = external {}

        # Triggers when the turret is destroyed.
        # Sends the triggering `agent`. If the activator is a non-agent then `false` is returned.
        DestroyedEvent<public>:listenable(?agent) = external {}

        # Triggers when the turret finds a target.
        # Sends the `agent` that was found.
        TargetFoundEvent<public>:listenable(agent) = external {}

        # Triggers when the turret loses a target.
        # Sends the `agent` that was lost.
        TargetLostEvent<public>:listenable(agent) = external {}

        # Enables the turret to rotate, target, and track.
        Enable<public>():void = external {}

        # Disables the turret, causing it to close and ignore its activation radius.
        Disable<public>():void = external {}

        # Set the turret to the Default Team.
        #  * Only usable if `Possible Targets` is not set to `Everyone`.
        UseDefaultTeam<public>():void = external {}

        # Set the turret to the Wildlife & Creatures team.
        #  * Only usable if `Possible Targets` is not set to `Everyone`.
        UseTeamWildlifeAndCreatures<public>():void = external {}

        # Set the turret to the same team as the supplied `Agent`.
        #  * Only usable if `Possible Targets` is not set to `Everyone`.
        (/Fortnite.com/Devices/automated_turret_device:)SetTeam<public>(Agent:agent):void = external {}

        # Returns the `agent` currently targeted by the device.
        GetTarget<public>():?agent = external {}

        # Set the supplied `Agent` as the turret's target.
        #  * The target will only change if `Agent` is within the activation radius, has direct line-of-sight to the turret,
        # is on a targetable team as determined by `Possible Targets`, and is not Down But Not Out.
        SetTarget<public>(Agent:agent):void = external {}

        # Clears the turret's current target and returns the turret to searching for targets.
        #  * If the current target is still in range, it'll likely be the best target, and will be reacquired.
        #  * Combine with disabled targeting for best results.
        ClearTarget<public>():void = external {}

        # Sets the amount of damage the turret will do per shot to targets to `Damage`.
        SetDamage<public>(Damage:float):void = external {}

        # Sets the range in meters at which the turret will activate to `Range`.
        # This is clamped between `2.0` and `100.0` meters.
        SetActivationRange<public>(Range:float):void = external {}

        # Sets the range in meters at which the turret will target to `Range`.
        # This is clamped between `2.0` and `100.0` meters. Setting it lower than 2m will disable Targeting.
        SetTargetRange<public>(Range:float):void = external {}

    # Used to apply Post Process Effects to players through the creative device rather than a Post Process Volume.
    post_process_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when a blend in event has finished. Sends the `agent` that used this device.
        BlendingInCompleteEvent<public>:listenable(?agent) = external {}

        # Signaled when a blend out event has finished. Sends the `agent` that used this device.
        BlendingOutCompleteEvent<public>:listenable(?agent) = external {}

        # Enables this device only for the instigating `Agent`. 
        Enable<public>(Agent:agent):void = external {}

        # Enables this device for all players.
        Enable<public>():void = external {}

        # Disables this device only for the instigating `Agent`. 
        Disable<public>(Agent:agent):void = external {}

        # Disables this device for all players.
        Disable<public>():void = external {}

        # Starts blending in the post process effect to the set strength only for the instigating `Agent`. 
        BlendIn<public>(Agent:agent):void = external {}

        # Starts blending in the post process effect to the set strength for all players.
        BlendInForAll<public>():void = external {}

        # Starts blending out the post process effect to 0 strength only for the instigating `Agent`. 
        BlendOut<public>(Agent:agent):void = external {}

        # Starts blending out the post process effect to 0 strength for all players.
        BlendOutForAll<public>():void = external {}

        # Resets to the starting strength, ending any ongoing blending only for the instigating `Agent`.
        Reset<public>(Agent:agent):void = external {}

        # Resets to the starting strength, ending any ongoing blending for all players.
        ResetForAll<public>():void = external {}

    using {/Fortnite.com/Vehicles}
    # A one stop automated refueling and repairing station for your vehicles.
    service_station_device<public> := class<concrete><final>(creative_device_base, healthful, damageable, enableable):
        # Returns the health state of the object. This value will between 0.0 and `GetMaxHealth`
        GetHealth<override>()<transacts>:float = external {}

        # Sets the health state of the object to `Health`.
        #  * Health state will be clamped between 1.0 and `GetMaxHealth`.
        #  * Health state cannot be directly set to 0.0. To eliminate `healthful` objects use the `damageable.Damage` functions instead.
        SetHealth<override>(Health:float)<transacts>:void = external {}

        # Returns the maximum health of the object. This value will be between 1.0 and Inf.
        GetMaxHealth<override>()<transacts>:float = external {}

        # Sets the maximum health state of the object.
        #  * MaxHealth will be clamped between 1.0 and Inf.
        #  * Current health state will be scaled up or down based on the scale difference between the old and new MaxHealth state.
        SetMaxHealth<override>(MaxHealth:float)<transacts>:void = external {}

        # Damage the `damageable` object anonymously by `Amount`. Setting `Amount` to less than 0 will cause no damage.
        # Use `Damage(:damage_args):void` when damage is being applied from a known instigator and source.
        Damage<override>(Amount:float):void = external {}

        # Damage the `damageable` object by `Args.Amount`. Setting `Amount` to less than 0 will cause no damage.
        Damage<override>(Args:damage_args):void = external {}

        # Signaled when damage is applied to the `damageable` object.
        DamagedEvent<override>():listenable(damage_result) = external {}

        # Enable this object.
        Enable<override>():void = external {}

        # Disable this object.
        Disable<override>():void = external {}

        # Succeeds if the object is enabled, fails if it's disabled.
        IsEnabled<override>()<transacts><decides>:void = external {}

        # Fires when a vehicle enters the service station, returns the vehicle that entered.
        VehicleEnteredEvent<public>:listenable(fort_vehicle) = external {}

        # Fires when a vehicle leaves the service station, returns the vehicle that exited.
        VehicleExitedEvent<public>:listenable(fort_vehicle) = external {}

        # Fires on the first tick of a vehicle refueling, returns the refueled vehicle.
        VehicleFuelingBeginEvent<public>:listenable(fort_vehicle) = external {}

        # Fires when a vehicle is at full fuel, returns the refueled vehicle.
        VehicleFuelingEndEvent<public>:listenable(fort_vehicle) = external {}

        # Fires when a vehicle starts repairing, returns the repaired vehicle.
        VehicleRepairBeginEvent<public>:listenable(fort_vehicle) = external {}

        # Fires when a vehicle is at full health, returns the repaired vehicle.
        VehicleRepairEndEvent<public>:listenable(fort_vehicle) = external {}

        # Check if any vehicle is inside the service station.
        IsAnyVehicleInside<public>()<transacts><decides>:void = external {}

    # Used to set up islands so players will earn Battle Pass XP when they interact with your island. Accolades are achievements or accomplishments that players can complete to earn XP.
    accolades_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when testing the accolade to make sure it is awarded as expected. Only signals within unpublished island environments.
        # Sends the `agent` receiving the achievement.
        TestAwardEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Awards the XP to `agent`.
        Award<public>(Agent:agent):void = external {}

    # Used to track `agent` events used to generate analytics.
    analytics_device<public> := class<concrete><final>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Submits an event for `Agent`.
        Submit<public>(Agent:agent):void = external {}

    # Used to configure and play audio from the device location or from registered `agent`s.
    audio_player_device<public> := class<concrete><final>(creative_device_base):
        # Enables this device. Allows this device to be triggered from other linked devices (i.e. triggers) and allow calls to `Play` to succeed.
        Enable<public>():void = external {}

        # Disables this device. No longer allows this device to be triggered from other linked devices (i.e. triggers) and will stop any currently playing audio.
        Disable<public>():void = external {}

        # Starts playing audio from this device for `Agent`. This can only be used when the device is set to be *Heard by Instigator*.
        Play<public>(Agent:agent):void = external {}

        # Starts playing audio from this device.
        Play<public>():void = external {}

        # Stops any audio playing from this device for `Agent`. This can only be used when the device is set to be *Heard by Instigator*.
        Stop<public>(Agent:agent):void = external {}

        # Stops any audio playing from this device.
        Stop<public>():void = external {}

        # Adds `Agent` as a target to play audio from when activated.
        Register<public>(Agent:agent):void = external {}

        # Removes `Agent` as a target to play audio from when activated.
        Unregister<public>(Agent:agent):void = external {}

        # Removes all previously registered `agent`s as valid targets to play audio from when activated.
        UnregisterAll<public>():void = external {}

        # Shows this device in the world.
        Show<public>()<transacts>:void = external {}

        # Hides this device from the world.
        Hide<public>()<transacts>:void = external {}

    # Used to manage sound buses via control bus mixes set on the Audio Mixer Device.
    audio_mixer_device<public> := class<concrete><final>(creative_device_base):
        # Activates the mix set on the audio mixer.
        ActivateMix<public>():void = external {}

        # Deactivates the mix set on the audio mixer.
        DeactivateMix<public>():void = external {}

        # Adds `Agent` as a target when using the *CanBeHeardBy* Registered Players or NonRegisteredPlayers options.
        Register<public>(Agent:agent):void = external {}

        # Removes `Agent` as a target when using the *CanBeHeardBy* Registered Players or NonRegisteredPlayers options.
        Unregister<public>(Agent:agent):void = external {}

        # Removes all previously registered `agent`s when using the *CanBeHeardBy* Registered Players or NonRegisteredPlayers options.
        UnregisterAll<public>():void = external {}

    # Used to place a campfire in the world that an `agent` can use to heal themselves.
    campfire_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when an `agent` enters the area of effect for this device.
        # Sends the entering `agent`.
        AgentEntersEffectAreaEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` exits the area of effect for this device.
        # Sends the exiting `agent`.
        AgentExitsEffectAreaEvent<public>:listenable(agent) = external {}

        # Signaled when this device generates a pulse.
        CampfirePulseEvent<public>:listenable(tuple()) = external {}

        # Signaled when an `agent` is affected by a pulse generated by this device.
        # Sends the affected `agent`.
        AgentPulsedEvent<public>:listenable(agent) = external {}

        # Signaled when this device is lit by an `agent`.
        # Sends the lighting `agent`.
        LitEvent<public>:listenable(agent) = external {}

        # Signaled when this device is extinguished by an `agent`.
        # Sends the extinguishing `agent`.
        ExtinguishedEvent<public>:listenable(agent) = external {}

        # Signaled when this device is enabled.
        EnabledEvent<public>:listenable(tuple()) = external {}

        # Signaled when this device is disabled.
        DisabledEvent<public>:listenable(tuple()) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Adds wood to this device.
        AddWood<public>():void = external {}

        # Lights this device.
        Light<public>(Agent:agent):void = external {}

        # Extinguishes this device.
        Extinguish<public>(Agent:agent):void = external {}

    # Used to spawn fireflies that can be collected by `agent`s.
    firefly_spawner_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when a firefly is collected.
        # Sends the `agent` collected the firefly.
        OnFirefliesCollected<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Spawns new fireflies. The previous fireflies will be destroyed before a new fireflies spawn.
        (/Fortnite.com/Devices/firefly_spawner_device:)Respawn<public>():void = external {}

        # Resets respawn count.
        ResetRespawnCount<public>():void = external {}

    # Used to customize (or prevent) the 'down but not out' player state between 'healthy' and 'removed from game'.
    down_but_not_out_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when an `agent` is set to the `down but not out` player state.
        # Sends the `agent` that was downed.
        AgentDownedEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` in the `down but not out` player state is picked up.
        # Sends the `agent` that was picked up.
        AgentPickedUpEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` in the `down but not out` player state is dropped.
        # Sends the `agent` that was dropped.
        AgentDroppedEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` in the `down but not out` player state is thrown.
        # Sends the `agent` that was thrown.
        AgentThrownEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` in the `down but not out` player state is revived.
        # Sends the `agent` that was revived.
        AgentRevivedEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` is the aggressor of a shake down.
        # Sends the `agent` that is the aggressor.
        ShakeDownEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` is the victim of a shake down.
        # Sends the `agent` that is the victim.
        ShakenDownEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Sets the `Agent` to the `down but not out` player state.
        Down<public>(Agent:agent):void = external {}

        # Sets the `Agent` to the `healthy` player state if they are in the `down but not out` player state.
        Revive<public>(Agent:agent):void = external {}

    # Used to send custom messages to the elimination feed.
    elimination_feed_device<public> := class<concrete><final>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Activates this device. `Agent` is used as the instigator of the action.
        Activate<public>(Agent:agent):void = external {}

        # Activates this device.
        # Requires *Activated by Team / Class* be set to `All`.
        Activate<public>():void = external {}

    # Used to configure rules that can end the current round or game.
    end_game_device<public> := class<concrete><final>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Ends the round/game. Uses `Agent`'s team to determine if the round/game ends when *Activating Team* is enabled.
        Activate<public>(Agent:agent):void = external {}

    # Allows pickup items to be placed in the world..
    item_placer_device<public> := class<concrete><final>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

    # Used to listen for the player activating or releasing certain inputs.
    # The input is defined by the *Input* option.
    # Players can configure the key for the input in the Creative Input Actions section of the Keyboard Settings.
    input_trigger_device<public> := class<concrete><final>(creative_device_base):
        # Enables this device.
        # An Input Trigger will listen for inputs from players that meet the device requirements.
        Enable<public>():void = external {}

        # Disables this device.
        # A disabled Input Trigger will not listen for inputs 
        # and will never show on the HUD.
        Disable<public>():void = external {}

        # Signaled when the tracked input is pressed by an `agent`.
        # Sends the `agent` that pressed the input.
        PressedEvent<public>:listenable(agent) = external {}

        # Signaled when the tracked input is released by an `agent`.
        # Sends the `agent` that released the input.
        # Sends the `float` duration that the input was held.
        ReleasedEvent<public>:listenable(tuple(agent, float)) = external {}

        # Adds `Agent` to the registered player list.
        # *Registered Player Behavior* determines whether registered players meet the device requirements.
        Register<public>(Agent:agent):void = external {}

        # Removes `Agent` from the registered player list.
        # *Registered Player Behavior* determines whether registered players meet the device requirements.
        Unregister<public>(Agent:agent):void = external {}

        # Clears the list of registered players.
        # *Registered Player Behavior* determines whether registered players meet the device requirements.
        UnregisterAll<public>():void = external {}

        # Succeeds if `Agent` is currently holding the input.
        IsHeld<public>(Agent:agent)<transacts><decides>:void = external {}

    # Used to cause `agent`s to drop or lose items from their inventory. For example, if an `agent` is *Down But Not Out*, they could drop items from their inventory, and other `agent`s could then pick up those items.
    item_remover_device<public> := class<concrete><final>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Remove items from `Agent`s inventory. The items that are removed can be configured using *Affected Items*.
        Remove<public>(Agent:agent):void = external {}

    # Allows players to change their outfit in game!
    changing_booth_device<public> := class<concrete><final>(creative_device_base):

    # Used to track and react to how many players are in a particular area, and optionally display that information in game.
    player_counter_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when the player count matches *Target Player Count*. The frequency of evaluation against *Target Player Count* can be controlled through the device settings.
        CountSucceedsEvent<public>:listenable(tuple()) = external {}

        # Signaled when the player count does not match *Target Player Count*. The frequency of evaluation against *Target Player Count* can be controlled through the device settings.
        CountFailsEvent<public>:listenable(tuple()) = external {}

        # Signaled when a valid player enters the zone and is counted. The frequency of evaluation against the *Target Player Count* can be controlled through the device settings.
        # Sends the `agent` that is now being counted.
        CountedEvent<public>:listenable(agent) = external {}

        # Signaled when a player is no longer counted by this device, such as when they leave the zone, leave the game, or are assigned to a different `team` or class.
        # Sends the `agent` that is no longer being counted.
        RemovedEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Resets *Target Player Count* to the default value defined in the device settings. If *Target Player Count* was previously incremented or decremented, this reset immediately triggers a new comparison.
        Reset<public>():void = external {}

        # Increments *Target Player Count* by `1`. Immediately triggers a new comparison.
        IncrementTargetCount<public>():void = external {}

        # Decrements *Target Player Count* by `1`. Immediately triggers a new comparison.
        DecrementTargetCount<public>():void = external {}

        # Triggers an evaluation of the current count vs *Target Player Count*, signaling `CountSucceedsEvent` or `CountFailsEvent` based on the evaluation result.
        CompareToTarget<public>():void = external {}

        # Triggers `CountedEvent` for all `agent`s currently being counted.
        TransmitForAllCounted<public>():void = external {}

        # Adds the player to the registered player list.
        # *Track Registered Players* determines how registered players are counted.
        Register<public>(Agent:agent):void = external {}

        # Removes the player from the registered player list.
        # *Track Registered Players* determines how registered players are counted.
        Unregister<public>(Agent:agent):void = external {}

        # Clears all players from the list of registered players.
        # *Track Registered Players* determines how registered players are counted.
        UnregisterAll<public>():void = external {}

        # Show this device in the world as an info panel showing Current + Required player counts.
        ShowInfoPanel<public>():void = external {}

        # Hide this device info panel from the world.
        HideInfoPanel<public>():void = external {}

        # Returns whether this device is represented in the world as an info panel showing Current + Required player counts.
        IsShowingInfoPanel<public>()<transacts><decides>:void = external {}

        # Sets the number of players required for this counter to succeed. Immediately triggers a new comparison.
        SetTargetCount<public>(Count:int):void = external {}

        # Returns the number of players required for this counter to succeed.
        GetTargetCount<public>()<transacts>:int = external {}

        # Returns the number of players currently counted by this device
        GetCount<public>()<transacts>:int = external {}

        # Is true if the device is currently succeeding in its comparison.
        IsPassingTest<public>()<transacts><decides>:void = external {}

        # Is true if `Agent` is currently counted by this device.
        IsCounted<public>(Agent:agent)<transacts><decides>:void = external {}

        # Returns an array of agents that are currently counted by the device.
        GetCountedAgents<public>()<reads>:[]agent = external {}

    # Used to create a light which can have its color and brightness manipulated in response to in-game events.
    customizable_light_device<public> := class<concrete><final>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Resets the light to its initial state.
        Reset<public>():void = external {}

        # Turns on the light.
        TurnOn<public>():void = external {}

        # Turns off the light.
        TurnOff<public>():void = external {}

        # Toggles between `TurnOn` and `TurnOff`.
        Toggle<public>():void = external {}

        # Dims the light by *Dimming Amount*.
        DimLight<public>():void = external {}

        # Brightens the light by *Dimming Amount*.
        UndimLight<public>():void = external {}

    # Used to manipulate the properties of one or more props in a specified area (e.g. Visibility/Destructibility).
    prop_manipulator_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when props affected by this device are damaged.
        # Sends the `agent` that damaged the prop.
        DamagedEvent<public>:listenable(agent) = external {}

        # Signaled when props affected by this device are destroyed.
        # Sends the `agent` that destroyed the prop.
        DestroyedEvent<public>:listenable(agent) = external {}

        # Signaled when prop resource nodes affected by this device are harvested.
        # Sends the `agent` that harvested resources from the prop.
        HarvestingEvent<public>:listenable(agent) = external {}

        # Signaled when prop resource nodes affected by this device are completely depleted of energy.
        # Sends the `agent` that depleted the prop's energy.
        ResourceDepletionEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Shows all props affected by this device.
        ShowProps<public>():void = external {}

        # Hides all props affected by this device.
        HideProps<public>():void = external {}

        # Empties the resources of all props affected by this device.
        ExhaustResources<public>():void = external {}

        # Restocks the resources of all props affected by this device.
        RestockResources<public>():void = external {}

        # Restores health of all props affected by this device.
        RestoreHealth<public>():void = external {}

        # Sets the *Override Resource* option to *Yes*.
        SetResourceOverridesActive<public>():void = external {}

        # Sets the *Override Resource* option to *No*.
        DisableResourceNodeOverrides<public>():void = external {}

    # Used to mark an `agent`'s position on the minimap and configure the information shown for marked `agent`s.
    # 
    # Example configuration options:
    #  * Health and shield bars for marked players.
    #  * Distance to a marked player.
    # 
    # Example marker appearance options:
    #  * Customized text label displayed on marked players.
    #  * Alternative minimap icon and icon color.
    player_marker_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when the first item type monitored on marked agents has changed.
        # Sends the marked `agent`.
        FirstItemValueChangedEvent<public>:listenable(agent) = external {}

        # Signaled when the second item type monitored on marked agents has changed.
        # Sends the marked `agent`.
        SecondItemValueChangedEvent<public>:listenable(agent) = external {}

        # Signaled when a marked `agent` meets the quantity condition for the first monitored item type (e.g. Fewer Than, Equal To, More Than X).
        # Sends the marked `agent`.
        FirstItemValueReachedEvent<public>:listenable(agent) = external {}

        # Signaled when a marked `agent` meets the quantity condition for the second monitored item type (e.g. Fewer Than, Equal To, More Than X).
        # Sends the marked `agent`.
        SecondItemValueReachedEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Attaches a marker to `Agent`.
        Attach<public>(Agent:agent):void = external {}

        # Detaches a marker from `Agent`.
        Detach<public>(Agent:agent):void = external {}

        # Detaches markers from all marked `agent`s.
        DetachFromAll<public>():void = external {}

    # Generates an AI bot that spawns in a location and usually attacks players when they come in range.
    sentry_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when the sentry is alerted to an `agent`.
        # Sends the `agent` who alerted the sentry.
        AlertedEvent<public>:listenable(agent) = external {}

        # Signaled when the sentry exists the alert state.
        ExitsAlertEvent<public>:listenable(tuple()) = external {}

        # Signaled when the sentry enters the alert state.
        EntersAlertCooldownEvent<public>:listenable(tuple()) = external {}

        # Signaled when a sentry attacks an `agent`.
        # Sends the `agent` who is being attacked.
        AttackingEvent<public>:listenable(agent) = external {}

        # Signaled when a sentry is eliminated.
        # Sends the `agent` that eliminated the sentry. If the sentry was eliminated by a non-agent then `false` is returned.
        EliminatedEvent<public>:listenable(?agent) = external {}

        # Signaled when the sentry eliminates a creature.
        EliminatingACreatureEvent<public>:listenable(tuple()) = external {}

        # Signaled when a sentry eliminates an `agent`.
        # Sends the `agent` who was eliminated by the sentry.
        EliminatingAgentEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Spawns the sentry.
        Spawn<public>():void = external {}

        # Destroys the current sentry.
        DestroySentry<public>():void = external {}

        # Sets the sentry to the same team `Agent` is on.
        JoinTeam<public>(Agent:agent):void = external {}

        # Resets the sentry to the original team designated in the device options.
        ResetTeam<public>():void = external {}

        # Puts the sentry into the pacify state, preventing from entering the alert (attacking) state.
        Pacify<public>():void = external {}

        # Puts the sentry into the alert state.
        EnableAlert<public>():void = external {}

        # Sets the sentry to target `Agent`. The sentry will not target agents on the same team as the sentry.
        Target<public>(Agent:agent):void = external {}

        # Resets the alert state.
        ResetAlertCooldown<public>():void = external {}

    # Used to move around a building or prop, and customize responses to various collision event types.
    prop_mover_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when this device is enabled.
        EnabledEvent<public>:listenable(tuple()) = external {}

        # Signaled when this device is disabled.
        DisabledEvent<public>:listenable(tuple()) = external {}

        # Signaled when the prop movement begins.
        BeganEvent<public>:listenable(tuple()) = external {}

        # Signaled when the prop movement ends.
        EndedEvent<public>:listenable(tuple()) = external {}

        # Signaled when the prop reaches its destination.
        FinishedEvent<public>:listenable(tuple()) = external {}

        # Signaled when the prop changes its direction.
        MovementModeChangedEvent<public>:listenable(tuple()) = external {}

        # Signaled when the prop hits an `agent`.
        # Sends the `agent` hit by the prop.
        AgentHitEvent<public>:listenable(agent) = external {}

        # Signaled when the prop hits a creature, animal, or NPC.
        AIHitEvent<public>:listenable(tuple()) = external {}

        # Signaled when the prop hits another prop.
        PropHitEvent<public>:listenable(tuple()) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Moves the prop to its original position.
        Reset<public>():void = external {}

        # Begins the prop moving.
        Begin<public>():void = external {}

        # Ends the prop moving.
        End<public>():void = external {}

        # Moves the prop forward based on this device's default configuration, ignoring the prop's previous movement.
        Advance<public>():void = external {}

        # Reverses the prop's moving direction.
        Reverse<public>():void = external {}

        # Sets the total distance (in meters) that the prop will move.
        SetTargetDistance<public>(InDistance:float):void = external {}

        # Returns the total distance (in meters) that the prop will move.
        GetTargetDistance<public>()<transacts>:float = external {}

        # Sets the speed (in meters per second) at which the prop will move to its destination.
        SetTargetSpeed<public>(Speed:float):void = external {}

        # Returns the speed (in meters per second) at which the prop mover will move the prop to its destination.
        GetTargetSpeed<public>()<transacts>:float = external {}

    # Used to trigger in game events based on real world time.
    real_time_clock_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when the optional second *Duration* time has been reached.
        DurationElapsedEvent<public>:listenable(tuple()) = external {}

        # Signaled when the target time is reached.
        TimeReachedEvent<public>:listenable(tuple()) = external {}

        # Signaled when this device is enabled after the target time has been reached.
        EnablingAfterTimeReachedEvent<public>:listenable(tuple()) = external {}

        # Signaled when this device is enabled before the target time has been reached.
        EnablingBeforeTimeReachedEvent<public>:listenable(tuple()) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

    @deprecated
    # Controls how the sky looks, as well as giving you options for changing the sun, clouds, stars or other objects in the sky above your island. You can control the sun and moon, and add other atmospheric elements like stars, fog and clouds. You can change the color of your light source, and blend different colors for your island's sky to create the perfect atmosphere for your game.
    skydome_device<public> := class<concrete><final>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

    # Used in tandem with `race_manager_device` to define the route players must traverse.
    race_checkpoint_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when this checkpoint becomes the next checkpoint that `agent`s need to pass for the first time.
        # Sends the first `agent` who is now targeting this checkpoint.
        CheckpointBecomesCurrentForTheFirstTimeEvent<public>:listenable(agent) = external {}

        # Signaled when this checkpoint becomes the current checkpoint for `agent`.
        # Sends the `agent` who is now targeting this checkpoint.
        CheckpointBecomesCurrentEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` passes this checkpoint.
        # Sends the `agent` that passed this checkpoint.
        CheckpointCompletedEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Sets this checkpoint as the current checkpoint for `Agent`. This only functions if `Agent` has not already passed this checkpoint.
        SetAsCurrentCheckpoint<public>(Agent:agent):void = external {}

    # Used with the `race_checkpoint_device` to create more advanced racing modes.
    race_manager_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when the race begins.
        # Sends the `agent` that started the race.
        RaceBeganEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` finishes the race.
        # Sends the `agent` that finished the race.
        RaceCompletedEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` completes their first lap.
        # Sends the `agent` that finished the lap.
        FirstLapCompletedEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` completes a lap.
        # Sends the `agent` that finished the lap.
        LapCompletedEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Begins the race.
        Begin<public>():void = external {}

        # Ends the race.
        End<public>():void = external {}

    # Used to spawn and configure an aerial supply drop that can provide players with customized weapons/supplies.
    supply_drop_spawner_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when the balloon on the supply crate is popped.
        # Sends the `?agent` that popped the balloon. If no `agent` popped the balloon returns `false`.
        BalloonPoppedEvent<public>:listenable(?agent) = external {}

        # Signaled when the supply crate is opened.
        # Sends the `agent` that opened the crate.
        OpenedEvent<public>:listenable(agent) = external {}

        # Signaled when the supply crate lands for the first time.
        LandingEvent<public>:listenable(tuple()) = external {}

        # Spawns a supply drop provided one hasn't already spawned. *Owning Team* is set to `Agent`'s team.
        Spawn<public>(Agent:agent):void = external {}

        # Spawns a supply drop provided one hasn't already spawned.
        Spawn<public>():void = external {}

        # Destroys the balloon and causes the supply crate to freefall.
        DestroyBalloon<public>():void = external {}

        # Opens the supply crate, ignoring the locked or unlocked state. `Agent` acts as the instigator of the open action.
        Open<public>(Agent:agent):void = external {}

        # Locks the supply crate so `agent`s cannot open it.
        Lock<public>():void = external {}

        # Unlocks the supply crate so `agent`s can open it.
        Unlock<public>():void = external {}

    # Used to allow agents to turn other linked devices on/off or other custom state changes.
    switch_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when the switch is turned on by the specified `agent`.
        # Sends the `agent` that turned on the device.
        TurnedOnEvent<public>:listenable(agent) = external {}

        # Signaled when the switch is turned off by the specified `agent`.
        # Sends the `agent` that turned off the device.
        TurnedOffEvent<public>:listenable(agent) = external {}

        # Signaled if the switch is on when the state is checked.
        IfOnWhenCheckedEvent<public>:listenable(tuple()) = external {}

        # Signaled if the switch is off when the state is checked.
        IfOffWhenCheckedEvent<public>:listenable(tuple()) = external {}

        # Signaled when the switch state is saved.
        StateSaveEvent<public>:listenable(tuple()) = external {}

        # Signaled when the switch state changes.
        StateChangesEvent<public>:listenable(tuple()) = external {}

        # Signaled when the switch state is loaded by the specified `agent`.
        # Sends the `agent` that loaded the state on the device.
        StateLoadEvent<public>:listenable(agent) = external {}

        # Signaled when the persistent data is cleared by the specified `agent`.
        # Sends the `agent` that cleared persistent data on the device.
        ClearEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Turns on this device with `Agent` acting as the instigator of the action.
        TurnOn<public>(Agent:agent):void = external {}

        # Turns off the device with `Agent` acting as the instigator of the action.
        TurnOff<public>(Agent:agent):void = external {}

        # Toggles between `TurnOn` and `TurnOff` with `Agent` acting as the instigator of the action.
        ToggleState<public>(Agent:agent):void = external {}

        # Loads the device state with `Agent` acting as the instigator of the action.
        LoadState<public>(Agent:agent):void = external {}

        # Saves the device state with `Agent` acting as the instigator of the action.
        SaveState<public>(Agent:agent):void = external {}

        # Loads the device state for all players.
        LoadStateForAll<public>():void = external {}

        # Saves the device state for all players
        SaveStateForAll<public>():void = external {}

        # Checks the device state with `Agent` acting as the instigator of the action.
        CheckState<public>(Agent:agent):void = external {}

        # Clears persistence data for `Agent`.
        ClearPersistenceData<public>(Agent:agent):void = external {}

        # Clears persistence data for all `agent`s.
        ClearAllPersistenceData<public>():void = external {}

        # Returns the current state of this switch: true (on) or false (off). Use this overload  of `GetCurrentState` when this device has *Store State Per Player* set to *Yes*.
        GetCurrentState<public>(Agent:agent)<transacts><decides>:void = external {}

        # Returns the current state of this switch: true (on) or false (off). Use this overload  of `GetCurrentState` when this device has *Store State Per Player* set to *No*.
        GetCurrentState<public>()<transacts><decides>:void = external {}

        # Query whether this device has a single global on/off state, or has a personalized on/off state for each individual agent.
        IsStatePerAgent<public>()<transacts><decides>:void = external {}

        # Sets the *Interaction Time* required to activate this device (in seconds).
        SetInteractionTime<public>(Time:float):void = external {}

        # Returns the *Interaction Time* required to activate this device (in seconds).
        GetInteractionTime<public>()<transacts>:float = external {}

        # Sets the *Turn On Text* to be displayed to a user when the switch is currently off, and offers an interaction to switch it on. Clamped to 150 characters.
        SetTurnOnInteractionText<public>(Text:message):void = external {}

        # Sets the *Turn Off Text* to be displayed to a user when the switch is currently on, and offers an interaction to switch it off. Clamped to 150 characters.
        SetTurnOffInteractionText<public>(Text:message):void = external {}

        # Sets the state of the switch to a specific value for a specific `Agent`.
        #  Use when the device has *Store State Per Player* set to *Yes*.
        SetState<public>(Agent:agent, State:logic):void = external {}

        # Sets the state of the switch to a specific value.
        #  Use when the device has *Store State Per Player* set to *No*.
        SetState<public>(State:logic):void = external {}

        # Updates the *State Reset Time* for the device, in seconds, clamped to the Min and Max defined in the device.
        #  This will not apply to any state reset timers currently in effect.
        #  Set to 0.0 to disable the State Reset Time. Set to less than 0.0 to reset to default.
        SetStateResetTime<public>(Time:float):void = external {}

        # Updates the *State Reset Time* for the device, in seconds,for a specific player (if *Store State Per Player* is *Yes*), clamped to the Min and Max defined in the device.
        #  This will not apply to any state reset timers currently in effect.
        #  Set to 0.0 to disable the State Reset Time. Set to less than 0.0 to reset to default.
        SetStateResetTime<public>(Agent:agent, Time:float):void = external {}

        # Returns the value of *State Reset Time*, in seconds, for the device. Returns -1.0 if *State Reset Time* is not used.
        GetStateResetTime<public>()<transacts>:float = external {}

        # Returns the value of *State Reset Time*, in seconds, for the device, for a specific player. Returns -1.0 if *State Reset Time* is not used.
        GetStateResetTime<public>(Agent:agent)<transacts>:float = external {}

        # Returns the time, in seconds, before the switch will reset itself to default.
        #  Returns -1.0 if *Store State Per Player* is *Yes* or if  there is no active reset timer.
        GetCurrentResetTime<public>():float = external {}

        # Returns the time, in seconds, before the switch will reset itself to default for *Agent*.
        #  Returns -1.0 if there is no active reset timer.
        GetCurrentResetTime<public>(Agent:agent):float = external {}

    # Used to create and customize your own visual effects. This is more flexible than the `vfx_spawner_device`, which gives you a selection of pre-made visual effects to choose from but limits how much you can customize or change those effects.
    vfx_creator_device<public> := class<concrete><final>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Toggles between `Enable` and `Disable`.
        ToggleEnabled<public>():void = external {}

        # Starts playing the effect.
        Begin<public>():void = external {}

        # Starts the effect at `Agent`'s location. This option is only valid if *Stick to Player* is enabled.
        Begin<public>(Agent:agent):void = external {}

        # Starts the effect at every `agent`'s location. This option is only valid if *Stick to Player* is enabled.
        BeginForAll<public>():void = external {}

        # Ends playing the effect.
        End<public>():void = external {}

        # Ends the effect at `Agent`'s location. This option is only valid if *Stick to Player* is enabled.
        End<public>(Agent:agent):void = external {}

        # Ends the effect at every `agent`'s locations. This option is only valid if *Stick to Player* is enabled.
        EndForAll<public>():void = external {}

        # Toggles between `Begin` and `End`.
        Toggle<public>():void = external {}

        # Toggles between `Begin` and `End`.
        Toggle<public>(Agent:agent):void = external {}

        # Toggles between `BeginForAll` and `EndForAll`.
        ToggleForAll<public>():void = external {}

        # Pauses the effect if the effect is running. If the effect is paused, unpauses the effect. Pausing an effect causes the effect to freeze in place.
        TogglePause<public>():void = external {}

        # Pauses the effect at `Agent`'s locations if it is playing, or resumes the effect if it is paused.
        # When paused the effect freezes in place.
        TogglePause<public>(Agent:agent):void = external {}

        # Pauses the effect at every `agent`'s locations if it is playing, or resumes the effect if it is paused.
        # When paused the effect freezes in place.
        TogglePauseForAll<public>():void = external {}

        # Removes the effect from `Agent` and continues playing at the device location. This option is only valid if *Stick to Player* is enabled.
        Remove<public>(Agent:agent):void = external {}

        # Removes the effect for every `agent` and continues playing at the device location. This option is only valid if *Stick to Player* is enabled.
        RemoveFromAll<public>():void = external {}

        # Spawns the effect at `Agent`'s location. This option is only valid if *Stick to Player* is enabled.
        SpawnAt<public>(Agent:agent):void = external {}

    # Used to create and manipulate volumes of water where players can swim, fish, or drive boats.
    water_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when an `agent` enters the water.
        # Sends `agent` that entered the water.
        AgentEntersWaterEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` exits the water.
        # Sends `agent` that exited the water.
        AgentExitsWaterEvent<public>:listenable(agent) = external {}

        # Signals when the volume is filled to the water level set in the *Default Vertical Water Percentage* option.
        VerticalFillingCompletedEvent<public>:listenable(tuple()) = external {}

        # Signals when the water volume is completely empty.
        VerticalEmptyingCompletedEvent<public>:listenable(tuple()) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Resets the water level in the volume to the value set in the *Default Vertical Water Percentage* option.
        Reset<public>():void = external {}

        # Starts vertically emptying the water in the volume.
        BeginVerticalEmptying<public>():void = external {}

        # Starts vertically filling the water in the volume.
        BeginVerticalFilling<public>():void = external {}

        # Stops filling or emptying the volume.
        StopVerticalMovement<public>():void = external {}

        # Resumes either filling or emptying the volume.
        ResumeVerticalMovement<public>():void = external {}

    # Used to spawn an `agent` on an island. Use multiple `player_spawner_device`s to spawn multiple `agent`s.
    player_spawner_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when an `agent` is spawned from this device.
        # Sends the `agent` that spawned.
        SpawnedEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

    # Used to set an `agent`'s spawn point when activated. This can also clear the `agent`'s inventory.
    player_checkpoint_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when this device is first activated by any `agent`.
        # Sends the `agent` that activated this device.
        FirstActivationEvent<public>:listenable(agent) = external {}

        # Signaled each time a new `agent` activates this device.
        # Sends the `agent` that activated this device.
        FirstActivationPerAgentEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Registers this checkpoint for `Agent`. This sets the respawn point and can clear `Agent`'s inventory depending on this device's settings. Multiple `agent`s can be registered to this device at one time.
        Register<public>(Agent:agent):void = external {}

        # Returns an array of agents that are currently registered to this checkpoint.
        GetRegisteredAgents<public>()<reads>:[]agent = external {}

        # Succeeds when `Agent` is registered to this checkpoint.
        IsRegistered<public>(Agent:agent)<transacts><decides>:void = external {}

    # Used to control the behavior of the map & minimap.
    # Activation for a given `agent` can occur automatically via the device's *Activate Automatically* user option, by the `agent` entering and exiting the device's volume if using the *Activate on Trigger* user option, or via events from other devices or verse.
    # When more than one map controller is activated for a given `agent`, the one with the highest *Map Priority* user option applies.
    map_controller_device<public> := class<concrete><final>(creative_device_base):
        # Adds the map controller to the provided `Agent`'s map controller stack.
        # If multiple map controllers are active for an `agent`, the one with the highest *Map Priority* is used.
        Activate<public>(Agent:agent):void = external {}

        # Adds the map controller to all `agent`s in the experience.
        # If multiple map controllers are active for an `agent`, the one with the highest *Map Priority* is used.
        Activate<public>():void = external {}

        # Removes the map controller from the provided `Agent`'s map controller stack.
        # The next highest priority active map controller will be used, or if none exists, the default behavior will be restored.
        Deactivate<public>(Agent:agent):void = external {}

        # Removes the map controller from all `agent`s in the experience.
        # The next highest priority active map controller will be used, or if none exists, the default behavior will be restored.
        Deactivate<public>():void = external {}

        # Enables the device.
        # Enabling the device will allow it to be activated, both by incoming events, and by trigger if using *Activate on Trigger*.
        Enable<public>():void = external {}

        # Disables the device.
        # Disabling the device will deactivate it for all `agents` in the experience, turn off the trigger functionality, and prevent it from responding to events.
        Disable<public>():void = external {}

        # Sets the *Capture Box Size* (in meters).
        # *Capture Box Size* refers to the length and width of the area used for both the map capture image as well as the activation trigger.
        # Value is clamped between `25.0` and `2500.0` meters. 
        SetCaptureBoxSize<public>(Size:float):void = external {}

        # Returns the *Capture Box Size* (in meters).
        GetCaptureBoxSize<public>()<transacts>:float = external {}

    # Used to create a minigame which expects specific input timing from the player.
    # Visuals may vary, but always feature a scrubber that moves from 0.0 to 1.0.
    # Good and perfect zones are defined on the device, describing at what point the player must provide input to succeed.
    skilled_interaction_device<public> := class<concrete><final>(creative_device_base, enableable):
        # Begins the interaction for the provided `agent`.
        # Will cancel any other interactions already in progress for the `agent`.
        BeginInteraction<public>(Agent:agent)<transacts><decides>:void = external {}

        # Cancels the interaction, if in progress, for the provided `agent`.
        EndInteraction<public>(Agent:agent)<transacts><decides>:void = external {}

        # Signaled when the `agent` provides a perfect input.
        # Perfect input occurs when input is provided while the scrubber is within the device's *Perfect Zone*.
        # Sends the `agent` that provided the input.
        # Sends the `float` position of the meter scrubber from `0.0` to `1.0`.
        PerfectInputTriggeredEvent<public>:listenable(tuple(agent, float)) = external {}

        # Signaled when the `agent` provides a good input.
        # Good input occurs when input is provided while the scrubber is within the *Good Zone* (excluding the *Perfect Zone*).
        # Sends the `agent` that provided the input.
        # Sends the `float` position of the meter scrubber from `0.0` to `1.0`.
        GoodInputTriggeredEvent<public>:listenable(tuple(agent, float)) = external {}

        # Signaled when the `agent` provides a bad input.
        # Bad input occurs when input is provided while the scrubber is outside the *Good Zone*.
        # Sends the `agent` that provided the input.
        # Sends the `float` position of the meter scrubber from `0.0` to `1.0`.
        BadInputTriggeredEvent<public>:listenable(tuple(agent, float)) = external {}

        # Signaled when the `agent` starts an interaction.
        # Input is the `agent` which started interacting with the device.
        InteractionStartedEvent<public>:listenable(agent) = external {}

        # Signaled when the `agent` succeeds at the interaction.
        # The interaction is completed depending on the device's configuration.
        # * Once the device's *Success Target* is reached.
        # * Upon a perfect input if using *Instant Success Perfect Behavior*.
        InteractionSucceededEvent<public>:listenable(agent) = external {}

        # Signaled when the `agent` fails the interaction.
        # The interaction can be failed in several ways
        # * If the number of bad inputs reaches the *Failure Limit*.
        # * If the interaction has an *Interaction Time Limit* that expires.
        InteractionFailedEvent<public>:listenable(agent) = external {}

        # Signaled when the interaction is interrupted.
        # The interaction can be interrupted in several ways.
        # * If the participating `agent` is eliminated or enters a downed state.
        # * If *Deactivate* is called while the interaction is in progress.* If *Disable* is called while the interaction is in progress.
        InteractionCanceledEvent<public>:listenable(agent) = external {}

        # Enable this device.
        Enable<override>():void = external {}

        # Disable this device.
        Disable<override>():void = external {}

        # Succeeds if the object is enabled, fails if it's disabled.
        IsEnabled<override>()<transacts><decides>:void = external {}

    # Used to show or hide parts of the HUD for players or teams. Use this with other devices such as the `hud_message_device`, `map_indicator_device`, and `billboard_device` to control exactly how much information players can see during a game, as well as how and when they see that information.
    hud_controller_device<public> := class<concrete><final>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Sets the *Affected Class* option to `Agent`'s class.
        UpdateAffectedClass<public>(Agent:agent):void = external {}

        # Sets the *Affected Team* option to `Agent`'s team.
        UpdateAffectedTeam<public>(Agent:agent):void = external {}

        # Resets the *Affected Class* option to its starting value.
        ResetAffectedClass<public>():void = external {}

        # Resets the *Affected Team* option to its starting value.
        ResetAffectedTeam<public>():void = external {}

    # Used to relay `agent` statistics to other devices and `agent`s. Can transmit statistics such as elimination count, eliminated count, or scores when certain conditions are met. Can also project a hologram of the `agent` and display text that can be altered in various positions and curvatures.
    player_reference_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when this device is activated.
        # Sends the `agent` stored in the device.
        ActivatedEvent<public>:listenable(agent) = external {}

        # Signaled when a stat tracked by this device is updated.
        # Sends the `agent` stored in the device.
        TrackedStatChangedEvent<public>:listenable(agent) = external {}

        # Signaled when the `agent` tracked by this device is updated.
        # Sends the new `agent` stored in the device.
        AgentUpdatedEvent<public>:listenable(agent) = external {}

        # Signaled when the `agent` tracked by this fails to be updated.
        # Sends the `agent` that attempted to be stored in this device.
        AgentUpdateFailsEvent<public>:listenable(agent) = external {}

        # Signaled when the `agent` tracked by this device is replaced.
        # Sends the new `agent` stored in the device.
        AgentReplacedEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Ends the round/game.
        Activate<public>():void = external {}

        # Registers `Agent` as the `agent` being tracked by this device.
        Register<public>(Agent:agent):void = external {}

        # Clears the state of this device.
        Clear<public>():void = external {}

        # Is true when `Agent` is the player being referenced by the device.
        IsReferenced<public>(Agent:agent)<transacts><decides>:void = external {}

        # Returns the `agent` currently referenced by the device.
        GetAgent<public>():?agent = external {}

        # Returns the stat value that this device is currently tracking
        GetStatValue<public>():int = external {}

    # Used to create a bouncer that can launch players, vehicles, and more into the air with optional effects.
    bouncer_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when the condition in the *On Bounced Trigger* option is met and someone or something is launched.
        #  * Sends the `agent` that bounced. If a vehicle bounced, sends the driver. If a projectile bounced, sends its instigator.
        #  * Sends `false` if something else bounced, including a vehicle with no driver
        BouncedEvent<public>:listenable(?agent) = external {}

        # Signaled when the heal effect starts for an `agent`.
        HealStartEvent<public>:listenable(agent) = external {}

        # Signaled when the heal effect stops for an `agent`.
        HealStopEvent<public>:listenable(agent) = external {}

        # Enables bouncing on this device, as well as any visual and audio effects.
        Enable<public>():void = external {}

        # Disables bouncing on this device, as well as any visual and audio effects.
        Disable<public>():void = external {}

    # Used to trigger a custom response to a *Primary* or *Secondary* signal, sent by a *Signal Remote* item.
    signal_remote_manager_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when a player has triggered the *Primary* signal using a *Signal Remote* item.
        # Sends the `agent` that triggered the signal.
        PrimarySignalEvent<public>:listenable(agent) = external {}

        # Signaled when a player has triggered the *Secondary* signal using a *Signal Remote* item.
        # Sends the `agent` that triggered the signal.
        SecondarySignalEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

    # Used to place a crash pad that can bounce players and protect them from fall damage.
    crash_pad_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when an `agent` is launched by this device.
        # Sends the launched `agent`.
        LaunchedEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

    # Spawns a crowd to cheer you on.
    crowd_volume_device<public> := class<concrete><final>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

    # Used to add fishing mechanics to experiences, such as:
    #  * Fishing competitions between players.
    #  * Collecting fish as a resource.
    #  * Fishing minigames with their own rewards.
    fishing_zone_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when an `agent` catches a fish.
        # Sends the `agent` that caught the fish.
        CaughtEvent<public>:listenable(agent) = external {}

        # Signaled when all items have been caught and removed.
        # Sends the `agent` that caught the last fish.
        EmptyEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Resets the number of available uses for this device back to *Uses Allowed*.
        Reset<public>():void = external {}

        # Returns all caught and removed items to the inventory. This only works when *Pool Type* is set to *Device Inventory*.
        Restock<public>():void = external {}

    # Used to place visual effects around your island. You can use these effects to enhance the overall theme and experience of your game.
    vfx_spawner_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when the effect is enabled.
        EffectEnabledEvent<public>:listenable(tuple()) = external {}

        # Signaled when the effect is disabled.
        EffectDisabledEvent<public>:listenable(tuple()) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Restarts the VFX. Triggers the VFX if it's Burst VFX.
        Restart<public>():void = external {}

    # Used to display curated videos onto in-game screens or player HUDs.
    video_player_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when this device becomes the controlling streaming device for the `agent`.
        StreamStartedEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Enables collision checks on this device.
        EnableCollision<public>():void = external {}

        # Disables collision checks on this device.
        DisableCollision<public>():void = external {}

        # Transitions to fullscreen for `Agent`.
        EnterFullScreen<public>(Agent:agent):void = external {}

        # Transitions to fullscreen for `Agent`.
        ExitFullScreen<public>(Agent:agent):void = external {}

        # Hides the picture-in-picture video from `Agent`.
        HidePIP<public>(Agent:agent):void = external {}

        # Transitions the picture-in-picture video to the default size for `Agent`.
        MakePIPDefaultSize<public>(Agent:agent):void = external {}

        # Transitions the picture-in-picture video to full screen for `Agent`.
        MakePIPFullScreen<public>(Agent:agent):void = external {}

        # Turns off all streaming devices of this type on the island.
        EndForAll<public>():void = external {}

        # Restart the stream from the beginning.
        Restart<public>():void = external {}

        # Seeks to the *Triggered Seek Time*. Caution: The stream will pause while the video buffers when seeking.
        Seek<public>():void = external {}

        # Stops the currently playing stream and starts the custom stream with the audio only playing from this device. *Stream Priority* will not work until control is released.
        TakeControl<public>():void = external {}

        # If any streaming device has forced control of the stream, this will release it and play the highest priority stream in line.
        ReleaseControl<public>():void = external {}

    # Used to track when agents enter and exit a volume.
    volume_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when an `agent` enters the device volume.
        AgentEntersEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` exits the device volume.
        AgentExitsEvent<public>:listenable(agent) = external {}

        @experimental
        # Signaled when an `creative_prop` entered the device volume.
        PropEnterEvent<public>:listenable(creative_prop) = external {}

        @experimental
        # Signaled when an `creative_prop` exited the device volume.
        PropExitEvent<public>:listenable(creative_prop) = external {}

        # Returns an array of agents that are currently occupying the volume.
        GetAgentsInVolume<public>()<reads>:[]agent = external {}

        # Succeeds when `Agent` is in the volume.
        IsInVolume<public>(Agent:agent)<transacts><decides>:void = external {}

    # Creates a chair where `Agent`s can sit.
    chair_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when an `agent` sits on the Chair.
        # Sends the sitting `agent`.
        SeatedEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` stops sitting on the Chair.
        # Sends the standing `Agent`.
        ExitedEvent<public>:listenable(agent) = external {}

        # Enables the Chair.
        # An enabled Chair can be interacted with and occupied
        # by any `agent` that meets the requirements.
        Enable<public>():void = external {}

        # Disables the Chair.
        # A disabled Chair cannot be interacted with
        # and any `agent` currently occupying the Chair will be ejected.
        Disable<public>():void = external {}

        # Allows any seated `agent` to leave the chair manually.
        EnableExit<public>():void = external {}

        # Prevents any seated `agent` from leaving the Chair manually.
        # While Exit is disabled, call Eject to force them out.
        DisableExit<public>():void = external {}

        # Makes `Agent` sit on this chair if they meet the requirements.
        Seat<public>(Agent:agent):void = external {}

        # Ejects any `agent` currently in the chair.
        Eject<public>():void = external {}

        # Makes `Agent` exit this chair if they are currently in the chair.
        Eject<public>(Agent:agent):void = external {}

        # Succeeds if `Agent` is currently in the chair .
        IsSeated<public>(Agent:agent)<transacts><decides>:void = external {}

        # Succeeds if the chair is currently occupied.
        IsOccupied<public>()<transacts><decides>:void = external {}

        # Returns the `agent` currently occupying the chair.
        GetSeatedAgent<public>():?agent = external {}

    # Used to project a hologram of a character performing dance emotes.
    dance_mannequin_device<public> := class<concrete><final>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Activates the hologram using *Default Preset* options.
        ActivateDefaultPreset<public>():void = external {}

        # Activates the hologram using *Preset 2* options.
        ActivatePreset2<public>():void = external {}

        # Activates the hologram using *Preset 3* options.
        ActivatePreset3<public>():void = external {}

        # Activates the hologram using `Agent`'s skin and emotes.
        ActivateSkinAndEmoteCapture<public>(Agent:agent):void = external {}

        # Deactivates the hologram.
        DeactivateSkinAndEmoteCapture<public>():void = external {}

    # Used to create patrolling behavior for guards spawned with the `guard_spawner_device`.
    ai_patrol_path_device<public> := class<concrete><final>(creative_device_base):
        # Assign an AI to this patrol path.
        Assign<public>(Patroller:agent):void = external {}

        # Signaled when a guard reaches this device.
        NodeReachedEvent<public>:listenable(agent) = external {}

        # Signaled when a guard cannot reach the next `ai_patrol_path_device`.
        NextNodeUnreachableEvent<public>:listenable(agent) = external {}

        # Signaled when a guard starts moving on the patrol path.
        PatrolPathStartedEvent<public>:listenable(agent) = external {}

        # Signaled when a guard stops moving on the patrol path.
        PatrolPathStoppedEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Commands patroller to follow the *Next Patrol Path Group* instead of the default *Patrol Path Group*.
        GoToNextPatrolGroup<public>(Patroller:agent):void = external {}

    # Used to customize one creature type at a time. Place multiple `creature_manager_device`s for each type of creature on your island.
    creature_manager_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when a creature of the selected *Creature Type* is eliminated.
        # Sends the `agent` that eliminated the creature.
        MatchingCreatureTypeEliminatedEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

    # Used to spawn a creature at a specified location.
    creature_placer_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when a creature is spawned.
        # Sends the `agent` creature who was spawned.
        SpawnedEvent<public>:listenable(agent) = external {}

        # Signaled when the creature is eliminated.
        #  * Sends the `agent` that eliminated the creature.
        #  * Sends `false` if the creature was eliminated by something other than an `agent` (e.g. a vehicle).
        EliminatedEvent<public>:listenable(?agent) = external {}

        # Spawns the creature.
        Spawn<public>():void = external {}

        # Despawns the creature.
        Despawn<public>():void = external {}

    # Used to spawn one or more waves of creatures of customizable types at selected time intervals.
    creature_spawner_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when a creature is spawned.
        # Sends the `agent` creature who was spawned.
        SpawnedEvent<public>:listenable(agent) = external {}

        # Signaled when a creature is eliminated.
        #  `Source` is the `agent` that has eliminated the creature. If the creature was eliminated by a non-agent then `Source` is 'false'.
        #  `Target` is the creature that was eliminated.
        EliminatedEvent<public>:listenable(device_ai_interaction_result) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Destroys this device.
        DestroySpawner<public>():void = external {}

        # Eliminates all creatures spawned by this device.
        EliminateCreatures<public>():void = external {}

        # Returns the spawn limit of the device.
        GetSpawnLimit<public>()<transacts>:int = external {}

    # Creates an impenetrable zone that can block `agent` movement and weapon fire.
    barrier_device<public> := class<concrete><final>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Adds the specified `agent` to a list of additional `agent`s that the Barrier should ignore. This list is in addition to the Ignore Team and Ignore Class options. Note: Has no effect on bullets.
        AddToIgnoreList<public>(Agent:agent):void = external {}

        # Removes the specified `agent` from the ignore list. The `agent` will still be ignored if they are on an ignored team or of an ignored class.
        RemoveFromIgnoreList<public>(Agent:agent):void = external {}

        # Removes all `agent`s from the ignore list. `Agent`s will still be ignored if they are on an ignored team or of an ignored class.
        RemoveAllFromIgnoreList<public>():void = external {}

    # Used to specify an area volume which can damage `agent`s, vehicles, and creatures.
    damage_volume_device<public> := class<concrete><final>(effect_volume_device):
        # Signaled when an `agent` enters the volume.
        # Sends the `agent` entering the volume.
        AgentEntersEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` exits the volume.
        # Sends the `agent` exiting the volume.
        AgentExitsEvent<public>:listenable(agent) = external {}

        # Updates *Selected Class* to `Agent`'s class.
        UpdateSelectedClass<public>(Agent:agent):void = external {}

        # Updates *Selected Team* to `Agent`'s team.
        UpdateSelectedTeam<public>(Agent:agent):void = external {}

        # Sets the damage to be applied each tick within the volume. `Damage` is clamped between `1` and `500`.
        SetDamage<public>(Damage:int):void = external {}

        # Returns the damage to be applied each tick within the volume.
        GetDamage<public>()<transacts>:int = external {}

        # Is true when `Agent` is in the zone.
        IsInVolume<public>(Agent:agent)<transacts><decides>:void = external {}

        # Returns an array of agents that are currently occupying the volume.
        GetAgentsInVolume<public>()<reads>:[]agent = external {}

    # Base class for types of volumes with special gameplay properties.
    effect_volume_device<public> := class<abstract><epic_internal>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

    # Used to specify an area which allows (or prevents) various objects, terrain, or buildings from being set on fire.
    fire_volume_device<public> := class<concrete><final>(effect_volume_device):
        # Extinguishes objects inside this device area.
        Extinguish<public>():void = external {}

        # Ignites objects inside this device area.
        Ignite<public>():void = external {}

    # Used to specify a zone where custom gameplay effects can be applied (e.g. gravity, no build, no weapons).
    mutator_zone_device<public> := class<concrete><final>(effect_volume_device):
        # Signaled when an `agent` in this zone begins emoting. This will not signal if the `agent` is on the *Safe Team* or if *Affects Players* is disabled.
        # Sends the `agent` that started emoting.
        AgentBeginsEmotingEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` in this zone ends emoting. This will not signal if the `agent` is on the *Safe Team* or if *Affects Players* is disabled.
        # Sends the `agent` that stopped emoting.
        AgentEndsEmotingEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` enters this zone.
        # Sends the `agent` entering this zone.
        AgentEntersEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` exits this zone.
        # Sends the `agent` exiting this zone.
        AgentExitsEvent<public>:listenable(agent) = external {}

        # Updates *Selected Class* to `Agent`'s class.
        UpdateSelectedClass<public>(Agent:agent):void = external {}

        # Updates *Selected Team* to `Agent`'s team.
        UpdateSelectedTeam<public>(Agent:agent):void = external {}

        # Is true when `Agent` is in the zone.
        IsInVolume<public>(Agent:agent)<transacts><decides>:void = external {}

        # Returns an array of agents that are currently occupying the volume.
        GetAgentsInVolume<public>()<reads>:[]agent = external {}

    # Used to create a zone where players are put into a skydive state. Can customize the amount of force used to push the player, and how fast players are launched into the air. The direction of the push is in relation to the device, so you can rotate and overlap several devices, then use variable speeds to create pneumatic tubes that propel players in different directions. You can even create unique traversal (traveling) options, where players can use these zones to reach places on your island they couldn't reach any other way.
    skydive_volume_device<public> := class<concrete><final>(effect_volume_device):
        # Signaled when an `agent` enters the volume.
        # Sends the `agent` that entered the volume.
        AgentEntersEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` exits the volume.
        # Sends the `agent` that exited the volume.
        AgentExitsEvent<public>:listenable(agent) = external {}

        # Signaled when the zone changes from empty to occupied.
        # Sends the `agent` that entered the volume.
        ZoneOccupiedEvent<public>:listenable(agent) = external {}

        # Signaled when the zone changes from occupied to empty.
        # Sends the `agent` that last left the volume.
        ZoneEmptiedEvent<public>:listenable(agent) = external {}

        # Enables volume locking which prevents users from leaving the volume once they've entered.
        EnableVolumeLocking<public>():void = external {}

        # Disables volume locking which prevents users from leaving the volume once they've entered.
        DisableVolumeLocking<public>():void = external {}

        # Is true when `Agent` is in the volume.
        IsInVolume<public>(Agent:agent)<transacts><decides>:void = external {}

        # Returns an array of agents that are currently occupying the volume.
        GetAgentsInVolume<public>()<reads>:[]agent = external {}

    # Used to spawn guards that can patrol and attack other `agent`s.
    guard_spawner_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when a guard is spawned.
        # Sends the `agent` guard who was spawned.
        SpawnedEvent<public>:listenable(agent) = external {}

        # Signaled when a guard has identified an opponent.
        # `Source` is the guard who is aware.
        # `Target` is the agent who alerted the guard.
        AlertedEvent<public>:listenable(device_ai_interaction_result) = external {}

        # Signaled when a guard has lost track of a target.
        # `Source` is the guard that lost track of a target.
        # `Target` is the `agent` no longer targeted by the guard.
        TargetLostEvent<public>:listenable(device_ai_interaction_result) = external {}

        # Signaled when a guard becomes suspicious.
        # Sends the `agent` guard who is suspicious.
        SuspiciousEvent<public>:listenable(agent) = external {}

        # Signaled when a guard becomes unaware.
        # Sends the `agent` guard who is unaware.
        UnawareEvent<public>:listenable(agent) = external {}

        # Signaled when guard is damaged.
        # `Source` is the `agent` that damaged the guard. If the guard was damaged by a non-agent then `false` is returned.
        # `Target` is the guard that was damaged.
        DamagedEvent<public>:listenable(device_ai_interaction_result) = external {}

        # Signaled when a guard is eliminated.
        # `Source` is the `agent` that eliminated the guard. If the guard was eliminated by a non-agent then `Source` is 'false'.
        # `Target` is the guard that was eliminated.
        EliminatedEvent<public>:listenable(device_ai_interaction_result) = external {}

        # Signaled when a guard eliminates an agent.
        # `Source` is the guard that eliminated the agent.
        # `Target` is the agent that was eliminated.
        EliminatingEvent<public>:listenable(device_ai_interaction_result) = external {}

        # Signaled when a guard is hired by a player.
        # `Source` is the `agent` who hired the guard.
        # `Target` is the guard that was hired.
        HiredEvent<public>:listenable(device_ai_interaction_result) = external {}

        # Signaled when a guard is dismissed by a player.
        # `Source` is the `agent` who dismissed the guard.
        # `Target` is the guard that was dismissed.
        DismissedEvent<public>:listenable(device_ai_interaction_result) = external {}

        # Enables this device. Guards will start to spawn.
        Enable<public>():void = external {}

        # Disables this device. Guards will despawn if *Despawn Guards When Disabled* is set.
        Disable<public>():void = external {}

        # Resets the spawn count allowing spawning of a new batch of guards.
        Reset<public>():void = external {}

        # Tries to spawn a guard.
        Spawn<public>():void = external {}

        # Tries to spawn a guard. If *Auto Hire When Spawned* is set to *Triggering Player* the guard will be hired by `Instigator`.
        Spawn<public>(Instigator:agent):void = external {}

        # Despawns guards.
        Despawn<public>():void = external {}

        # Despawns guards. `Instigator` will be considered as the eliminator of those guards.
        Despawn<public>(Instigator:agent):void = external {}

        # Hires guards to `Instigator`'s team.
        Hire<public>(Instigator:agent):void = external {}

        # Dismisses all hired guards.
        DismissAllHiredGuards<public>():void = external {}

        # Dismisses all hired guards that were recruited by `Instigator`.
        DismissAgentHiredGuards<public>(Instigator:agent):void = external {}

        # Forces guards to attack `Target`, bypassing perception checks.
        # 'ForgetTime' ranges from 0.0 to 600.0 (in seconds, default is 600.0), it is the time after which the target will be ignored if not found.
        # 'ForgetDistance' ranges from 0.0 to 100000.0 (in centimeters, default is 100000.0), it is the distance at which the target will be ignored if not found.
        ForceAttackTarget<public>(Target:agent, ?ForgetTime:float = external {}, ?ForgetDistance:float = external {}):void = external {}

        # Allows guards to be hired.
        SetGuardsHireable<public>():void = external {}

        # Prevents guards from being hired.
        SetGuardsNotHireable<public>():void = external {}

        # Returns the spawn limit of the device.
        GetSpawnLimit<public>()<transacts>:int = external {}
    Patchwork<public> := module:
        # Base class for all Patchwork devices.
        patchwork_device<public> := class<concrete>(creative_device_base):
            # Enables this device.
            Enable<public>():void = external {}

            # Disables this device.
            Disable<public>():void = external {}

        # Modify a setting on another Patchwork device in a regularly repeating pattern.
        lfo_modulator_device<public> := class<concrete><final>(patchwork_device):

        # Create drum note patterns for Patchwork devices.
        drum_sequencer_device<public> := class<concrete><final>(patchwork_device):

        # Apply a distortion effect to Patchwork audio inputs.
        distortion_effect_device<public> := class<concrete><final>(patchwork_device):

        cable_splitter_device<public> := class<concrete><final>(patchwork_device):

        # Turn Patchwork note inputs into audio using instrument samples.
        instrument_player_device<public> := class<concrete><final>(patchwork_device):

        song_sync_device<public> := class<concrete><final>(patchwork_device):

        # Modify a setting on another Patchwork device in steps over time.
        step_modulator_device<public> := class<concrete><final>(patchwork_device):

        # Modify a setting on another Patchwork device when triggered.
        value_setter_device<public> := class<concrete><final>(patchwork_device):

        # Apply an echo effect to Patchwork audio inputs.
        echo_effect_device<public> := class<concrete><final>(patchwork_device):

        # Send events to devices based on Patchwork note inputs.
        note_trigger_device<public> := class<concrete><final>(patchwork_device):

        # Turn Patchwork note inputs into audio using customizable sound synthesis.
        omega_synthesizer_device<public> := class<concrete><final>(patchwork_device):

        # Turn Patchwork note inputs into audio using drum samples.
        drum_player_device<public> := class<concrete><final>(patchwork_device):

        # Create melodic note patterns for Patchwork devices.
        note_sequencer_device<public> := class<concrete><final>(patchwork_device):

        # Output Patchwork audio for players to hear.
        speaker_device<public> := class<concrete><final>(patchwork_device):

        # Provides a shared tempo, key, and timeline for Patchwork devices to follow.
        music_manager_device<public> := class<concrete><final>(patchwork_device):

        # Transpose Patchwork note inputs to follow a chord progression.
        note_progressor_device<public> := class<concrete><final>(patchwork_device):

    using {/Verse.org/Native}
    using {/UnrealEngine.com/Temporary/SpatialMath}
    # Used in conjunction with `advanced_storm_controller_device` to customize individual storm phases.
    advanced_storm_beacon_device<public> := class<concrete><final>(creative_device_base):
        # Teleports the `advanced_storm_beacon_device` to the specified `Position` and `Rotation`. 
        # Existing storms will not target the new location, but newly generated storms will.
        TeleportTo<override>(Position:(/UnrealEngine.com/Temporary/SpatialMath:)vector3, Rotation:(/UnrealEngine.com/Temporary/SpatialMath:)rotation)<transacts><decides>:void = external {}

        # Teleports the `advanced_storm_beacon_device` to the specified location defined by `Transform`, also applies rotation and scale accordingly. 
        # Existing storms will not target the new location, but newly generated storms will.
        TeleportTo<override>(Transform:(/UnrealEngine.com/Temporary/SpatialMath:)transform)<transacts><decides>:void = external {}

        # Moves the `advanced_storm_beacon_device` to the specified `Position` and `Rotation` over the specified time, in seconds. 
        # Existing storms will not target the new location, but newly generated storms will.
        MoveTo<override>(Position:(/UnrealEngine.com/Temporary/SpatialMath:)vector3, Rotation:(/UnrealEngine.com/Temporary/SpatialMath:)rotation, OverTime:float)<suspends>:move_to_result = external {}

        # Moves the `advanced_storm_beacon_device` to the specified `Transform` over the specified time, in seconds. 
        # Existing storms will not target the new location, but newly generated storms will.
        MoveTo<override>(Transform:(/UnrealEngine.com/Temporary/SpatialMath:)transform, OverTime:float)<suspends>:move_to_result = external {}

    # Used to control a Battle Royale-style storm with up to 50 phases.
    # 
    # Like `basic_storm_controller_device`s, you can use this storm to keep players inside a playable area, but unlike the `basic_storm_controller_device`, this device generates multiple storm phases. When used in conjunction with `advanced_storm_beacon_device`s, you can customize each phase of the storm by applying one or more beacons and setting customization options for the specific phase you assign to that beacon.
    advanced_storm_controller_device<public> := class<concrete><final>(storm_controller_device):

    # Used to boost `agent`s, vehicles, and other objects upwards into the air.
    air_vent_device<public> := class<concrete><final>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Activates this device.
        Activate<public>():void = external {}

    # Evaluates attributes for `agent` when signaled from other devices. Acts as branching logic, checking whether the `agent` associated with the signal passes all of the tests setup in this device, then sends a signal on either `PassEvent` or `FailEvent`.
    attribute_evaluator_device<public> := class<concrete><final>(trigger_base_device):
        # Signaled when the `agent` from `EvaluateAgent` passes the requirements specified by this device.
        # Sends the `agent` originally passed to this device in `EvaluateAgent`.
        PassEvent<public>:listenable(agent) = external {}

        # Signaled when the `agent` from `EvaluateAgent` fails the requirements specified by this device.
        # Sends the `agent` originally passed to this device in `EvaluateAgent`.
        FailEvent<public>:listenable(agent) = external {}

        # Tests whether the specified agent satisfies the required conditions specified on the device (e.g. eliminations/score), and fires either the `PassEvent` or `FailEvent` accordingly.
        EvaluateAgent<public>(Agent:agent):void = external {}

    # Used to spawn various types of balls. Can be used to control HUD elements related to the spawned balls.
    ball_spawner_device<public> := class<concrete><final>(creative_device_base):
        # Shows the floating HUD Icons to players, if these have been configured by the device.
        ShowHUD<public>():void = external {}

        # Hides the floating HUD Icons from players, if these have been configured by the device.
        HideHUD<public>():void = external {}

    # Base class for devices that spawn items.
    base_item_spawner_device<public> := class<abstract><epic_internal>(creative_device_base):
        # Signaled when an `agent` picks up the spawned item.
        # Sends the `agent` that picked up the item.
        ItemPickedUpEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

    # A simplified storm device that provides a way to create a single-phase storm and control its basic behaviors.
    # To control multiple phases of the storm see `advanced_storm_controller_device`.
    basic_storm_controller_device<public> := class<concrete><final>(storm_controller_device):

    # Used to show an in world visual effect and/or a HUD marker at the desired location.
    beacon_device<public> := class<concrete><final>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Adds the specified `agent` to a list of `agent`s that the Beacon will be shown to. This list of `agent`s is maintained separately from the Team Visibility set of `agent`s.
        AddToShowList<public>(Agent:agent):void = external {}

        # Removes the specified `agent` from the show list. The `agent` will still see the Beacon if they meet the Team Visibility check.
        RemoveFromShowList<public>(Agent:agent):void = external {}

        # Removes all `agent`s from the show list. `Agent`s will still see the Beacon if they meet the Team Visibility check.
        RemoveAllFromShowList<public>():void = external {}

    # Used to display custom text messages on a billboard.
    billboard_device<public> := class<concrete><final>(creative_device_base):
        # Shows the billboard text.
        ShowText<public>():void = external {}

        # Hides the billboard text.
        HideText<public>():void = external {}

        # Updates the device display to show the current *Text*.
        UpdateDisplay<public>():void = external {}

        # Sets the visibility of the device border mesh. This also determines whether the device collision is enabled.
        SetShowBorder<public>(Show:logic):void = external {}

        # Returns `true` if the device border is enabled.
        GetShowBorder<public>()<transacts>:logic = external {}

        # Sets the device *Text*.
        SetText<public>(Text:message):void = external {}

        # Sets the *Text Size* of the device *Text*. Clamped to range [8, 24].
        SetTextSize<public>(Size:int):void = external {}

        # Returns the *Text Size* of the device *Text*.
        GetTextSize<public>()<transacts>:int = external {}

    # Used to create a button which can trigger other devices when an agent interacts with it.
    button_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when an `agent` successfully interacts with the button for `GetInteractionTime` seconds.
        # Sends the `agent` that interacted with the button.
        InteractedWithEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Sets the text that displays when an `agent` is close to this button and looks at it. `Text` is limited to `64` characters.
        SetInteractionText<public>(Text:message):void = external {}

        # Sets the duration of the interaction required to activate this device (in seconds).
        SetInteractionTime<public>(Time:float):void = external {}

        # Returns the duration of the interaction required to activate this device (in seconds).
        GetInteractionTime<public>()<transacts>:float = external {}

        # Sets the maximum amount of times this button can be interacted with before it will be disabled.
        #  * `MaxCount` must be between `0` and `10`.
        #  * `0` indicates no limit on trigger count.
        SetMaxTriggerCount<public>(MaxCount:int):void = external {}

        # Returns the maximum amount of times this button can be interacted with before it will be disabled.
        #  * `GetTriggerMaxCount` will be between `0` and `10`.
        #  * `0` indicates no limit on trigger count.
        GetMaxTriggerCount<public>()<transacts>:int = external {}

        # Returns the number of times that this button can still be interacted with before it will be disabled. Will return `0` if `GetMaxTriggerCount` is unlimited.
        GetTriggerCountRemaining<public>()<transacts>:int = external {}

    # Used to create a zone that can trigger effects once players enter it. Can be set up to be capturable by a team, to provide a score while held, or to require a specific item as a drop-off.
    capture_area_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when an `agent` enters this device area.
        # Sends the `agent` that entered this device area.
        AgentEntersEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` exits this device area.
        # Sends the `agent` that exited this device area.
        AgentExitsEvent<public>:listenable(agent) = external {}

        # Signaled when the first `agent` enters this device area.
        # Sends the `agent` that entered this device area.
        FirstAgentEntersEvent<public>:listenable(agent) = external {}

        # Signaled when the last `agent` exits this device area.
        # Sends the `agent` that exited this device area.
        LastAgentExitsEvent<public>:listenable(agent) = external {}

        # Signaled when this device is contested.
        # Sends the `agent` that is contesting this device.
        AreaIsContestedEvent<public>:listenable(agent) = external {}

        # Signaled when this device is scored.
        # Sends the `agent` that scored this device.
        AreaIsScoredEvent<public>:listenable(agent) = external {}

        # Signaled when this device control change starts.
        # Sends the `agent` that is triggering this device control change.
        ControlChangeStartsEvent<public>:listenable(agent) = external {}

        # Signaled when this device control changes.
        # Sends the `agent` that triggered this device control change.
        ControlChangeEvent<public>:listenable(agent) = external {}

        # Signaled when an item is consumed by this device.
        # Sends the `agent` that provided the item to this device.
        ItemIsConsumedEvent<public>:listenable(agent) = external {}

        # Signaled when an item is delivered to this device.
        # Sends the `agent` that delivered the item to this device.
        ItemIsDeliveredEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Toggles between `Enable` and `Disable`.
        ToggleEnabled<public>():void = external {}

        # Allows this device to be captured.
        AllowCapture<public>():void = external {}

        # Disallows this device from being captured.
        DisallowCapture<public>():void = external {}

        # Toggles between `AllowCapture` and `DisallowCapture`.
        ToggleCaptureAllowed<public>():void = external {}

        # Resets control of this device for all teams.
        Reset<public>():void = external {}

        # Gives control of this device to the capturing `agent`'s team.
        GiveControl<public>(Agent:agent):void = external {}

        # Clears control of this device for all teams.
        Neutralize<public>():void = external {}

        # Activates the objective pulse for this device.
        ActivateObjectivePulse<public>():void = external {}

        # Deactivates the objective pulse for this device.
        DeactivateObjectivePulse<public>():void = external {}

        # Sets the *Capture Height* (in meters) of the capture area.
        SetHeight<public>(Height:float):void = external {}

        # Returns the *Capture Height* (in meters) of the capture area.
        GetHeight<public>()<reads>:float = external {}

        # Sets the *Capture Radius* (in meters) of the capture area.
        SetRadius<public>(Radius:float):void = external {}

        # Returns the *Capture Radius* (in meters) of the capture area.
        GetRadius<public>()<reads>:float = external {}

        # Returns an array of agents that are currently occupying the Capture Area.
        GetAgentsInVolume<public>()<reads>:[]agent = external {}

        # Is true when `Agent` is in the Capture Area.
        IsInArea<public>(Agent:agent)<transacts><decides>:void = external {}

    # Spawns and tracks a single item as a game objective (e.g. flag).
    capture_item_spawner_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when spawned item is captured.
        # Sends the `agent` that captured the item.
        ItemCapturedEvent<public>:listenable(agent) = external {}

        # Signaled when spawned item is picked up.
        # Sends the `agent` that picked up the item.
        ItemPickedUpEvent<public>:listenable(agent) = external {}

        # Signaled when spawned item is dropped.
        # Sends the `agent` that dropped the item.
        ItemDroppedEvent<public>:listenable(agent) = external {}

        # Signaled when spawned item is returned.
        # Sends the `agent` that returned the item.
        ItemReturnedEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

    # Sends an Event for every signal it receives. A simple relay to connect multiple devices together.
    # It can also broadcast an event to the playspace and listen for any other channel devices broadcasting
    # to the playspace.
    channel_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when either `Transmit` is called.
        ReceivedTransmitEvent<public>:listenable(?agent) = external {}

        # Calls `ReceivedTransmitEvent`, optionally with `Agent` as the instigator.
        Transmit<public>(Agent:?agent):void = external {}

    # Used together with `class_designer_device` to control how/when created classes can be accessed by `agent`s.
    class_and_team_selector_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when an `agent` changes class.
        # Sends the `agent` whose class changed.
        ClassSwitchedEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` changes teams.
        # Sends the `agent` whose team changed.
        TeamSwitchedEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Changes the `Agent`'s class.
        ChangeClass<public>(Agent:agent):void = external {}

        # Changes the `Agent`'s team.
        ChangeTeam<public>(Agent:agent):void = external {}

        # Changes the `Agent`'s team and class.
        ChangeTeamAndClass<public>(Agent:agent):void = external {}

        # Changes the selecting team.
        ChangeSelectorTeam<public>(Agent:agent):void = external {}

    # Used together with `class_selector_device` to create class based gameplay. Defines custom class attributes and inventory loadouts.
    class_designer_device<public> := class<concrete><final>(creative_device_base):
        # Returns an array of agents that are currently of *Class* defined by this device.
        GetClassMembers<public>()<reads>:[]agent = external {}

        # Is true if `Agent` is on *Class* defined by this device.
        IsOfClass<public>(Agent:agent)<transacts><decides>:void = external {}

    # Used to place a collectible item into the world.
    collectible_object_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when the collectible item is collected.
        # Sends the `agent` that collected the item.
        CollectedEvent<public>:listenable(agent) = external {}

        # Makes the collectible visible.
        Show<public>():void = external {}

        # Makes the collectible invisible.
        Hide<public>():void = external {}

        # Immediately respawns the object for the instigating agent.
        # This will be affected by the option *Consume If Collected By*.
        (/Fortnite.com/Devices/collectible_object_device:)Respawn<public>(Agent:agent):void = external {}

        # Immediately respawns the object for all agents.
        RespawnForAll<public>():void = external {}

    # Used to create a tile that changes colors when `agent`s interact with it.
    color_changing_tiles_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when this device changes color.
        # Sends the `agent` that interacted with this device.
        ActivatedEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Shows this device in the world.
        Show<public>():void = external {}

        # Hides this device from the world.
        Hide<public>():void = external {}

        # Resets this device to its initial settings.
        Reset<public>():void = external {}

        # Sets the color of the tile to `Agent`'s team color.
        (/Fortnite.com/Devices/color_changing_tiles_device:)SetTeam<public>(Agent:agent):void = external {}

    # Used to create a specialized button which can only be activated when `agent`s are carrying specific items.
    conditional_button_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when this device is activated.
        # Sends the `agent` that activated this device.
        ActivatedEvent<public>:listenable(agent) = external {}

        # Signaled when this device fails to activate because `agent` didn't have the required items.
        # Sends the `agent` that attempted to activate the device.
        NotEnoughItemsEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Resets this device to its original settings.
        Reset<public>():void = external {}

        # Activates this device. `Agent` is used as the instigator of the action.
        Activate<public>(Agent:agent):void = external {}

        # Toggles the conditional button state. `Agent` is used as the instigator of the action.
        Toggle<public>(Agent:agent):void = external {}

        # Sets the text that appears when `agent`s approach the device. `Text` is limited to `150` characters and will revert back to default if empty.
        SetInteractionText<public>(Text:message):void = external {}

        # Sets the time (in seconds) that an interaction with this device should take to complete.
        SetInteractionTime<public>(Time:float):void = external {}

        # Returns the time (in seconds) that an interaction with this device will take to complete.
        GetInteractionTime<public>()<transacts>:float = external {}

        # Sets the quantity of a specific key item type that needs to be collected in order to activate the switch.
        # `KeyItemIndex` ranges from `0` to number of key item types - 1.
        SetItemCountRequired<public>(KeyItemIndex:int, Count:int):void = external {}

        # Returns the total quantity of a specific key item type that needs to be collected in order to activate the switch.
        GetItemCountRequired<public>(KeyItemIndex:int)<transacts>:int = external {}

        # Returns the remaining quantity of a specific key item type that needs to be collected in order to activate the switch.
        GetRemainingItemCountRequired<public>(KeyItemIndex:int)<transacts>:int = external {}

        # Sets the score to be awarded for a key item. `KeyItemIndex` ranges from `0` to number of key item types - 1.
        SetItemScore<public>(KeyItemIndex:int, Score:int):void = external {}

        # Returns the score to be awarded for a key item.
        GetItemScore<public>(KeyItemIndex:int)<transacts>:int = external {}

        # Returns how many items an `Agent` has of the item stored at `KeyItemIndex`.
        GetItemCount<public>(Agent:agent, KeyItemIndex:int):int = external {}

        # Returns if the `Agent` has all of the items required to interact with this Device.
        HasAllItems<public>(Agent:agent)<transacts><decides>:void = external {}

        # Returns if the `Agent` is currently holding any of the items stored in the Device.
        IsHoldingItem<public>(Agent:agent)<transacts><decides>:void = external {}

        # Returns if the `Agent` is currently holding the item stored at `KeyItemIndex`.
        IsHoldingItem<public>(Agent:agent, KeyItemIndex:int)<transacts><decides>:void = external {}

    # Used to amplify an `agent`'s damage temporarily. This applies to any weapon the `agent` is using at the time of the powerup.
    damage_amplifier_powerup_device<public> := class<concrete><final>(powerup_device):
        # Sets the *Magnitude* for this powerup, clamped to the Min and Max defined in the device.
        # Will not apply to any currently applied effects.
        # For the Damage Amplifier Powerup, this is the damage multiplier.
        SetMagnitude<public>(Magnitude:float):void = external {}

        # Returns the current *Magnitude* for the powerup.
        # For the Damage Amplifier Powerup, this is the damage multiplier.
        GetMagnitude<public>()<transacts>:float = external {}

    # Used to spawn items when an `agent` or specified target is eliminated.
    elimination_manager_device<public> := class<concrete><final>(base_item_spawner_device):
        # Signaled when a qualifying elimination occurs.
        # Sends the eliminated `agent`.
        EliminatedEvent<public>:listenable(agent) = external {}

        # Signaled when a qualifying elimination occurs.
        # Sends the eliminator `agent`. If the eliminator is a non-agent then `false` is returned.
        EliminationEvent<public>:listenable(?agent) = external {}

    # Used to customize high level properties of the game mode.
    experience_settings_device<public> := class<concrete><final>(creative_device_base):

    # Hazard which deals damage in a radius around it when destroyed or triggered.
    explosive_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when this device explodes.
        # Sends the `agent` that caused the explosion.
        ExplodedEvent<public>:listenable(agent) = external {}

        # Shows this device in the world.
        Show<public>():void = external {}

        # Hides this device from the world.
        Hide<public>():void = external {}

        # Resets this device.
        Reset<public>():void = external {}

        # Triggers this device to explode. Passes `Agent` as the instigator of the explosion.
        Explode<public>(Agent:agent):void = external {}

    # Used to provide fuel sources for vehicles. Can also be used to deal considerable damage to `agent`s and the environment when destroyed.
    fuel_pump_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when the fuel pump is emptied.
        # Sends the `agent` that emptied the fuel pump.
        EmptyEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Resets fuel stock to *Fuel Capacity* value.
        Reset<public>():void = external {}

        # Grants fuel to `Agent`.
        Empty<public>(Agent:agent):void = external {}

    # Used to let `agent`s slide on any surface with accompanying visual and audio effects.
    grind_powerup_device<public> := class<concrete><final>(powerup_device):

    # Used to create a holographic screen that displays a clock or other curated images.
    holoscreen_device<public> := class<concrete><final>(creative_device_base):

    # Used to show custom HUD messages to one or more `agent`s.
    hud_message_device<public> := class<concrete><final>(creative_device_base):
        # Shows the currently set HUD *Message* on `Agent`s screen. Will replace any previously active message.
        # Use this when the device is setup to target specific `agent`s.
        Show<public>(Agent:agent):void = external {}

        # Shows the currently set *Message* HUD message on screen. Will replace any previously active message.
        Show<public>():void = external {}

        # Hides the HUD message.
        Hide<public>():void = external {}

        @available {MinUploadedAtFNVersion := 3100}
        # Hides the currently set HUD *Message* on `Agent`s screen.
        # Use this when the device is setup to target specific `agent`s.
        Hide<public>(Agent:agent):void = external {}

        # Displays a Custom message to a specific *Agent* that you define.Setting *DisplayTime* to `0.0` will display the HUD message persistently.If not defined, or less than `0.0` the message will show for the time set on the device.
        Show<public>(Agent:agent, Message:message, ?DisplayTime:float = external {}):void = external {}

        # Displays a Custom message that you define for all PlayersSetting *DisplayTime* to `0.0` will display the HUD message persistently.If not defined, or less than `0.0` the message will show for the time set on the device.
        Show<public>(Message:message, ?DisplayTime:float = external {}):void = external {}

        # Sets the time (in seconds) the HUD message will be displayed. `0.0` will display the HUD message persistently.
        SetDisplayTime<public>(Time:float):void = external {}

        # Returns the time (in seconds) for which the HUD message will be displayed. `0.0` means the message is displayed persistently.
        GetDisplayTime<public>()<transacts>:float = external {}

        # Sets the *Message* to be displayed when the HUD message is activated. `Text` is clamped to 150 characters.
        SetText<public>(Text:message):void = external {}

    # Used to grant items to `agent`s. Items can either be dropped at the `agent`'s location or added directly to their inventory.
    item_granter_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when an item is granted to an `agent`.
        # Sends the `agent` that was granted the item.
        ItemGrantedEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Clears saved data for `Agent`, preventing them from receiving items while offline. This only works when *Grant While Offline* is set to *Yes*.
        ClearSaveData<public>(Agent:agent):void = external {}

        # Cycles to the next item. If *Grant on Cycle* is set `Agent` will be granted the item.
        CycleToNextItem<public>(Agent:agent):void = external {}

        # Cycles to the previous item. If *Grant on Cycle* is set `Agent` will be granted the item.
        CycleToPreviousItem<public>(Agent:agent):void = external {}

        # Cycles to a random item. If *Grant on Cycle* is set `Agent` will be granted the item.
        CycleToRandomItem<public>(Agent:agent):void = external {}

        # Grants an item to `Agent`.
        GrantItem<public>(Agent:agent):void = external {}

        # Grants an item without requiring an agent reference.
        #  This only works when *Receiving Players* is set to *All* or *Team Index*.
        GrantItemToAll<public>():void = external {}

        @available {MinUploadedAtFNVersion := 3100}
        # Grants an item at a specific `ItemIndex` to an `Agent`.
        #  `Index` should be between `0` and the available item count - 1.
        #  If Value is out of bounds, which item is granted is determined by *Cycle Behavior*.
        GrantItemIndex<public>(Agent:agent, ItemIndex:int):void = external {}

        @available {MinUploadedAtFNVersion := 3100}
        # Grants an item at a specific `ItemIndex` to all players.
        #  Only functions when *Receiving Players* is set to *All* or *Team Index*.
        #  `Index` should be between `0` and the available item count - 1.
        #  If Value is out of bounds, which item is granted is determined by *Cycle Behavior*.
        GrantItemIndex<public>(ItemIndex:int):void = external {}

        # Returns the current Item Index that this device will grant when activated.
        GetItemIndex<public>()<transacts>:int = external {}

        # Restocks this device back to its starting inventory count.
        RestockItems<public>():void = external {}

        # Sets the next item to be granted.
        #  * `Index` should be between `0` and the available item count - 1.
        #  * Calling `SetNextItem` with an invalid index will do nothing.
        SetNextItem<public>(Index:int):void = external {}

    # Used to configuration and spawn items that players can pick up and use.
    item_spawner_device<public> := class<concrete><final>(base_item_spawner_device):
        # Cycles device to next configured item.
        CycleToNextItem<public>():void = external {}

        # Spawns the current item.
        SpawnItem<public>():void = external {}

        # Sets device *Respawn Item on Timer* option (see `SetTimeBetweenSpawns`)
        SetEnableRespawnTimer<public>((local:)Respawn:logic):void = external {}

        # Returns device *Respawn Item on Timer* option (see `SetTimeBetweenSpawns`)
        GetEnableRespawnTimer<public>()<transacts>:logic = external {}

        # Sets the *Time Between Spawns* (in seconds) after an item is collected before the next is spawned, if this device has *Respawn Item on Timer* enabled (see `SetEnableRespawnTimer`)
        SetTimeBetweenSpawns<public>(Time:float):void = external {}

        # Returns the *Time Between Spawns* (in seconds) after an item is collected before the next is spawned, if this device has *Respawn Item on Timer* enabled (see `SetEnableRespawnTimer`)
        GetTimeBetweenSpawns<public>()<transacts>:float = external {}

    # Used to customize the state and accessibility of doors. `lock_device` only works with assets that have a door attached.
    lock_device<public> := class<concrete><final>(creative_device_base):
        # Locks the door. `Agent` is the instigator of the action.
        Lock<public>(Agent:agent):void = external {}

        # Unlocks the door. `Agent` is the instigator of the action.
        Unlock<public>(Agent:agent):void = external {}

        # Toggles between `Lock` and `Unlock`. `Agent` is the instigator of the action.
        ToggleLocked<public>(Agent:agent):void = external {}

        # Opens the door. `Agent` is the instigator of the action.
        Open<public>(Agent:agent):void = external {}

        # Closes the door. `Agent` is the instigator of the action.
        Close<public>(Agent:agent):void = external {}

        # Toggles between `Open` and `Close`. `Agent` is the instigator of the action.
        ToggleOpened<public>(Agent:agent):void = external {}

    # Used to create custom points of interest and markers on the minimap and overview map.
    map_indicator_device<public> := class<concrete><final>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Activates an objective pulse at `Agent`'s location pointing toward this device.
        ActivateObjectivePulse<public>(Agent:agent):void = external {}

        # Deactivates the objective pulse at `Agent`'s location.
        DeactivateObjectivePulse<public>(Agent:agent):void = external {}

    # Used to take players to different islands and to link experiences together.
    matchmaking_portal_device<public> := class<concrete><final>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

    # Used to temporarily modify the speed of `agent`s and vehicles.
    movement_modulator_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when this device is activated by an `agent`.
        # Sends the `agent` that activated this device.
        ActivationEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Activates this device's movement effect on `Agent`.
        Activate<public>(Agent:agent):void = external {}

    # Provides a collection of destructible devices that you can select from to use as objectives in your game.
    objective_device<public> := class<concrete><final>(creative_device_base, healthful, damageable, healable):
        GetHealth<override>()<transacts>:float = external {}

        SetHealth<override>(Health:float)<transacts>:void = external {}

        GetMaxHealth<override>()<transacts>:float = external {}

        SetMaxHealth<override>(MaxHealth:float)<transacts>:void = external {}

        Damage<override>(Amount:float):void = external {}

        Damage<override>(Args:damage_args):void = external {}

        DamagedEvent<override>():listenable(damage_result) = external {}

        Heal<override>(Amount:float):void = external {}

        Heal<override>(Args:healing_args):void = external {}

        HealedEvent<override>():listenable(healing_result) = external {}

        # Signaled when this device has been destroyed by an `agent`.
        # Sends the `agent` that destroyed this device.
        DestroyedEvent<public>:listenable(agent) = external {}

        # Shows this device in the world.
        Show<public>():void = external {}

        # Hides this device from the world.
        Hide<public>():void = external {}

        # Activates an objective pulse at `Agent`'s location pointing toward this device.
        ActivateObjectivePulse<public>(Agent:agent):void = external {}

        # Deactivates the objective pulse at `Agent`'s location.
        DeactivateObjectivePulse<public>(Agent:agent):void = external {}

        # Destroys the objective item. This is done regardless of the visibility or health of the item.
        Destroy<public>(Agent:agent):void = external {}

        # Sets the device either invulnerable or damageable
        SetInvulnerable<public>(Invulnerable:logic):void = external {}

    # Specialized `trigger_base_device` that will fire output events based on line of sight between `agent`s and the device.
    perception_trigger_device<public> := class<concrete><final>(trigger_base_device):
        # Signaled when an `agent` has direct line of sight to this device.
        # Sends the `agent` that has seen this device.
        AgentLooksAtDeviceEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` has lost direct line of sight to this device.
        # Sends the `agent` that has lost sight of this device.
        AgentLooksAwayFromDeviceEvent<public>:listenable(agent) = external {}

        # Signaled when this device has direct line of sight to an `agent`.
        # Sends the `agent` seen by this device.
        DeviceSeesAgentEvent<public>:listenable(agent) = external {}

        # Signaled when this device loses direct line of sight to an `agent`.
        # Sends the `agent` this device has lost sight of.
        DeviceLosesSightOfAgentEvent<public>:listenable(agent) = external {}

        # Returns an array of agents that are currently of the class defined by this device.
        GetPerceivedAgents<public>()<reads>:[]agent = external {}

        # Succeeds when `Agent` is registered to this checkpoint.
        IsPerceived<public>(Agent:agent)<transacts><decides>:void = external {}

        # Returns an array of agents that are currently of the class defined by this device.
        GetLookingAtDeviceAgents<public>()<reads>:[]agent = external {}

        # Succeeds when `Agent` is registered to this checkpoint.
        IsLookingAtDevice<public>(Agent:agent)<transacts><decides>:void = external {}

    # A triggered bumper that can knock players back, damage them, and award points.
    pinball_bumper_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when this device is activated by an `agent`.
        # Sends the `agent` that activated this device.
        ActivatedEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Activates this device.
        Activate<public>():void = external {}

    # Used to move, damage, and give scores to players that interact with it. By default, it is activated by any player touching its front face, which rotates it counterclockwise and knocks those players away from it and slightly upward.
    pinball_flipper_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when this device is activated by an `agent`.
        # Sends the `agent` that activated this device.
        ActivatedEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Causes `Agent` to activate this device.
        Activate<public>(Agent:agent):void = external {}

    # Base class for various powerup devices offering common events like `ItemPickedUpEvent`.
    powerup_device<public> := class<abstract><epic_internal>(creative_device_base):
        # Signaled when the powerup is picked up by an `agent`.
        # Sends the `agent` that picked up the powerup.
        ItemPickedUpEvent<public>:listenable(agent) = external {}

        # Spawns the powerup into the experience so users can interact with it.
        Spawn<public>():void = external {}

        # Despawns this powerup from the experience.
        Despawn<public>():void = external {}

        # Updates the *Duration* for this powerup, clamped to the Min and Max defined in the device.
        # Will not apply to any currently applied effects.
        SetDuration<public>(Time:float):void = external {}

        # Returns the *Duration* that this powerup will be active for on any player it is applied to.
        GetDuration<public>()<transacts>:float = external {}

        # If the `Agent` has the effect applied to them, this will return the remaining time the effect has.
        # Returns -1.0 if the effect has an infinite duration.
        # Returns 0.0 if the `Agent` does not have the effect applied.
        GetRemainingTime<public>(Agent:agent)<transacts>:float = external {}

        # Returns the `Agent` has the powerup's effect (or another of the same type) applied to them.
        HasEffect<public>(Agent:agent)<transacts><decides>:void = external {}

        # Succeeds if the powerup is currently spawned.
        IsSpawned<public>()<transacts><decides>:void = external {}

        # Grants this powerup to `Agent`.
        Pickup<public>(Agent:agent):void = external {}

        # Grants this powerup without an agent reference.
        # Requires *Apply To* set to *All Players*.
        Pickup<public>():void = external {}

    # Allows customization of the Prop-o-Matic weapon functions and how the game reacts to players using it.
    prop_o_matic_manager_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when an `agent` begins entering a disguise.
        # Sends the `agent` that began entering the disguise.
        BeginEnteringDisguiseEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` finishes entering a disguise.
        # Sends the `agent` that finished entering the disguise.
        FinishEnteringDisguiseEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` exits a disguise.
        # Sends the `agent` that exited the disguise.
        ExitingDisguiseEvent<public>:listenable(agent) = external {}

        # Toggle Pinging props on/off.
        SetPingProps<public>(On:logic):void = external {}

        # Adjust the ping time.
        SetPingFrequency<public>(Time:float):void = external {}

        # Toggle showing the props remaining on the UI.
        SetShowPropsRemaining<public>(On:logic):void = external {}

        # Toggle showing the prop ping cooldown.
        SetShowPropPingCooldown<public>(On:logic):void = external {}

        # Manually ping all players that are currently hiding as props.
        PingPlayerProps<public>():void = external {}

        # Manually ping a specific player if they are currently a prop.
        PingPlayerProp<public>(Agent:agent):void = external {}

        # Returns whether a player is currently hiding or not.
        IsPlayerProp<public>(Agent:agent)<transacts><decides>:void = external {}

    # Base class for devices that spawn a prop object.
    prop_spawner_base_device<public> := class<abstract><epic_internal>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Spawns the prop associated with this device.
        SpawnObject<public>():void = external {}

        # Destroys all props spawned from this device.
        DestroyAllSpawnedObjects<public>():void = external {}

    # A device used to damage players who collide with it. Can also be used as a trigger to activate other devices.
    pulse_trigger_device<public> := class<concrete><final>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Starts the damage pulse.
        Begin<public>():void = external {}

        # Stops the damage pulse.
        End<public>():void = external {}

        @deprecated
        # This function is deprecated. Use `ResumePulse` instead of this.
        # Resumes the damage sequence from the last position where it was stopped.
        ResumeSequence<public>():void = external {}

        # Resumes the damage pulse from the last position where it was stopped.
        ResumePulse<public>():void = external {}

        # Sets the damage to be applied to those hit by an active pulse. Clamped between `0 <= GetDamage <= 100000`.
        # Pulse visuals will change to reflect whether the pulse causes damage or not.
        SetDamage<public>(Damage:float):void = external {}

        # Returns the damage to be applied to those hit by an active pulse. Clamped between `0 <= GetDamage <= 100000`.
        GetDamage<public>()<transacts>:float = external {}

        # Sets the total number of times this pulse will complete before ending. `LoopCount = 0` indicates the pulse should continue indefinitely.
        SetLoopCount<public>(LoopCount:int):void = external {}

        # Returns the total number of times this pulse will complete before ending.
        # `0` indicates the pulse will continue indefinitely.
        GetLoopCount<public>()<transacts>:int = external {}

        # Sets the speed (in meters per second) at which the pulses generated by this pulse trigger will travel.
        SetWaveSpeed<public>(Speed:float):void = external {}

        # Returns the speed (in meters per second) at which the pulses generated by this pulse trigger will travel.
        GetWaveSpeed<public>()<transacts>:float = external {}

    # Used to play curated music from the device or one or more registered `agent`s.
    radio_device<public> := class<concrete><final>(creative_device_base):
        # Starts playing audio from this device.
        Play<public>():void = external {}

        # Stops playing audio from this device.
        Stop<public>():void = external {}

        # Adds the specified agent as a target for the Radio to play audio from
        Register<public>(Agent:agent):void = external {}

        # Removes the specified agent as a target for the Radio to play audio from if previously Registered
        Unregister<public>(Agent:agent):void = external {}

        # Removes all previously registered agents as targets for the Radio to play audio from
        UnregisterAll<public>():void = external {}

        # Shows this device in the world.
        Show<public>()<transacts>:void = external {}

        # Hides this device from the world.
        Hide<public>()<transacts>:void = external {}

    # Used to generate random numbers between a minimum and maximum value. Events are signaled when numbers are generated.
    #  * *Value Limit 1* is the minimum value for generation.
    #  * *Value Limit 2* is the maximum value for generation.
    rng_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when the generated number >= *Winning Value*.
        WinEvent<public>:listenable(tuple()) = external {}

        # Signaled when the generated number < *Winning Value*.
        LoseEvent<public>:listenable(tuple()) = external {}

        # Signaled when the generated number = maximum.
        RolledMaxEvent<public>:listenable(tuple()) = external {}

        # Signaled when the generated number = minimum.
        RolledMinEvent<public>:listenable(tuple()) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Randomly generate a number between *Value Limit 1* and *Value Limit 2*.
        #  * If the number is >= *Winning Value* then `WinEvent` is fired.
        #  * If the number is < *Winning Value* then `LoseEvent` is fired.
        #  * If the number = minimum then `RolledMinEvent` is fired.
        #  * If the number = maximum then `RolledMaxEvent` is fired.
        #  * `Agent` is used as the Instigator of the roll event.
        Activate<public>(Agent:agent):void = external {}

        # Randomly roll a number within the configured min + max value range.
        #  * If the number is >= *Winning Value* then `WinEvent` is fired.
        #  * If the number is < *Winning Value* then `LoseEvent` is fired.
        #  * If the number = minimum then `RolledMinEvent` is fired.
        #  * If the number = maximum then `RolledMaxEvent` is fired.
        Activate<public>():void = external {}

        # Cancels the active number generation.
        Cancel<public>():void = external {}

    # Used to customize gameplay for any round-based game. It generally defines what happens to the`agent`'s inventory and rewards in each round.
    round_settings_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when a game round starts.
        RoundBeginEvent<public>:listenable(tuple()) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Enables the ability for players to Matchmake into the Island. Only applies to published games that have matchmaking turned on in the Island settings
        EnableMatchmaking<public>():void = external {}

        # Disables the ability for players to Matchmake into the Island. Only applies to published games that have matchmaking turned on in the Island settings
        DisableMatchmaking<public>():void = external {}

        # Toggles between `EnableMatchmaking` and `DisableMatchmaking`.
        ToggleMatchmaking<public>():void = external {}

        # Disables all end-round conditions. The round must be ended through calling `EndRound` or a creative event after this is called.
        DisableEndRoundConditions<public>():void = external {}

        # Ends the round immediately with `Agent`'s team set as the winner of the round.
        EndRound<public>(Agent:agent):void = external {}

    # Used to manipulate scores using in-experience triggers. If *Activating Team* is set to a specific team, then you should use the `agent` overloads of each function. The `agent`'s team will be used to determine if that `agent` is allowed to affect the state of the device.
    score_manager_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when the this device reaches its maximum number of triggers as defined by *Times Can Trigger*.
        # Sends the `agent` who last triggered the device.
        MaxTriggersEvent<public>:listenable(agent) = external {}

        # Signaled when the this device awards points to an `agent`.
        # Sends the `agent` who received the points.
        ScoreOutputEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>(Agent:agent):void = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>(Agent:agent):void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Resets this device to its original state.
        Reset<public>(Agent:agent):void = external {}

        # Resets this device to its original state.
        Reset<public>():void = external {}

        # Grant points to `Agent`.
        Activate<public>(Agent:agent):void = external {}

        # Grants points.
        Activate<public>():void = external {}

        # Increments the score quantity to be awarded by the next activation by `1`.
        Increment<public>(Agent:agent):void = external {}

        # Increments the score quantity to be awarded by the next activation by `1`.
        Increment<public>():void = external {}

        # Decrements the score quantity to be awarded by the next activation by `1`.
        Decrement<public>(Agent:agent):void = external {}

        # Decrements the score quantity to be awarded by the next activation by `1`.
        Decrement<public>():void = external {}

        # Sets the score to be awarded by the next activation to `Value`.
        SetScoreAward<public>(Value:int):void = external {}

        # Returns the score to be awarded by the next activation.
        GetScoreAward<public>()<transacts>:int = external {}

        # Sets the score to be awarded by the next activation to `Agent`'s current score.
        SetToAgentScore<public>(Agent:agent):void = external {}

        # Returns the current score for `Agent`.
        GetCurrentScore<public>(Agent:agent)<transacts>:int = external {}

    # A single customizable pop up target that can be hit by `agent`s to trigger various events.
    shooting_range_target_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when the target is hit in the bullseye area.
        BullseyeHitEvent<public>:listenable(tuple()) = external {}

        # Signaled when the target is hit by an `agent`.
        HitEvent<public>:listenable(tuple()) = external {}

        # Signaled when the target takes enough damage to get knocked down.
        KnockdownEvent<public>:listenable(tuple()) = external {}

        # Signaled when the target moves up slightly, making it harder to hit.
        HopUpEvent<public>:listenable(tuple()) = external {}

        # Signaled when the target moves down slightly, making it harder to hit.
        HopDownEvent<public>:listenable(tuple()) = external {}

        # Signaled when the target moves from laying flat to standing upright.
        PopUpEvent<public>:listenable(tuple()) = external {}

        # Signaled when the target moves from standing upright to laying flat.
        PopDownEvent<public>:listenable(tuple()) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Resets the target to its initial settings.
        Reset<public>():void = external {}

        # Moves an active (standing upright) target up slightly, in an effort to make it harder to hit.
        HopUp<public>():void = external {}

        # Moves an active (standing upright) target down slightly, in an effort to make it harder to hit.
        HopDown<public>():void = external {}

        # Causes a target to transition from lying flat (inactive) to standing upright (active).
        PopUp<public>():void = external {}

        # Causes a target to transition from standing upright (active) to lying flat (inactive).
        PopDown<public>():void = external {}

    # A set of customizable pop up targets that can be hit by players to trigger various events.
    shooting_range_target_track_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when target is hit in the bullseye area.
        BullseyeHitEvent<public>:listenable(tuple()) = external {}

        # Signaled when the target is hit by a player.
        HitEvent<public>:listenable(tuple()) = external {}

        # Signaled when the target is hit by a player.
        KnockdownEvent<public>:listenable(tuple()) = external {}

        # Signaled when the target moves up slightly, making it harder to hit.
        HopUpEvent<public>:listenable(tuple()) = external {}

        # Signaled when the target moves down slightly, making it harder to hit.
        HopDownEvent<public>:listenable(tuple()) = external {}

        # Signaled when the target moves from laying flat to standing upright.
        PopUpEvent<public>:listenable(tuple()) = external {}

        # Signaled when the target moves from standing upright to laying flat.
        PopDownEvent<public>:listenable(tuple()) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Resets the target to its initial settings.
        Reset<public>():void = external {}

        # Moves an active (standing upright) target attached to the track up slightly, in an effort to make it harder to hit
        HopUp<public>():void = external {}

        # Moves an active (standing upright) target attached to the track down slightly, in an effort to make it harder to hit
        HopDown<public>():void = external {}

        # Causes the target attached to the track to transition from lying flat (inactive) to standing upright (active)
        PopUp<public>():void = external {}

        # Causes the target attached to the track to transition from standing upright (active) to lying flat (inactive)
        PopDown<public>():void = external {}

        # Enables movement on the track. This does not start the target moving, it only enables movement.
        EnableTrackMovement<public>():void = external {}

        # Disables movement on the track. This prevents any movement from occurring, until track movement is enabled again.
        DisableTrackMovement<public>():void = external {}

        # Activates the movement track.
        ActivateTrack<public>():void = external {}

        # Deactivates the movement track.
        DeactivateTrack<public>():void = external {}

        # Starts the target moving toward the end of the track.
        MoveToEnd<public>():void = external {}

        # Starts the target moving toward the start of the track.
        MoveToStart<public>():void = external {}

    # Base class for various specialized storm devices. See also: * `basic_storm_controller_device` * `advanced_storm_controller_device`
    storm_controller_device<public> := class<abstract><epic_internal>(creative_device_base):
        # Signaled when storm resizing ends. Use this with the *On Finish Behavior* option for better controls.
        PhaseEndedEvent<public>:listenable(tuple()) = external {}

        # Generates the storm. *Generate Storm On Game Start* must be set to *No* if you choose to use `GenerateStorm`.
        GenerateStorm<public>():void = external {}

        # Destroys the storm.
        DestroyStorm<public>():void = external {}

        # Teleports the `storm_controller_device` to the specified `Position` and `Rotation`. 
        # Existing storms will not target the new location, but newly generated storms will.
        TeleportTo<override>(Position:(/UnrealEngine.com/Temporary/SpatialMath:)vector3, Rotation:(/UnrealEngine.com/Temporary/SpatialMath:)rotation)<transacts><decides>:void = external {}

        # Teleports the `storm_controller_device` to the specified location defined by `Transform`, also applies rotation and scale accordingly. 
        # Existing storms will not target the new location, but newly generated storms will.
        TeleportTo<override>(Transform:(/UnrealEngine.com/Temporary/SpatialMath:)transform)<transacts><decides>:void = external {}

        # Moves the `storm_controller_device` to the specified `Position` and `Rotation` over the specified time, in seconds. 
        # Existing storms will not target the new location, but newly generated storms will.
        MoveTo<override>(Position:(/UnrealEngine.com/Temporary/SpatialMath:)vector3, Rotation:(/UnrealEngine.com/Temporary/SpatialMath:)rotation, OverTime:float)<suspends>:move_to_result = external {}

        # Moves the `storm_controller_device` to the specified `Transform` over the specified time, in seconds. 
        # Existing storms will not target the new location, but newly generated storms will.
        MoveTo<override>(Transform:(/UnrealEngine.com/Temporary/SpatialMath:)transform, OverTime:float)<suspends>:move_to_result = external {}

    # DEPRECATED This device is no longer supported.Used to inform players of other content made by a creator using a Support-A-Creator code. When players activate the device, it will bring up the same Support-A-Creator dialog box they would see in the Creative hub. The Support-A-Creator information shows when the dialog box opens for the player.
    support_a_creator_device<public> := class<concrete><final>(creative_device_base):

    # Used to place the Infinity Blade on your island. When placed, the Infinity Blade becomes available to any player regardless of team affiliation.
    sword_in_the_stone_device<public> := class<concrete><final>(creative_device_base):

    # Provides team and inventory configurations that go beyond the choices the My Island settings provide.
    # Can also be used to customize individual devices and create variations in team setup.
    team_settings_and_inventory_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when an enemy of *Team* is eliminated by a team member.
        # Sends the `agent` team member who eliminated the enemy.
        EnemyEliminatedEvent<public>:listenable(agent) = external {}

        # Signaled when a member of *Team* is eliminated.
        # Sends the `agent` that was eliminated.
        TeamMemberEliminatedEvent<public>:listenable(agent) = external {}

        # Signaled when a member of *Team* is spawned.Sends the `agent` that has spawned.
        TeamMemberSpawnedEvent<public>:listenable(agent) = external {}

        # Signaled when *Team* runs out of respawns.
        TeamOutOfRespawnsEvent<public>:listenable(tuple()) = external {}

        # Ends the round and *Team* wins the round.
        EndRound<public>():void = external {}

        # Is true if `Agent` is on *Team*.
        IsOnTeam<public>(Agent:agent)<transacts><decides>:void = external {}

        # Returns an array of agents that are currently of the team defined by this device.
        GetTeamMembers<public>()<reads>:[]agent = external {}

    # Customizable rift that allows `agent`s to move instantly between locations. You can use this to move players around your island, or create multi-island experiences with teleporters that take players from one island to another.
    teleporter_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when an `agent` enters this device.
        # Sends the `agent` that entered this device.
        EnterEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` emerges from this device.
        # Sends the `agent` that emerged from this device.
        TeleportedEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Teleport `Agent` to the target group using this device.
        Activate<public>(Agent:agent):void = external {}

        # When a link is activated, the current destination teleporter will be able to bring the `agent` back to this origin teleporter. Both origin and destination teleporters need to have this activated to work as expected.
        ActivateLinkToTarget<public>():void = external {}

        # Deactivates any currently active Link. The current destination teleporter will no longer be able to return the agent to this origin teleporter.
        DeactivateLinkToTarget<public>():void = external {}

        # Resets the currently selected destination teleporter, and selects an eligible destination. If the target is a *Teleporter Group*, this may be another randomly chosen `teleporter_device` from that group.
        ResetLinkToTarget<public>():void = external {}

        # Teleport `Agent` to this device.
        Teleport<public>(Agent:agent):void = external {}

    # Configures game modes where players can start or stop timers to advance gameplay objectives, such as Attack/Defend Bomb objectives.
    timed_objective_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when the objective begins.
        # Sends the `agent` that started the timer.
        BeganEvent<public>:listenable(agent) = external {}

        # Signaled when the objective ends.
        # Sends the `agent` that stopped the timer.
        EndedEvent<public>:listenable(agent) = external {}

        # Signaled when the objective is paused.
        # Sends the `agent` that paused the timer.
        PausedEvent<public>:listenable(agent) = external {}

        # Signaled when the objective is resumed.
        # Sends the `agent` that resumed the timer.
        ResumedEvent<public>:listenable(agent) = external {}

        # Signaled when the objective is restarted.
        # Sends the `agent` that restarted the timer.
        RestartedEvent<public>:listenable(agent) = external {}

        # Signaled when the objective is completed.
        # Sends the `agent` that started the timer or completed the timer by calling `Complete`.
        CompletedEvent<public>:listenable(agent) = external {}

        # Enables the objective for `Agent`.
        Enable<public>(Agent:agent):void = external {}

        # Disables the objective for `Agent`.
        Disable<public>(Agent:agent):void = external {}

        # Makes this device visible.
        Show<public>():void = external {}

        # Makes this device invisible.
        Hide<public>():void = external {}

        # Starts the objective with `Agent` acting as the user the interacted this device.
        Begin<public>(Agent:agent):void = external {}

        # Ends the objective with `Agent` acting as the user the interacted this device.
        End<public>(Agent:agent):void = external {}

        # Pauses the objective with `Agent` acting as the user the interacted this device.
        Pause<public>(Agent:agent):void = external {}

        # Resumes the objective with `Agent` acting as the user the interacted this device.
        Resume<public>(Agent:agent):void = external {}

        # Restarts the objective with `Agent` acting as the user the interacted this device.
        Restart<public>(Agent:agent):void = external {}

        # Completes the objective with `Agent` acting as the user the interacted this device.
        Complete<public>(Agent:agent):void = external {}

    # Provides a way to keep track of the time something has taken, either for scoreboard purposes, or to trigger actions. It can be configured in several ways, either acting as a countdown to an event that is triggered at the end, or as a stopwatch for an action that needs to be completed before a set time runs out.
    timer_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when the timer completes or ends with success.
        # Sends the `agent` that activated the timer, if any.
        SuccessEvent<public>:listenable(?agent) = external {}

        # Signaled when the timer completes or ends with failure.
        # Sends the `agent` that activated the timer, if any.
        FailureEvent<public>:listenable(?agent) = external {}

        # Signaled when the timer enters *Urgency Mode*.
        # Sends the `agent` that activated the timer, if any.
        StartUrgencyModeEvent<public>:listenable(?agent) = external {}

        # Enables this device for `Agent`.
        Enable<public>(Agent:agent):void = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device for `Agent`. While disabled this device will not receive signals.
        Disable<public>(Agent:agent):void = external {}

        # Disables this device. While disabled this device will not receive signals.
        Disable<public>():void = external {}

        # Resets the timer back to its base time and stops it for `Agent`.
        Reset<public>(Agent:agent):void = external {}

        # Resets the timer back to its base time and stops it.
        Reset<public>():void = external {}

        # Resets the timer back to its base time and stops it for all `agent`s.
        ResetForAll<public>(Agent:agent):void = external {}

        # Resets the timer back to its base time and stops it for all `agent`s.
        ResetForAll<public>():void = external {}

        # Starts the timer for `Agent`.
        Start<public>(Agent:agent):void = external {}

        # Starts the timer.
        Start<public>():void = external {}

        # Pauses the timer for `Agent`.
        Pause<public>(Agent:agent):void = external {}

        # Pauses the timer.
        Pause<public>():void = external {}

        # Resumes the timer for `Agent`.
        Resume<public>(Agent:agent):void = external {}

        # Resumes the timer.
        Resume<public>():void = external {}

        # Completes the timer for `Agent`.
        Complete<public>(Agent:agent):void = external {}

        # Completes the timer.
        Complete<public>():void = external {}

        # Starts the timer for all `agent`s.
        StartForAll<public>(Agent:agent):void = external {}

        # Starts the timer for all `agent`s.
        StartForAll<public>():void = external {}

        # Pauses the timer for all `agent`s.
        PauseForAll<public>(Agent:agent):void = external {}

        # Pauses the timer for all `agent`s.
        PauseForAll<public>():void = external {}

        # Resumes the timer for all `agent`s.
        ResumeForAll<public>(Agent:agent):void = external {}

        # Resumes the timer for all `agent`s.
        ResumeForAll<public>():void = external {}

        # Completes the timer for all `agent`s.
        CompleteForAll<public>(Agent:agent):void = external {}

        # Completes the timer for all `agent`s.
        CompleteForAll<public>():void = external {}

        # Saves this device's data for `Agent`.
        Save<public>(Agent:agent):void = external {}

        # Loads this device's saved data for `Agent`.
        Load<public>(Agent:agent):void = external {}

        # Clears this device's saved data for `Agent`.
        ClearPersistenceData<public>(Agent:agent):void = external {}

        # Clears this device's saved data for all `agent`s.
        ClearPersistenceDataForAll<public>(Agent:agent):void = external {}

        # Clears this device's saved data for all `agent`s.
        ClearPersistenceDataForAll<public>():void = external {}

        # Sets the remaining time (in seconds) on the timer, if active, on `Agent`.
        SetActiveDuration<public>(Time:float, Agent:agent):void = external {}

        # Sets the remaining time (in seconds) on the timer, if active. Use this function if the timer is set to use the same time for all `agent`'s.
        SetActiveDuration<public>(Time:float):void = external {}

        # Returns the remaining time (in seconds) on the timer for `Agent`.
        GetActiveDuration<public>(Agent:agent)<transacts>:float = external {}

        # Returns the remaining time (in seconds) on the timer if it is set to be global.
        GetActiveDuration<public>()<transacts>:float = external {}

        # Sets the lap time indicator for `Agent`.
        SetLapTime<public>(Agent:agent):void = external {}

        # Sets the lap time indicator for all `agent`s.
        SetLapTimeForAll<public>(Agent:agent):void = external {}

        # Sets the lap time indicator for all `agent`s.
        SetLapTimeForAll<public>():void = external {}

        # Sets the maximum duration of the timer (in seconds).
        SetMaxDuration<public>(Time:float):void = external {}

        # Returns the maximum duration of the timer (in seconds).
        GetMaxDuration<public>()<transacts>:float = external {}

        # Succeeds if this device is tracking timer state for each individual `agent` independently. Fails if state is being tracked globally for all `agent`'s.
        IsStatePerAgent<public>()<transacts><decides>:void = external {}

    # Allows creation and HUD tracking of custom objectives for `agent`s to complete.
    tracker_device<public> := class<concrete><final>(creative_device_base):
        # Resets the progress for `Agent` (and any `agent`s sharing progress).
        Reset<public>(Agent:agent):void = external {}

        # Signaled when the tracked value reaches `GetTarget` for an `agent`.
        # Sends the `agent` that reached `GetTarget` for their tracked value.
        CompleteEvent<public>:listenable(agent) = external {}

        # Assigns the device to `Agent` (and any `agent`s sharing progress).
        Assign<public>(Agent:agent):void = external {}

        # Assigns this device to all valid `agent`s.
        AssignToAll<public>():void = external {}

        # Removes this device from `Agent` (and any `agent`s sharing progress).
        Remove<public>(Agent:agent):void = external {}

        # Removes this device from all valid `agent`s.
        RemoveFromAll<public>():void = external {}

        # The objective immediately completes.
        Complete<public>(Agent:agent):void = external {}

        # Increases the tracked value by *Amount to Change on Received Signal* for `Agent`.
        Increment<public>(Agent:agent):void = external {}

        # Decrease the tracked value by *Amount to Change on Received Signal* for `Agent`.
        Decrement<public>(Agent:agent):void = external {}

        # Increases the target value for `Agent` by 1.
        IncreaseTargetValue<public>(Agent:agent):void = external {}

        # Decreases the target value for `Agent` by 1.
        DecreaseTargetValue<public>(Agent:agent):void = external {}

        # Saves tracked progress for `Agent`. Only valid if *Use Persistence* is set to *Use*.
        Save<public>(Agent:agent):void = external {}

        # Loads tracked progress for `Agent`. Only valid if *Use Persistence* is set to *Use*.
        Load<public>(Agent:agent):void = external {}

        # Loads tracked progress for all valid `agent`s. Only valid if *Use Persistence* is set to *Use*.
        LoadForAll<public>():void = external {}

        # Clears tracked progress for `Agent`. Only valid if *Use Persistence* is set to *Use*.
        ClearPersistence<public>(Agent:agent):void = external {}

        # Sets a description for the `tracker_device`, which is displayed if *Show on HUD* is enabled.
        # `Text` has a 64 character limit.
        SetDescriptionText<public>(Text:message):void = external {}

        # Sets the target value that must be achieved in order for `CompleteEvent` to trigger.
        # Clamped to `0 <= TargetValue <= 10000`.
        SetTarget<public>(TargetValue:int):void = external {}

        # Returns the target value that must be achieved in order for `CompleteEvent` to trigger.
        # Clamped to `0 <= GetTarget <= 10000`.
        GetTarget<public>()<transacts>:int = external {}

        # Sets the current tracked value for the device for all active players.
        SetValue<public>(Value:int):void = external {}

        # Sets the current tracked value for the device for the Team at the `TeamIndex`.
        # If *Sharing* is set to *Individual*, this will set the value for all team members.
        # If *Sharing* is set to *All*, this will set the value for all players.
        SetValue<public>(TeamIndex:int, Value:int):void = external {}

        # Sets the current tracked value for the device for a specific 'Agent'.
        # If *Sharing* is set to *Team*, this will set the value for their team.
        # If *Sharing* is set to *All*, this will set the value for everyone.
        SetValue<public>(Agent:agent, Value:int):void = external {}

        # Returns the current total tracked value for all players.
        GetValue<public>()<transacts>:int = external {}

        # Returns the current total tracked value for the team at `TeamIndex`.
        GetValue<public>(TeamIndex:int)<transacts>:int = external {}

        # Returns the current tracked value for `Agent`.
        GetValue<public>(Agent:agent)<transacts>:int = external {}

        # Sets the title for the `tracker_device`, which is displayed if *Show on HUD* is enabled.
        # `Text` has a 32 character limit.
        SetTitleText<public>(Text:message):void = external {}

        # Is true if `Agent` currently has the tracker active.
        IsActive<public>(Agent:agent)<transacts><decides>:void = external {}

        # Is true if `Agent` has reached the *TargetValue* for the tracker.
        HasReachedTarget<public>(Agent:agent)<transacts><decides>:void = external {}

        # Returns an array of agents that currently have this tracker active.
        GetActiveAgents<public>()<reads>:[]agent = external {}

    # A trap device that destroys the tile it's placed on when activated.
    trick_tile_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when the tile this device is attached to is removed. This may occur later than `TriggeredEvent` if *Activation Delay* is set on the device.
        # Sends the `agent` that activated this device.
        ActivatedEvent<public>:listenable(agent) = external {}

        # Signaled when this device is triggered.
        # Sends the `agent` that triggered this device.
        TriggeredEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device. While disabled this device will not react to incoming events.
        Disable<public>():void = external {}

        # Flips the device between `Enabled` and `Disable`.
        ToggleEnabled<public>():void = external {}

        # Restores the tile removed when this device was triggered.
        Reset<public>():void = external {}

        # Enables this device to trigger when an `agent` makes contact with the device.
        EnableAgentContactTrigger<public>():void = external {}

        # Disables this device from triggering when an `agent` makes contact with the device.
        DisableAgentContactTrigger<public>():void = external {}

        # Flips the device between `EnableAgentContactTrigger` and `DisableAgentContactTrigger.
        ToggleAgentContactTrigger<public>():void = external {}

        # Triggers the device, removing the associated tile.
        Trigger<public>():void = external {}

        # Teleports the `trick_tile_device` to the specified `Position` and `Rotation`. 
        # Only the trigger will teleport, the target buildings will not change. 
        TeleportTo<override>(Position:(/UnrealEngine.com/Temporary/SpatialMath:)vector3, Rotation:(/UnrealEngine.com/Temporary/SpatialMath:)rotation)<transacts><decides>:void = external {}

        # Teleports the `trick_tile_device` to the specified location defined by `Transform`, also applies rotation and scale accordingly. 
        # Only the trigger will teleport, the target buildings will not change. 
        TeleportTo<override>(Transform:(/UnrealEngine.com/Temporary/SpatialMath:)transform)<transacts><decides>:void = external {}

        # Moves the `trick_tile_device` to the specified `Position` and `Rotation` over the specified time, in seconds. 
        # Only the trigger will move, the target buildings will not change. 
        MoveTo<override>(Position:(/UnrealEngine.com/Temporary/SpatialMath:)vector3, Rotation:(/UnrealEngine.com/Temporary/SpatialMath:)rotation, OverTime:float)<suspends>:move_to_result = external {}

        # Moves the `trick_tile_device` to the specified `Transform` over the specified time, in seconds. 
        # Only the trigger will move, the target buildings will not change. 
        MoveTo<override>(Transform:(/UnrealEngine.com/Temporary/SpatialMath:)transform, OverTime:float)<suspends>:move_to_result = external {}

    # Base class for various specialized trigger devices. See also: * `trigger_device` * `perception_trigger_device` * `attribute_evaluator_device`
    trigger_base_device<public> := class<abstract><epic_internal>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Resets the number of times this device has been activated. This will set `GetTriggerCountRemaining` back to `0`
        Reset<public>():void = external {}

        # Sets the maximum amount of times this device can trigger.
        #  * `0` can be used to indicate no limit on trigger count.
        #  * `MaxCount` is clamped between [0,20].
        SetMaxTriggerCount<public>(MaxCount:int):void = external {}

        # Gets the maximum amount of times this device can trigger.
        #  * `0` indicates no limit on trigger count.
        GetMaxTriggerCount<public>()<transacts>:int = external {}

        # Returns the number of times that this device can still be triggered before hitting `GetMaxTriggerCount`.
        # Returns `0` if `GetMaxTriggerCount` is unlimited.
        GetTriggerCountRemaining<public>()<transacts>:int = external {}

        # Sets the time (in seconds) after triggering, before the device can be triggered again (if `MaxTrigger` count allows).
        SetResetDelay<public>(Time:float):void = external {}

        # Gets the time (in seconds) before the device can be triggered again (if `MaxTrigger` count allows).
        GetResetDelay<public>()<transacts>:float = external {}

        # Sets the time (in seconds) which must pass after triggering, before this device informs other external devices that it has been triggered.
        SetTransmitDelay<public>(Time:float):void = external {}

        # Gets the time (in seconds) which must pass after triggering, before this device informs other external devices that it has been triggered.
        GetTransmitDelay<public>()<transacts>:float = external {}

    # Used to relay events to other linked devices.
    trigger_device<public> := class<concrete><final>(trigger_base_device):
        # Signaled when an `agent` triggers this device.
        # Sends the `agent` that used this device. Returns `false` if no `agent` triggered the action (ex: it was triggered through code).
        TriggeredEvent<public>:listenable(?agent) = external {}

        # Triggers this device with `Agent` being passed as the `agent` that triggered the action. Use an `agent` reference when this device is setup to require one (for instance, you want to trigger the device only with a particular `agent`.
        Trigger<public>(Agent:agent):void = external {}

        # Triggers this device, causing it to activate its `TriggeredEvent` event.
        Trigger<public>():void = external {}

    # Holds and spawns items, with an optional cost for each item. Can hold up to three items, and `agent`s can cycle between these by hitting the machine with their pickaxe.
    vending_machine_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when an item is spawned from this device.
        # Sends the `agent` that used this device.
        ItemSpawnedEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Cycles the screen to show the next item.
        CycleToNextItem<public>():void = external {}

        # Spawns an item.
        SpawnItem<public>():void = external {}

    # Used to trigger a visual effect (a glow or an outline) when `agent`s pick it up.
    visual_effect_powerup_device<public> := class<concrete><final>(powerup_device):

    using {/Verse.org/Assets}
    using {/Verse.org/Colors}
    # Used to spawn customizable boxes containing vehicle mods. Hit a box with a vehicle to apply the mod.
    vehicle_mod_box_spawner_device<public> := class<concrete><final>(creative_device_base, enableable):
        # Enable the device and start the spawn timer. While enabled, it can spawn vehicle mod boxes, and those boxes can be interacted with.
        Enable<override>():void = external {}

        # Disable the device and clear any active spawn timer. While disabled, it will be hidden and vehicle mod boxes will not spawn.
        Disable<override>():void = external {}

        # Succeeds if the object is enabled, fails if it's disabled.
        IsEnabled<override>()<transacts><decides>:void = external {}

        # Contains the custom settings for *Overall Visual Style*, which can be individually set and returned.
        var<private> ModBoxOverallSettings<public>:vehicle_mod_box_settings = external {}

        # An array containing the custom settings for each entry in the device's custom list.
        #  * Index `0` applies to Custom List Mod 1, index `1` applies to Custom List Mod 2, and so on.
        #  * The contents of each entry in the array can be individually set and returned.
        #  * If *Possible Mods* is not set to `Custom List`, this array will be empty.
        var<private> ModBoxCustomListSettings<public>:[]vehicle_mod_box_settings = external {}

        # Triggers whenever the mod box spawns.
        #  * Returns the index `int` of the chosen mod in the device's list, starting with `0` for Custom List Mod 1.
        # If *Possible Mods* is not set to `Custom List`, `int` will be `-1`.
        BoxSpawnedEvent<public>:listenable(int) = external {}

        # Triggers whenever the mod box is destroyed regardless of whether a mod was applied.
        # Returns tuple:
        #  * 0: The `agent` that triggered this event, if applicable.
        #  * 1: The index `int` of the chosen mod in the device's list, starting with `0` for Custom List Mod 1.
        # If *Possible Mods* is not set to `Custom List`, `int` will be `-1`.
        BoxDestroyedEvent<public>:listenable(tuple(?agent, int)) = external {}

        # Triggers whenever the mod is applied to a vehicle.
        # Returns tuple:
        #  * 0: The `agent` driving the vehicle, if applicable.
        #  * 1: The `fort_vehicle` the mod applied to.
        #  * 2: The index `int` of the chosen mod in the device's list, starting with `0` for Custom List Mod 1.
        # If *Possible Mods* is not set to `Custom List`, `int` will be `-1`.
        ModAppliedEvent<public>:listenable(tuple(?agent, fort_vehicle, int)) = external {}

        # Triggers whenever the mod fails to apply to a vehicle.
        # Returns tuple:
        #  * 0: The `agent` driving the vehicle, if applicable.
        #  * 1: The `fort_vehicle` the mod failed to apply to.
        #  * 2: The index `int` of the chosen mod in the device's list, starting with `0` for Custom List Mod 1.
        # If *Possible Mods* is not set to `Custom List`, `int` will be `-1`.
        ModApplyFailedEvent<public>:listenable(tuple(?agent, fort_vehicle, int)) = external {}

        # Triggers whenever a `No Mod` entry from a custom list tries to apply to a vehicle.
        # Returns tuple:
        #  * 0: The `agent` driving the vehicle, if applicable.
        #  * 1: The `fort_vehicle` that broke the box.
        #  * 2: The index `int` of the specific `No Mod` entry in the device's list, starting with `0` for Custom List Mod 1.
        NoModEvent<public>:listenable(tuple(?agent, fort_vehicle, int)) = external {}

        # If ModAppliedEvent is overridden (see `SetOverrideModApplyEvent`), breaking a mod box triggers this instead of `ModAppliedEvent`, `ModApplyFailEvent`, or `NoModEvent`.
        # Returns tuple:
        #  * 0: The `agent` driving the vehicle, if applicable.
        #  * 1: The `fort_vehicle` the mod would attempt to apply to.
        #  * 2: The index `int` of the chosen mod in the device's list, starting with `0` for Custom List Mod 1.
        # If *Possible Mods* is not set to `Custom List`, `int` will be `-1`.
        ModApplyOverriddenEvent<public>:listenable(tuple(?agent, fort_vehicle, int)) = external {}

        # Spawn a mod box if the device is enabled. If one is already spawned, spawn a new one without triggering `BoxDestroyEvent`.
        SpawnBox<public>():void = external {}

        # Despawn the box and clear the spawn timer without triggering `BoxDestroyEvent`. The respawn hologram will be visible, but will not progress without `StartSpawnTimer`. The device must be enabled.
        DespawnBox<public>():void = external {}

        # Check if this device currently has a spawned mod box.
        IsSpawned<public>()<transacts><decides>:void = external {}

        # Returns the number of times the device has spawned a mod box.
        GetSpawnCount<public>()<transacts>:int = external {}

        # Despawn the box if necessary without triggering `BoxDestroyEvent`, then start the spawn timer. The device must be enabled.
        StartSpawnTimer<public>():void = external {}

        # Returns the total length of the initial spawn timer in seconds, regardless of the timer's current state.
        GetInitialSpawnTimerLength<public>()<transacts>:float = external {}

        # Set the remaining time (in seconds) on the initial spawn timer (clamped to a minimum of `0.0`). The device must be enabled.
        #  * Even if the initial spawn timer is not currently active, this sets its length.
        SetInitialSpawnTimer<public>(Time:float):void = external {}

        # Returns the total length of the respawn timer in seconds, regardless of the timer's current state.
        GetRespawnTimerLength<public>()<transacts>:float = external {}

        # Set the remaining time (in seconds) on the respawn timer (clamped to a minimum of `0.0`). The device must be enabled.
        #  * This also sets the length of this device's future respawn timers.
        SetRespawnTimer<public>(Time:float):void = external {}

        # Returns the remaining time (in seconds) of the device's active spawn timer.
        #  * If there is no active timer, return `0.0`.
        GetActiveTimerRemaining<public>()<transacts>:float = external {}

        # Returns the current index `int` in the device's list of mod boxes. This value is set whenever the device spawns a new mod box.
        #  * The device's list starts with `0` for Custom List Mod 1.
        #  * If *Possible Mods* is not set to `Custom List` or the device has never spawned a mod box, `int` will be `-1`.
        GetCurrentIndex<public>()<transacts>:int = external {}

        # Succeeds if `Index` is a valid entry in the device's list. Fails if `Index` is an invalid entry or *Possible Mods* is not set to `Custom List`.
        IsValidIndex<public>(Index:int)<transacts><decides>:void = external {}

        # Spawn the same mod box this device spawned before the current one. If a mod box is already spawned, despawn that first without triggering `BoxDestroyEvent`.
        #  * Does nothing if this device is disabled or has not spawned more than one mod box.
        SpawnLastChosenMod<public>():void = external {}

        # If the mod box is spawned, respawn it as the previous valid entry in the list without triggering `BoxDestroyEvent`.
        #  * If the mod box is not spawned, that will be the next mod box.
        #  * The list wraps, which means the last index is considered directly before the first index.
        #  * Does nothing if this device is disabled or *Possible Mods* is not set to `Custom List`.
        CycleToPreviousValidIndex<public>():void = external {}

        # If the mod box is spawned, respawn it as the next valid entry in the list without triggering `BoxDestroyEvent`.
        #  * If the mod box is not spawned, that will be the next mod box.
        #  * The list wraps, which means the first index is considered directly after the last index.
        #  * Does nothing if this device is disabled or *Possible Mods* is not set to `Custom List`.
        CycleToNextValidIndex<public>():void = external {}

        # Set the next mod box to spawn to be the one at `Index` in the device's list.
        #  * The first index in the list is `0` for Custom List Mod 1.
        #  * Does nothing if this device is disabled, *Possible Mods* is not set to `Custom List`, or `Index` is invalid.
        SetNextModIndex<public>(Index:int):void = external {}

        # Spawn the mod box at `Index` in the device's list. If a mod box is already spawned, despawn that first without triggering `BoxDestroyEvent`.
        #  * The first index in the list is `0` for Custom List Mod 1.
        #  * Does nothing if this device is disabled, *Possible Mods* is not set to `Custom List`, or `Index` is invalid.
        SpawnModBoxByIndex<public>(Index:int):void = external {}

        # Apply the mod at `Index` in the device's list to the vehicle that `Agent` is riding.
        #  * The first index in the list is `0` for Custom List Mod 1.
        #  * Triggers `ModAppliedEvent`, `ModApplyFailEvent`, and `NoModEvent` as appropriate, regardless of `SetOverrideModApplyEvent`.
        #  * Fails if this device is disabled, *Possible Mods* is not set to `Custom List`, `Index` or `Agent` is invalid, or `Agent` is not riding a vehicle.
        TryApplyModByAgent<public>(Index:int, Agent:agent)<transacts><decides>:void = external {}

        # Apply the mod at `Index` in the device's list to `Vehicle`.
        #  * The first index in the list is `0` for Custom List Mod 1.
        #  * Triggers `ModAppliedEvent`, `ModApplyFailEvent`, and `NoModEvent` as appropriate, regardless of `SetOverrideModApplyEvent`.
        #  * Fails if this device is disabled, *Possible Mods* is not set to `Custom List`, or `Index` or `Vehicle` is invalid.
        #  * `Vehicle` must be an actual `fort_vehicle`. A vehicle spawner device will not be valid.
        #  * `ModApplyOverriddenEvent`, `ModAppliedEvent`, `ModApplyFailEvent`, and `NoModEvent` are a few ways to get a `fort_vehicle` value.
        TryApplyModByVehicle<public>(Index:int, Vehicle:fort_vehicle)<transacts><decides>:void = external {}

        # If `Override` is `true`, destroying the mod box with a vehicle will trigger `ModApplyOverriddenEvent` and a mod will not be applied.
        #  * In this case, destroying the mod box will not trigger `ModAppliedEvent`, `ModApplyFailEvent`, or `NoModEvent`.
        #  * The values returned by `ModApplyOverriddenEvent` can be used for `TryApplyModByAgent` and `TryApplyModByVehicle`, so the event can be handled manually.
        #  * See `ModApplyOverriddenEvent` for more information.
        SetOverrideModApplyEvent<public>(Override:logic):void = external {}

        # Set this device's mod boxes to use the name `Name`
        #  * The displayed name will not exceed 50 characters.
        #  * The following characters are disallowed: {, }, <, >, \, /, and |.
        SetCustomModBoxName<public>(Name:message):void = external {}

        # Reset the names of this device's mod boxes, if the name has been customized.
        ResetModBoxName<public>():void = external {}

        # Set the `Text` of the tooltip that appears when a player gets close to the mod box without a vehicle.
        #  * The displayed text will not exceed 50 characters.
        #  * The following characters are disallowed: {, }, <, >, \, /, and |.
        SetCustomPlayerTooltipText<public>(Text:message):void = external {}

        # Reset the text of the tooltip that appears when a player gets close to the mod box without a vehicle, if it's been customized.
        ResetPlayerTooltip<public>():void = external {}

    # Settings for vehicle mod boxes, used with vehicle_mod_box_spawner_device.
    vehicle_mod_box_settings<public> := class<final><public>:
        # A custom paint color on the mod box.
        #  * If the associated mod box uses default visuals, it becomes a blank box with that paint color.
        #  * Setting this value to `false` removes any custom paint color.
        var PaintColor<public>:?color = external {}

        # A custom icon on the mod box. Attempting to return this value will fail unless it's your own texture.
        #  * If the associated mod box uses default visuals, it becomes a blank box with that icon.
        #  * Setting this value to `false` removes any custom icon.
        var Icon<public>:?texture = external {}

        # The custom color of the icon on the mod box. This value has no effect if this entry's icon is not set.
        #  * Setting this value to `false` removes any custom icon color.
        var IconColor<public>:?color = external {}

        # The scale multiplier of the icon on the mod box, clamped between `0.1` and `2.0`. This value has no effect if this entry's icon is not set.
        #  * This value defaults to `1.0`.
        var IconScale<public>:float = external {}

        # Reset visual settings and make the associated mod box use its default non-customized visuals.
        #  * Fails if this is a custom list entry set to `Empty Slot`.
        ApplyDefaultVisuals<public>()<transacts><decides>:void = external {}

    # Specialized `vehicle_spawner_device` that allows an ATK (all terrain kart) to be configured and spawned.
    vehicle_spawner_atk_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Specialized `vehicle_spawner_device` that allows a Baller vehicle to be configured and spawned.
    vehicle_spawner_baller_device<public> := class<concrete><final>(vehicle_spawner_device):
        # Signaled when the vehicle runs out of energy.
        OutOfEnergyEvent<public>:listenable(tuple()) = external {}

        # Refills the vehicle's energy.
        RefillEnergy<public>():void = external {}

    # Specialized `vehicle_spawner_device` that allows a pickup truck to be configured and spawned.
    vehicle_spawner_pickup_truck_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Specialized `vehicle_spawner_device` that allows a Stormwing biplane to be configured and spawned.
    vehicle_spawner_biplane_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Specialized `vehicle_spawner_device` that allows a boat to be configured and spawned.
    vehicle_spawner_boat_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Specialized `vehicle_spawner_device` that allows a cannon to be configured and spawned.
    vehicle_spawner_cannon_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Specialized `vehicle_spawner_device` that allows a Driftboard to be configured and spawned.
    vehicle_spawner_driftboard_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Specialized `vehicle_spawner_device` that allows a Mudflap semi truck to be configured and spawned.
    vehicle_spawner_big_rig_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Specialized `vehicle_spawner_device` that allows a Prevalent sedan to be configured and spawned.
    vehicle_spawner_sedan_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Specialized `vehicle_spawner_device` that allows a Quadcrasher to be configured and spawned.
    vehicle_spawner_quadcrasher_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Specialized `vehicle_spawner_device` that allows a shopping cart to be configured and spawned.
    vehicle_spawner_shopping_cart_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Specialized `vehicle_spawner_device` that allows a surfboard to be configured and spawned.
    vehicle_spawner_surfboard_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Specialized `vehicle_spawner_device` that allows a taxi to be configured and spawned.
    vehicle_spawner_taxi_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Base class for various specialized vehicle spawners which allow specific vehicle types to be spawned and configured with specialized options.
    vehicle_spawner_device<public> := class<abstract><epic_internal>(creative_device_base):
        # Signaled when an `agent` enters the vehicle.
        # Sends the `agent` that entered the vehicle.
        AgentEntersVehicleEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` exits the vehicle.
        # Sends the `agent` that exited the vehicle.
        AgentExitsVehicleEvent<public>:listenable(agent) = external {}

        # Signaled when a vehicle is spawned or respawned by this device.
        # Sends the fort_vehicle who was spawned.
        SpawnedEvent<public>:listenable(fort_vehicle) = external {}

        # Signaled when a vehicle is spawned or respawned by this device.
        # Deprecated, use SpawnedEvent instead.
        VehicleSpawnedEvent<public>:listenable(tuple()) = external {}

        # Signaled when a vehicle is destroyed.
        # Deprecated, use DestroyedEvent instead.
        VehicleDestroyedEvent<public>:listenable(tuple()) = external {}

        # Signaled when a vehicle is destroyed.
        DestroyedEvent<public>:listenable(tuple()) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Sets `agent` as the vehicle's driver.
        AssignDriver<public>(Agent:agent):void = external {}

        # Destroys the vehicle if it exists.
        DestroyVehicle<public>():void = external {}

        # Spawns a new vehicle. The previous vehicle will be destroyed before a new vehicle spawns.
        RespawnVehicle<public>():void = external {}

    # Specialized `vehicle_spawner_device` that allows a Whiplash sports car to be configured and spawned.
    vehicle_spawner_sports_car_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Used to configure a single interactive mannequin, that can visualize characters, clothing, and perform emotes.
    character_device<public> := class<concrete>(creative_device_base):
        # Signaled when an `agent` interacts with this device.
        # Sends the `agent` that interacted with this device.
        InteractedWithEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Shows this device.
        Show<public>():void = external {}

        # Hides this device.
        Hide<public>():void = external {}

        # Plays an emote on the character created by this device.
        PlayEmote<public>():void = external {}

    # Allows the item shop to be opened when activated
    item_shop_device<public> := class<concrete><final>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

    # Used to allow players to select their *Class* from a *Class Selector UI*.
    class_selector_ui_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when an `agent` selects a class.
        # Sends the `agent` that selected a class.
        ClassSelectedEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` changes a class.
        # Sends the `agent` that selected a class and the `int` class that the player has changed to.
        ClassChangedEvent<public>:listenable(tuple(agent, int)) = external {}

        # Signaled when an `agent` closes the UI.
        # Sends the `agent` that closed the UI.
        UIClosedEvent<public>:listenable(agent) = external {}

        # Signaled when the UI is opened.
        # Sends the `agent` that is responsible for opening the UI.
        UIOpenedEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Show the *Class Selector UI* to `Agent`.
        Show<public>(Agent:agent):void = external {}

    # Used to regenerate an `agent`'s health and/or shields.
    health_powerup_device<public> := class<concrete><final>(powerup_device):
        # Sets the *Magnitude* for this powerup, clamped to the Min and Max defined in the device.
        # Will not apply to any currently applied effects.
        # For the Health Powerup, this is the amount of health or shield that the powerup will add or remove,
        SetMagnitude<public>(Magnitude:float):void = external {}

        # Returns the current *Magnitude* for the powerup.
        # For the Health Powerup, this is the amount of health or shield that the powerup will add or remove,
        GetMagnitude<public>()<transacts>:float = external {}

    # Physics boulder that can be dislodged and damage `agent`s, vehicles, creatures, and structures.
    physics_boulder_device<public> := class<concrete><final>(physics_object_base_device):
        # Signaled when the balanced boulder is spawned on the base.
        BalancedBoulderSpawnedEvent<public>:listenable(tuple()) = external {}

        # Signaled when the balanced boulder is destroyed.
        BalancedBoulderDestroyedEvent<public>:listenable(tuple()) = external {}

        # Signaled when the base of the boulder is destroyed.
        BaseDestroyedEvent<public>:listenable(tuple()) = external {}

        # Signaled when the rolling boulder is destroyed.
        RollingBoulderDestroyedEvent<public>:listenable(tuple()) = external {}

        # Destroys the boulder's base.
        DestroyBase<public>():void = external {}

        # Releases the boulder sitting on the base, if there is one.
        ReleaseRollingBoulder<public>():void = external {}

        # Destroys the current rolling boulder.
        DestroyRollingBoulder<public>():void = external {}

    # Base class for various physics-based gameplay elements (e.g. boulders/trees).
    physics_object_base_device<public> := class<abstract><epic_internal>(prop_spawner_base_device):

    # Physics tree that can be chopped down, and damage players, vehicles, creatures, and structures.
    physics_tree_device<public> := class<concrete><final>(physics_object_base_device):
        # Signaled when a tree is spawned.
        TreeSpawnedEvent<public>:listenable(tuple()) = external {}

        # Signaled when the log created by a tree is destroyed.
        LogDestroyedEvent<public>:listenable(tuple()) = external {}

        # Signaled when the stump created by a tree is destroyed.
        StumpDestroyedEvent<public>:listenable(tuple()) = external {}

        # Signaled when a tree has taken enough damage to be knocked down.
        TreeKnockedDownEvent<public>:listenable(tuple()) = external {}

        # Releases the log from the tree, if there is one.
        ReleaseLog<public>():void = external {}

        # Destroys the current log.
        DestroyLog<public>():void = external {}

        # Destroys the current stump.
        DestroyStump<public>():void = external {}

    # Used to create HUD text boxes that give players information, and allows responses to be customized to player choices.
    popup_dialog_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when *Button <Index>* on this device is pushed by an `agent`.
        # Sends the `agent` that pushed the button.
        # Sends the `int` index of the button that was clicked.
        RespondingButtonEvent<public>:listenable(tuple(agent, int)) = external {}

        # Signaled when this device is shown to an `agent`.
        # Sends the `agent` looking at the popup.
        ShownEvent<public>:listenable(agent) = external {}

        # Signaled when this device is dismissed by an `agent`.
        # Sends the `agent` who dismissed the popup.
        DismissedEvent<public>:listenable(agent) = external {}

        # Signaled when this device times out while an `agent` is looking at it.
        # Sends the `agent` who was looking at the popup.
        TimeOutEvent<public>:listenable(agent) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Shows the popup to `Agent`.
        Show<public>(Agent:agent):void = external {}

        # Shows the popup to all `agent`s in the experience.
        Show<public>():void = external {}

        # Hides the popup from `Agent`.
        Hide<public>(Agent:agent):void = external {}

        # Hides the popup from all `agent`s in the experience.
        Hide<public>():void = external {}

        # Sets the number of buttons this popup has.
        # Button Count is not updated on active Popups.
        SetButtonCount<public>(Count:int):void = external {}

        # Returns the *Button Text* for this popup at a specified index.
        GetButtonText<public>(Index:int)<transacts>:string = external {}

        # Sets the *Button Text* for a button at a specific index on this popup.
        #  * `Text` should be no more than `24` characters.
        #  * If `Text` is empty the button will show *OK* instead.
        #  * Button 1 uses `Index` 0.
        SetButtonText<public>(Text:message, Index:int):void = external {}

        # Sets the *Description* text for this popup. `Text` should be no more than `350` characters.
        SetDescriptionText<public>(Text:message):void = external {}

        # Returns the *Description* text for this popup.
        GetDescriptionText<public>()<transacts>:string = external {}

        # Sets the *Title* text for this popup. `Text` should be no more than `32` characters.
        SetTitleText<public>(Text:message):void = external {}

        # Returns the *Title* text for this popup.
        GetTitleText<public>()<transacts>:string = external {}

    # A device that generates a scoreboard stat that can be used by the game to determine Game End and Round End conditions.
    # The stat can apply to individual players, teams, or everyone in the match. It can also generate a Level that will increment by 1 each time Value reaches *Max Value*, resetting Value to 0 when Level increments.
    stat_creator_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when the stat changes Value.
        # Sends a tuple of the instigating `agent` (or `false` if none) and the new Value. Value increases to a maximum of *Max Value*. If the stat has not yet reached *Max Level* then Value will reset to 0 and Level will increment by 1.
        ValueChangedEvent<public>:listenable(tuple(?agent, int)) = external {}

        # Signaled when the stat changes Level.
        # Sends a tuple of the instigating `agent` (or `false` if none) and the new Level.
        LevelChangedEvent<public>:listenable(tuple(?agent, int)) = external {}

        # Signaled when an `agent` reaches the maximum stat value. This occurs when the stat reaches *Max Value* if the device has no levels, or *Max Level* if that option is set.
        # Sends the `agent` that caused the stat event that changed the level and the new level as an `int` .
        MaximumReachedEvent<public>:listenable(tuple(?agent, int)) = external {}

        # Returns stat Value for `Agent`.
        # Succeeds if *Scope* is set to *Player* and `Agent` passes the requirement checks on the device.
        GetValue<public>(Agent:agent)<transacts><decides>:int = external {}

        # Returns stat Value for `Team`.
        # Succeeds if *Scope* is set to *Team* and `Team` passes the requirement checks on the device.
        GetValue<public>(Team:team)<transacts><decides>:int = external {}

        # Returns stat Value for the match.
        # Succeeds if *Scope* is set to *Match*.
        GetValueForMatch<public>()<transacts><decides>:int = external {}

        # Updates stat Value for `Agent`.
        # Succeeds if *Scope* is set to *Agent* and `Agent` passes the requirement checks on the device.
        # If the stat does not have levels, `Value` is clamped between 0 and *Max Value*.
        # If the stat does have levels, the Value is not clamped and setting a value below 0 or above *Max Value* will
        # decrement or increment the level appropriately. e.g. if *Number of Levels* is 10, *Max Value is 100* 
        # and you are currently level 0, a `Value` of 550 will set you to Level 5 and Value 50.
        SetValue<public>(Agent:agent, Value:int)<transacts><decides>:void = external {}

        # Updates stat Value for `Team`.
        # Succeeds if *Scope* is set to *Team* and `Team` passes the requirement checks on the device.
        # If the stat does not have levels, `Value` is clamped between 0 and *Max Value*.
        # If the stat does have levels, the Value is not clamped and setting a value below 0 or above *Max Value* will
        # decrement or increment the level appropriately. e.g. if *Number of Levels* is 10, *Max Value is 100* 
        # and you are currently level 0, a `Value` of 550 will set you to Level 5 and Value 50.
        SetValue<public>(Team:team, Value:int)<transacts><decides>:void = external {}

        # Updates stat Value for the match.
        # Succeeds if *Scope* is set to *Match*.
        # If the stat does not have levels, `Value` is clamped between 0 and *Max Value*.
        # If the stat does have levels, the Value is not clamped and setting a value below 0 or above *Max Value* will
        # decrement or increment the level appropriately. e.g. if *Number of Levels* is 10, *Max Value is 100* 
        # and you are currently level 0, a `Value` of 550 will set you to Level 5 and Value 50.
        SetValueForMatch<public>(Value:int)<transacts><decides>:void = external {}

        # Returns stat Level for `Agent`.
        # Succeeds if *Number of Levels* is set to greater than 0, *Scope* is set to *Player* 
        # and `Agent` passes the requirement checks on the device.
        GetLevel<public>(Agent:agent)<transacts><decides>:int = external {}

        # Returns stat Level for `Team`.
        # Succeeds if *Number of Levels* is set to greater than 0, *Scope* is set to *Team* 
        # and the `Team` passes the requirement checks on the device.
        GetLevel<public>(Team:team)<transacts><decides>:int = external {}

        # Returns stat Level for the match.
        # Succeeds if *Number of Levels* is set to greater than 0 and *Scope* is set to *Match*.
        GetLevelForMatch<public>()<transacts><decides>:int = external {}

        # Updates stat Level for `Agent`.
        # Succeeds if *Number of Levels* is set to greater than 0 and *Scope* is set to *Agent*.
        # Altering stat Level will set `Agent`'s stat Value to 0.
        SetLevel<public>(Agent:agent, Level:int)<transacts><decides>:void = external {}

        # Updates stat Level for `Team`.
        # Succeeds if *Number of Levels* is set to greater than 0 and *Scope* is set to *Team*.
        # Altering stat Level will set `Team`'s stat Value to 0.
        SetLevel<public>(Team:team, Level:int)<transacts><decides>:void = external {}

        # Updates stat Level for the match.
        # Succeeds if *Number of Levels* is set to greater than 0 and *Scope* is set to *Match* .
        # Altering stat Level will set the Match's stat Value to 0.
        SetLevelForMatch<public>(Level:int)<transacts><decides>:void = external {}

        # Returns the name for the stat that is generated by this Stat Creator.
        GetName<public>():string = external {}

    # Used to increase or decrease a stat for an `agent`.
    stat_powerup_device<public> := class<concrete><final>(powerup_device):
        # Sets the *Magnitude* for this powerup, clamped to the Min and Max defined in the device.
        # Will not apply to any currently applied effects.
        # For the Stat Powerup, this is the value of the stat that the powerup will add or remove to a player that picks up the powerup.
        SetMagnitude<public>(Magnitude:float):void = external {}

        # Returns the current *Magnitude* for the powerup.
        # For the Stat Powerup, this is the value of the stat that the powerup will add or remove to a player that picks up the powerup.
        GetMagnitude<public>()<transacts>:float = external {}

    # Use to create a sprite that players can trade in a weapon for a random legendary weapon or a custom item list.
    earth_sprite_device<public> := class<concrete><final>(creative_device_base, enableable):
        # Enable the device, and resets all trade counts the Sprite is tracking.
        Enable<override>():void = external {}

        # Disable the device.
        Disable<override>():void = external {}

        # Succeeds if the device is enabled, fails if it's disabled.
        IsEnabled<override>()<transacts><decides>:void = external {}

        # Triggers when a player gives the Earth Sprite a weapon.
        # Sends the triggering `agent`.
        WeaponConsumedEvent<public>:listenable(agent) = external {}

        # Triggers when the grant timer has completed.
        # Sends the triggering `agent`.
        GrantTimerCompletedEvent<public>:listenable(?agent) = external {}

        # Enable the device’s ability to grant items.
        EnableItemGranting<public>():void = external {}

        # Disable the device’s ability to grant items. Can still interact and consume weapons.
        DisableItemGranting<public>():void = external {}

        # Makes the Earth Sprite invisible.
        Hide<public>():void = external {}

        # Makes the Earth Sprite visible.
        Show<public>():void = external {}

        # Allows a `agent` to trade, and will reset the `agent`'s trade count for this Sprite.
        EnableTradingForPlayer<public>(Agent:agent):void = external {}

        # Prevents the `agent` from trading.
        DisableTradingForPlayer<public>(Agent:agent):void = external {}

    # Use to create a flaming hoop that accelerates and applies nitro to players and vehicles.
    nitro_hoop_device<public> := class<concrete><final>(creative_device_base):
        # Triggers when a player triggers the device.
        # Sends the triggering `agent`.
        PlayerTriggeredEvent<public>:listenable(agent) = external {}

        # Triggers when a vehicle triggers the device.
        # Sends the driver as the triggering `agent`. If the vehicle has no driver then `false` is returned.
        VehicleTriggeredEvent<public>:listenable(?agent) = external {}

        # Triggers when the device enters the cooldown state, becoming disabled.
        # This is not triggered by manually calling `Disable`.
        CooldownStartEvent<public>:listenable(tuple()) = external {}

        # Triggers when the device becomes enabled after being disabled, potentially through the cooldown state.
        EnabledEvent<public>:listenable(tuple()) = external {}

        # Enable the device. If the device is currently in the cooldown state, this also ends the cooldown state.
        Enable<public>():void = external {}

        # Disable the device. This causes the device to enter the cooldown state until the device is re-enabled by `Enable`, `DisallowCooldown`, or `Enable on Phase`.
        Disable<public>():void = external {}

        # Allow the device to enter the cooldown state after a player or vehicle triggers it.
        AllowCooldown<public>():void = external {}

        # Prevent the device from entering the cooldown state after use. Until `AllowCooldown` is called, triggering the device will not begin cooldown.
        # If the device is currently in the cooldown state, this ends the cooldown state and re-enables the device.
        DisallowCooldown<public>():void = external {}

        # Enter the cooldown state for `Seconds` seconds. If the device is already in the cooldown state, re-enable it after `Seconds` seconds.
        # `Seconds` is clamped to a minimum of `1.0`.
        StartCooldown<public>(Seconds:float):void = external {}

        # Succeeds if the device is currently enabled.
        # The device is not enabled if it is in the cooldown state, or has otherwise been manually disabled.
        IsEnabled<public>()<transacts><decides>:void = external {}

        # Return the default duration of the cooldown state in seconds.
        GetDefaultCooldownDuration<public>():float = external {}

        # Set the default duration of the cooldown state to `Seconds`.
        #  * `Seconds` is clamped between `1.0` and `60.0`.
        SetDefaultCooldownDuration<public>(Seconds:float):void = external {}

        # Return the duration of the cooldown delay in seconds. This is the delay between triggering a cooldown and entering the cooldown phase.
        GetCooldownDelay<public>():float = external {}

        # Set the duration of the cooldown delay to `Seconds`. This is the delay between triggering a cooldown and entering the cooldown phase.
        #  * The cooldown delay does not apply to cooldowns triggered by *StartCooldown* or *Disable*.
        #  * `Seconds` is clamped between `0.0` and `5.0`.
        SetCooldownDelay<public>(Seconds:float):void = external {}

    # Spawns a lightweight vehicle made for defying gravity with its rocket boosting, jumping, and aerial maneuverability capabilities.
    vehicle_spawner_octane_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Specialized `vehicle_spawner_device` that allows an armored battle bus to be configured and spawned.
    vehicle_spawner_armored_battle_bus_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Specialized `vehicle_spawner_device` that allows a dirtbike to be configured and spawned.
    vehicle_spawner_dirtbike_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Specialized `vehicle_spawner_device` that allows a helicopter to be configured and spawned.
    vehicle_spawner_helicopter_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Specialized `vehicle_spawner_device` that allows a UFO to be configured and spawned.
    vehicle_spawner_ufo_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Specialized `vehicle_spawner_device` that allows a Nitro Drifter sedan to be configured and spawned.
    vehicle_spawner_nitro_drifter_sedan_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Specialized `vehicle_spawner_device` that allows a siege cannon to be configured and spawned.
    vehicle_spawner_siege_cannon_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Specialized `vehicle_spawner_device` that allows an anti-vehicle turret to be configured and spawned.
    vehicle_spawner_heavy_turret_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Specialized `vehicle_spawner_device` that allows a sportbike to be configured and spawned.
    vehicle_spawner_sportbike_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Specialized `vehicle_spawner_device` that allows a valet SUV to be configured and spawned.
    vehicle_spawner_valet_suv_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Specialized `vehicle_spawner_device` that allows a tank to be configured and spawned.
    vehicle_spawner_tank_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Used to spawn NPCs made with Character Definition asset.
    npc_spawner_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when a character is spawned.
        # Sends the `agent` character who was spawned.
        SpawnedEvent<public>:listenable(agent) = external {}

        # Signaled when a character is eliminated.
        # `Source` is the `agent` that eliminated the character. If the character was eliminated by a non-agent then `Source` is 'false'.
        # `Target` is the character that was eliminated.
        EliminatedEvent<public>:listenable(device_ai_interaction_result) = external {}

        # Enables this device. Characters will start to spawn.
        Enable<public>():void = external {}

        # Disables this device. Characters will despawn if *Despawn AIs When Disabled* is set.
        Disable<public>():void = external {}

        # Resets the spawn count allowing spawning of a new batch of characters.
        Reset<public>():void = external {}

        # Tries to spawn a character.
        Spawn<public>():void = external {}

        # Despawns all characters. If set, `Instigator` will be considered as the eliminator of those characters.
        DespawnAll<public>(Instigator:?agent):void = external {}

    # Used to set and trigger conversations made via the Conversation Graph. The conversation triggered is assigned to the device.
    conversation_device<public> := class<concrete><final>(creative_device_base, enableable):
        # Enables this device.
        # An enabled conversation device will listen for inputs and trigger conversations.
        Enable<override>():void = external {}

        # Disables this device.
        # A disabled conversation device will not listen for inputs and trigger conversations.
        Disable<override>():void = external {}

        # Check if the device is enabled.
        IsEnabled<override>()<transacts><decides>:void = external {}

        # Begins the assigned conversation with the `agent`.
        # The conversation will not start if the device or the `agent` are otherwise incapable of having a conversation.
        InitiateConversation<public>(Agent:agent):void = external {}

        # Check if an `agent` is in an active conversation.
        IsAgentInConversation<public>(Agent:agent)<transacts><decides>:void = external {}

        # Check if an `agent` can initiate a conversation.
        CanInitiateConversation<public>(Agent:agent)<transacts><decides>:void = external {}

        # Ends the assigned conversation with the `agent`.
        EndConversation<public>(Agent:agent):void = external {}

        # Ends all active conversations with this device.
        EndConversationForAll<public>():void = external {}

        # Signaled when a choice node tied to this event is selected by an `agent`.
        # Sends the `agent` that triggered the event, and `EventNumber` is the number specified in the Conversation Event node.
        OnConversationEvent<public>:listenable(tuple(agent, int)) = external {}

        # Returns the count of currently-active conversations with this device.
        GetActiveConversationsCount<public>()<transacts>:int = external {}

        # The Speaker Name of the device.
        var SpeakerName<public>:message = external {}

        # Whether the device will show the Speaker when nearby.
        var ShowNameWhenNearby<public>:logic = external {}

        # whether the device will show the Indicator Bubble when nearby.
        var ShowIndicatorBubble<public>:logic = external {}

        # The range that the Indicator Bubble will be seen at. Clamps to range[0, 25].
        var IndicatorBubbleRange<public>:float = external {}

        # Whether the device will show conversation text in worldspace when using the Radial UI type.
        var ShowConversationTextInWorldSpace<public>:logic = external {}

        # Sets the number of conversations allowed at once with the device.
        # This will not make any conversations exit if there are more current conversations than allowed after this call.
        var AllowedConversationCount<public>:int = external {}

    # Used to update the camera’s current viewpoint and rotation based on current camera mode.
    gameplay_camera_device<public> := class<abstract><epic_internal>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Adds the camera to the `Agent`’s camera stack and pushes it to be the active camera.
        AddTo<public>(Agent:agent):void = external {}

        # Adds the camera to all `Agent`s camera stacks and pushes it to be the active camera.
        AddToAll<public>():void = external {}

        # Removes the camera from the `Agent`’s camera stack and pops from being the active camera replacing it with the next one in the stack.
        RemoveFrom<public>(Agent:agent):void = external {}

        # Removes the camera from all `Agent`s camera stacks and pops from being the active camera replacing it with the next one in the stack.
        RemoveFromAll<public>():void = external {}

    # Used to update the camera’s current viewpoint and rotation based on a fixed point.
    gameplay_camera_fixed_point_device<public> := class<concrete><final>(gameplay_camera_device):

    # Used to update the camera's viewpoint from the perspective of the player character.
    gameplay_camera_first_person_device<public> := class<concrete><final>(gameplay_camera_device):

    # Used to update the camera’s current viewpoint and rotation based on a fixed angle.
    gameplay_camera_fixed_angle_device<public> := class<concrete><final>(gameplay_camera_device):

    # Used to update the camera's viewpoint to follow the target and be rotated manually.
    gameplay_camera_orbit_device<public> := class<concrete><final>(gameplay_camera_device):

    # Used to create an environmental prop that applies Nitro to those around it when it is destroyed.
    nitro_barrel_spawner_device<public> := class<concrete><final>(creative_device_base):
        # Triggers when an `agent` launches the barrel. Sends the launching `agent`.If the launcher is a non-agent, sends `false`.
        LaunchedEvent<public>:listenable(?agent) = external {}

        # Triggers when the barrel explodes after being launched. Sends the launching `agent`.If the launcher is a non-agent, sends `false`.
        ExplodedEvent<public>:listenable(?agent) = external {}

        # Enables and shows the barrel.Enabling the device when it's disabled will spawn a new barrel.
        Enable<public>():void = external {}

        # Disables and hides the barrel.Disabling the device will remove an existing barrel and reset the respawn delay.
        Disable<public>():void = external {}

        # Succeeds if the barrel is enabled and visible.
        IsEnabled<public>()<transacts><decides>:void = external {}

        # Sets the multiplier applied to the force used when launching.This is clamped between `0.25` and `2.0`.
        SetLaunchForceMultiplier<public>(LaunchForceMultiplier:float):void = external {}

        # Returns the force multiplier to launch the barrel.
        GetLaunchForceMultiplier<public>():float = external {}

        # Allows the barrel to respawn after it explodes, waiting *RespawnDelay* seconds.
        AllowRespawn<public>():void = external {}

        # Prevents the barrel from respawning.The *RespawnDelay* countdown will not start. If the countdown has already started, the barrel will not respawn when it ends.
        DisallowRespawn<public>():void = external {}

        # Succeeds if the barrel has respawn allowed.
        IsRespawnAllowed<public>()<transacts><decides>:void = external {}

        # Sets the delay time to respawn the barrel, clamped between `0.0` and `1000.0` seconds.This will override the delay timer if set during the delay countdown.
        SetRespawnDelay<public>(RespawnDelay:float):void = external {}

        # Returns the delay between exploding and respawning (if allowed), in seconds.
        GetRespawnDelay<public>():float = external {}

    # Specialized `vehicle_spawner_device` that allows a GetAway Car to be configured and spawned.
    vehicle_spawner_getaway_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Used to trigger level sequences that allow coordination of cinematic animation, transformation, and audio tracks.
    cinematic_sequence_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when the sequence is stopped.
        StoppedEvent<public>:listenable(tuple()) = external {}

        # Plays the sequence. This will only work when the device is set to *Everyone*
        Play<public>():void = external {}

        # Plays the sequence. An instigating 'Agent' is required when the device is set to anything except *Everyone*.
        Play<public>(Agent:agent):void = external {}

        # Plays the sequence in reverse. This will only work when the device is set to *Everyone*
        PlayReverse<public>():void = external {}

        # Plays the sequence in reverse. An instigating 'Agent' is required when the device is set to anything except *Everyone*.
        PlayReverse<public>(Agent:agent):void = external {}

        # Stops the sequence.
        Stop<public>():void = external {}

        # Stops the sequence. An instigating 'Agent' is required when the device is set to anything except *Everyone*.
        Stop<public>(Agent:agent):void = external {}

        # Go to the end and stop the sequence.
        GoToEndAndStop<public>():void = external {}

        # Go to the end and stop the sequence. An instigating 'Agent' is required when the device is set to anything except *Everyone*.
        GoToEndAndStop<public>(Agent:agent):void = external {}

        # Pauses the sequence.
        Pause<public>():void = external {}

        # Pauses the sequence. An instigating 'Agent' is required when the device is set to anything except *Everyone*.
        Pause<public>(Agent:agent):void = external {}

        # Toggles between `Play` and `Stop`.
        TogglePause<public>():void = external {}

        # Toggles between `Play` and `Stop`. An instigating 'Agent' is required when the device is set to anything except *Everyone*.
        TogglePause<public>(Agent:agent):void = external {}

        # Set the playback position (in frames) of the sequence.
        SetPlaybackFrame<public>(PlaybackFrame:int):void = external {}

        # Returns the playback position (in frames) of the sequence.
        GetPlaybackFrame<public>()<transacts>:int = external {}

        # Set the playback position (in time/seconds) of the sequence.
        SetPlaybackTime<public>(PlaybackTime:float):void = external {}

        # Returns the playback position (in time/seconds) of the sequence.
        GetPlaybackTime<public>()<transacts>:float = external {}

        # Set the playback rate of the sequence.
        SetPlayRate<public>(PlayRate:float):void = external {}

        # Returns the playback rate of the sequence.
        GetPlayRate<public>()<transacts>:float = external {}

    # The Rift Point volume is used to interface with and manage the Rift Point item,
    # and provides an area that enables players to plant the item to create search and destroy style gameplay.
    rift_point_volume_device<public> := class<concrete><final>(creative_device_base, enableable):
        # Sends an event when planting the Rift Point is started, passing in the planting `agent`.
        PlantStartEvent<public>:listenable(agent) = external {}

        # Sends an event when planting the Rift Point is canceled, passing in the planting `agent`.
        PlantCancelEvent<public>:listenable(agent) = external {}

        # Sends an event when the Rift Point is planted, passing in the planting `agent`.
        PlantEvent<public>:listenable(agent) = external {}

        # Sends an event when defusing the Rift Point is started, passing in the defusing `agent`.
        DefuseStartEvent<public>:listenable(agent) = external {}

        # Sends an event when defusing the Rift Point is canceled, passing in the defusing `agent`.
        DefuseCancelEvent<public>:listenable(agent) = external {}

        # Sends an event when the Rift Point is defused, passing in the defusing `agent`.
        DefuseEvent<public>:listenable(agent) = external {}

        # Sends an event when the Rift Point detonates, passing in the planting `agent`.
        DetonateEvent<public>:listenable(agent) = external {}

        # Sends an event when an `agent` enters the volume.
        OnAgentEntered<public>:listenable(agent) = external {}

        # Sends an event when an `agent` exits the volume.
        OnAgentExited<public>:listenable(agent) = external {}

        # Is true when `Agent` is in the volume.
        IsInVolume<public>(Agent:agent)<transacts><decides>:void = external {}

        # Returns an array of agents that are currently occupying the volume.
        GetAgentsInVolume<public>()<reads>:[]agent = external {}

        # Enables the device, allowing the Rift Point to be planted.
        Enable<override>():void = external {}

        # Disables the device, preventing the Rift Point from being planted.
        Disable<override>():void = external {}

        # Succeeds if the component is enabled, fails if it’s disabled.
        IsEnabled<override>()<transacts><decides>:void = external {}

    # Specialized `vehicle_spawner_device` that allows a war bus to be configured and spawned.
    vehicle_spawner_war_bus_device<public> := class<concrete><final>(vehicle_spawner_device):

    # Used to create plants with explosive pods that players can detonate and launch.
    wilds_plant_device<public> := class<concrete><final>(creative_device_base):
        # Triggers whenever the plant grows.
        GrowEvent<public>:listenable(tuple()) = external {}

        # Triggers whenever the plant or launched projectile explodes.
        #  * Sends the `agent` that initially launched the projectile or triggered an immediate explosion.
        #  * Sends `false` if no `agent` is found.
        ExplodeEvent<public>:listenable(?agent) = external {}

        # Triggers whenever the plant launches a projectile.
        #  * Sends the `agent` that triggered this event.
        #  * Sends `false` if no `agent` is found.
        LaunchEvent<public>:listenable(?agent) = external {}

        # Enables the device to allow interaction and let it grow.
        Enable<public>():void = external {}

        # Disables the device to prevent interaction and growth.
        Disable<public>():void = external {}

        # Grows the plant if the device is enabled. If *Infinite Regrowths* is `false`, this is limited by *Maximum Regrowths*.
        Grow<public>():void = external {}

        # Detonates the plant if the device is enabled.
        Explode<public>():void = external {}

        # Sets whether the plant can always regrow after launching a projectile or being destroyed.
        SetInfiniteRegrowths<public>(InfiniteRegrowths:logic):void = external {}

        # Sets how many times the plant can regrow after launching a projectile or being destroyed.
        #  * This applies across the device’s entire lifetime and is unaffected by *Enable* and *Disable*.
        #  * This value is clamped.
        SetMaximumRegrowths<public>(MaximumRegrowths:int):void = external {}

    # Used to create customizable Grind Rails.
    grind_rail_device<public> := class<concrete><final>(creative_device_base):
        # Enables this device, letting players grind on the rail.
        Enable<public>():void = external {}

        # Disables this device, preventing players from grinding on the rail.
        Disable<public>():void = external {}

        # Sets the color of the Grind Rail.
        SetRailColor<public>(Color:color):void = external {}

        # Hides the track. Players can still grind on the track if it is enabled.
        Hide<public>():void = external {}

        # Make this track visible.
        Show<public>():void = external {}

        # Signaled when an `agent` starts grinding on this `grind_rail_device`.
        StartedGrindingEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` starts grinding on this `grind_rail_device`.
        EndedGrindingEvent<public>:listenable(agent) = external {}

    # Used to create a customizable vine version of the Grind Rails.
    vine_rail_device<public> := class<concrete><final>(creative_device_base):
        # Enables this device, letting players grind on the vines.
        Enable<public>():void = external {}

        # Disables this device, preventing players from grinding on the vines.
        Disable<public>():void = external {}

        # Hides the track. Players can still grind on the track if it is enabled.
        Hide<public>():void = external {}

        # Make this track visible.
        Show<public>():void = external {}

        # Signaled when an `agent` starts grinding on this `vine_rail_device`.
        StartedGrindingEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` starts grinding on this `vine_rail_device`.
        EndedGrindingEvent<public>:listenable(agent) = external {}

    # Used to update the gameplay controls scheme based on current control mode.
    gameplay_controls_device<public> := class<abstract><epic_internal>(creative_device_base):
        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Adds the gameplay control to the `Player`’s gameplay controls stack and pushes it to be the active control.
        AddTo<public>(Agent:agent):void = external {}

        # Adds the gameplay control to all `Agent`s gameplay controls stack and pushes it to be the active control.
        AddToAll<public>():void = external {}

        # Removes the gameplay control from the `Agent`’s gameplay controls stack and pops from being the active control replacing it with the next one in the stack.
        RemoveFrom<public>(Agent:agent):void = external {}

        # Removes the gameplay control from all `Agent`s gameplay controls stack and pops from being the active control replacing it with the next one in the stack.
        RemoveFromAll<public>():void = external {}

    # Used to adapt the controls to side scroller functionality
    gameplay_controls_side_scroller_device<public> := class<concrete><final>(gameplay_controls_device):

    # Used to adapt the controls to the camera perspective
    gameplay_controls_third_person_device<public> := class<concrete><final>(gameplay_controls_device):

    # Use to create a cactus with healing fruits that can be burst to heal nearby players.
    healing_cactus_device<public> := class<concrete><final>(creative_device_base):
        # Triggers when the plant grows.
        GrowEvent<public>:listenable(tuple()) = external {}

        # Triggers when the plant bursts, passing in the triggering `agent`.
        BurstEvent<public>:listenable(?agent) = external {}

        # Enables the device to allow interaction and let it grow.
        Enable<public>():void = external {}

        # Disables the device to prevent interaction and growth.
        Disable<public>():void = external {}

        # Grows the plant if the device is enabled. If `Infinite Regrowths` is `false`, this is limited by `Maximum Regrowths`.
        # If someone is too close, the plant won't grow until they move away.
        Grow<public>():void = external {}

        # Burst the plant if the device is enabled, passing in the triggering `agent`.
        # If there is no triggering `agent`, players will only be healed if `Heal Targets` is set to `Everyone`.
        Burst<public>(Agent:agent):void = external {}

        # Burst the plant if the device is enabled, passing in the triggering `agent`.
        # If there is no triggering `agent`, players will only be healed if `Heal Targets` is set to `Everyone`.
        Burst<public>():void = external {}

    # Used to spawn various wildlife that players can herd, hunt, or tame.
    wildlife_spawner_device<public> := class<concrete><final>(creative_device_base):
        # Signaled when this device spawns wildlife.
        # Sends the `agent` wildlife that was spawned.
        SpawnedEvent<public>:listenable(agent) = external {}

        # Signaled when wildlife is eliminated.
        # `Source` is the `agent` that eliminated the wildlife. If the wildlife was eliminated by a non-agent, or because the oldest wildlife was eliminated as Spawn function was called after spawn count was exceeded, then `Source` is 'false'.
        # `Target` is the wildlife that was eliminated.
        EliminatedEvent<public>:listenable(device_ai_interaction_result) = external {}

        # Signaled when a wildlife eliminates an agent.
        # `Source` is the wildlife that eliminated the agent.
        # `Target` is the agent that was eliminated.
        EliminatingEvent<public>:listenable(device_ai_interaction_result) = external {}

        # Signaled when wildlife is tamed.
        # `Source` is the `agent` that tamed the wildlife.
        # `Target` is the wildlife that was tamed.
        TamedEvent<public>:listenable(device_ai_interaction_result) = external {}

        # Signaled when wildlife is untamed.
        # `Source` is the `agent` that tamed the wildlife.
        # `Target` is the wildlife that was untamed.
        UntamedEvent<public>:listenable(device_ai_interaction_result) = external {}

        # Signaled when wildlife is force-spawned and causes the oldest wildlife to be eliminated.
        # Sends the `agent` wildlife that was spawned.
        ForceSpawnedEvent<public>:listenable(agent) = external {}

        # Signaled when wildlife is damaged.
        # `Source` is the `agent` that damaged the wildlife. If the wildlife was damaged by a non-agent then `false` is returned.
        # `Target` is the wildlife that was damaged.
        DamagedEvent<public>:listenable(device_ai_interaction_result) = external {}

        # Signaled when wildlife eats a pickup such as a Shroom or Meat.
        #  Sends the wildlife that ate something.
        SomethingIsEatenEvent<public>:listenable(agent) = external {}

        # Signaled when an `agent` rides wildlife.
        # `Source` is the `agent` that started riding the wildlife.
        # `Target` is the wildlife that was ridden.
        RiddenEvent<public>:listenable(device_ai_interaction_result) = external {}

        # Signaled when an `agent` dismounts wildlife.
        # `Source` is the `agent` that dismounted the wildlife.
        # `Target` is the wildlife that was dismounted.
        DismountedEvent<public>:listenable(device_ai_interaction_result) = external {}

        # Enables this device.
        Enable<public>():void = external {}

        # Disables this device.
        Disable<public>():void = external {}

        # Resets the count on the *Total Spawn Count* option.
        Reset<public>():void = external {}

        # Spawns wildlife from this device. If spawn count is exceeded the oldest wildlife will be eliminated.
        Spawn<public>():void = external {}

        # Despawns wildlife. `Agent` is marked as the one who eliminated the wildlife.
        Despawn<public>(Agent:agent):void = external {}

        # Destroys this device, marking `Agent` as the destroyer of the device.
        DestroySpawner<public>(Agent:agent):void = external {}

        # Tames wildlife, making them AI partners of `Agent`.
        Tame<public>(Agent:agent):void = external {}

        # Untames any tamed wildlife that belong to `Agent`.
        Untame<public>(Agent:agent):void = external {}

        # Untames all wildlife.
        UntameAll<public>():void = external {}

        # Teleports `Agent` to the nearest wildlife, then `Agent` mounts that wildlife.
        Ride<public>(Agent:agent):void = external {}

        # Dismounts `Agent` from wildlife.
        Dismount<public>(Agent:agent):void = external {}

        # Dismounts all `agent`s from wildlife.
        DismountAll<public>():void = external {}

        # Restores energy to wildlife belonging to `Agent` by *Energy Restore Amount*.
        RestoreEnergy<public>(Agent:agent):void = external {}

        # Restores energy to wildlife by *Energy Restore Amount*.
        RestoreEnergyForAll<public>():void = external {}

        # Consumes energy from wildlife belonging to `Agent` by *Energy Consume Amount*.
        ConsumeEnergy<public>(Agent:agent):void = external {}

        # Consumes energy from wildlife by *Energy Consume Amount*.
        ConsumeEnergyForAll<public>():void = external {}

        # Returns the spawn limit of the device.
        GetSpawnLimit<public>()<transacts>:int = external {}

    # Specialized `vehicle_spawner_device` that allows a Rocket Racing Vehicle to be configured and spawned.
    vehicle_spawner_rocketracing_device<public> := class<concrete><final>(vehicle_spawner_device):

    # A vault door that requires a start of a sequence to damage a number of weakpoints to open.
    bank_vault_device<public> := class<concrete><final>(creative_device_base, enableable):
        # Enable the device.
        Enable<override>():void = external {}

        # Disable the device. Pauses vault sequence.
        Disable<override>():void = external {}

        # Succeeds if the device is enabled, fails if it's disabled.
        IsEnabled<override>()<transacts><decides>:void = external {}

        # Triggers when the vault sequence is started by a player or event.
        #  * Sends the triggering `agent` or none if the sequence was triggered by a function.
        StartSequenceEvent<public>:listenable(?agent) = external {}

        # Triggers when the vault is opened either by the destruction of its weakpoints or by an event.
        #  * Sends the last `agent` that damaged a weakpoint or the `agent` that started the sequence if no external damage was done or none if the sequence was triggered by a function.
        OpenEvent<public>:listenable(?agent) = external {}

        # Triggers when a weakpoint is activated. The weakpoint will begin to glow, but does not become vulnerable until a subsequent `WeakpointVulnerableEvent`
        #  * Sends the `agent` that activated the vault or none if the sequence was triggered by a function, and the `int` index of the weakpoint from 0-4, in order of activation.
        ActivateWeakpointEvent<public>:listenable(tuple(?agent, int)) = external {}

        # Triggers when a weakpoint becomes vulnerable.
        #  * Sends the `agent` that activated the vault or none if the sequence was triggered by a function, and the `int` index of the weakpoint from 0-4, in order of activation.
        WeakpointVulnerableEvent<public>:listenable(tuple(?agent, int)) = external {}

        # Triggers when a weakpoint is destroyed.
        #  * Sends the last `agent` that damaged the weakpoint or the `agent` that started the sequence if no external damage was done or none if the sequence was triggered by a function, and the `int` index of the weakpoint from 0-4, in order of activation.
        DestroyWeakpointEvent<public>:listenable(tuple(?agent, int)) = external {}

        # Destroys the remaining weakpoints and opens the vault door. Requires the device to be enabled.
        ForceOpen<public>():void = external {}

        # Begin the sequence of events to open the vault door without thermite, or unpauses the sequence if it was paused. Requires the device to be enabled. Weakpoints can only be damaged while the vault sequence is active.
        StartSequence<public>():void = external {}

        # Disable the weakpoint vulnerability and freeze the progress of each one. Weakpoints can only be damaged while the vault sequence is active.
        PauseSequence<public>():void = external {}

        # Restores vault to default state, deactivates the device, and heals all weakpoints to max health. Requires the device to be enabled.
        Reset<public>():void = external {}

        # Whether the vault sequence is currently active and not paused.
        IsSequenceActive<public>()<transacts><decides>:void = external {}

    # Specialized `vehicle_spawner_device` that allows an armored transport to be configured and spawned.
    vehicle_spawner_armored_transport_device<public> := class<concrete><final>(vehicle_spawner_device):

using {/Fortnite.com/Teams}
using {/Fortnite.com/Characters}
# Module import path: /Fortnite.com/AI
AI<public> := module:
    @experimental
    # Result of an AI action
    ai_action_result<native><public> := enum<open>:
        # The action has been successfully executed
        Success
        # The action has failed during its execution
        Failure
        # The action has been canceled before completion
        Canceled
        # The action was not allowed to start
        Disallowed

    focus_interface<native><public> := interface<epic_internal>:
        # Look At specified location. Will never complete unless interrupted.
        MaintainFocus<public>(Location:(/UnrealEngine.com/Temporary/SpatialMath:)vector3)<suspends>:void

        # Look At specified Agent. Will never complete unless interrupted.
        MaintainFocus<public>(Agent:agent)<suspends>:void

    # Get the focus_interface interface for the specified character.
    (InCharacter:fort_character).GetFocusInterface<native><public>()<transacts><decides>:focus_interface

    @experimental
    # Fortnite Guards AI actions management
    fort_guard_actions<native><public> := interface<epic_internal>(fort_npc_actions, fort_leashable):
        # Roam around the current position. Use the fort_leashable_component to specify the radius, else will roam anywhere.
        RoamAround<public>(?MovementType:movement_type = external {})<suspends>:navigation_result

        # Move in range to attack the current target
        MoveInRangeToAttack<public>()<suspends>:ai_action_result

        # Play a random emote from the character emotes bank
        PlayRandomEmote<public>()<suspends>:ai_action_result

        # Revive the specified target
        Revive<public>(Target:agent)<suspends>:ai_action_result

        # Attack the target
        Attack<public>(Target:fort_target_info)<suspends>:ai_action_result

    @experimental
    # Get the current fort_guard_actions interface for the specified character.
    (InCharacter:fort_character).GetFortGuardActions<native><public>()<transacts><decides>:fort_guard_actions

    @experimental
    # Fortnite NPC perception management
    fort_guard_perception<native><public> := interface<epic_internal>(fort_npc_perception):
        @experimental
        # Get information about the current target.
        GetCurrentTargetInfo<public>()<reads><decides>:fort_target_info

        @experimental
        # Get the current alert level of the guard.
        GetAlertLevel<public>()<reads>:fort_guard_alert_level

        @experimental
        # Get the current alert level of a target for the guard.
        GetAlertLevel<public>(Target:entity)<reads><decides>:fort_guard_alert_level

        @experimental
        # Event when the alert level of the guard has changed.
        AlertLevelChangedEvent<public>:listenable(fort_guard_alert_level)

        @experimental
        # Event when the current target has changed.
        CurrentTargetChangedEvent<public>:listenable(fort_target_info)

    @experimental
    # Get the current fort_guard_perception interface for the specified character.
    (InCharacter:fort_character).GetFortGuardPerception<native><public>()<transacts><decides>:fort_guard_perception

    fort_leashable<native><public> := interface<epic_internal>:
        # Set custom leash position.
        #  'InnerRadius' ranges from 0.0 to 20000.0 (in centimeters).
        #  'OuterRadius' ranges from 0.0 to 20000.0 (in centimeters) and no less than 'InnerRadius'.
        SetLeashPosition<public>(Location:(/UnrealEngine.com/Temporary/SpatialMath:)vector3, InnerRadius:float, OuterRadius:float):void

        # Set the agent to be the new center of the leash.
        #  'InnerRadius' ranges from 0.0 to 20000.0 (in centimeters).
        #  'OuterRadius' ranges from 0.0 to 20000.0 (in centimeters) and no less than 'InnerRadius'.
        SetLeashAgent<public>(Agent:agent, InnerRadius:float, OuterRadius:float):void

        # Removes the current leash.
        ClearLeash<public>():void

    # Get the current fort_leashable interface for the specified character.
    (InCharacter:fort_character).GetFortLeashable<native><public>()<transacts><decides>:fort_leashable

    @experimental
    # Fortnite NPC AI actions management
    fort_npc_actions<native><public> := interface<epic_internal>(navigatable, focus_interface):

    @experimental
    # Get the current fort_npc_actions interface for the specified character.
    (InCharacter:fort_character).GetFortNPCActions<native><public>()<transacts><decides>:fort_npc_actions

    @experimental
    # Fortnite NPC perception management
    fort_npc_perception<native><public> := interface<epic_internal>:
        @experimental
        # Get the last known position of a target.
        GetLastKnownPosition<public>(Target:entity)<reads><decides>:(/UnrealEngine.com/Temporary/SpatialMath:)vector3

        @experimental
        # Event when a target is added.
        TargetAddedEvent<public>:listenable(fort_target_info)

        @experimental
        # Event when the info about a target has been updated.
        TargetInfoUpdatedEvent<public>:listenable(fort_target_info)

        @experimental
        # Event when a target was removed.
        TargetRemovedEvent<public>:listenable(entity)

        @experimental
        # Event when a target is seen. Sight sense must be active.
        TargetSeenEvent<public>:listenable(fort_target_info)

        @experimental
        # Event when a target is heard. Hearing sense must be active.
        TargetHeardEvent<public>:listenable(fort_target_info)

        @experimental
        # Event when a target is touched. Touch sense must be active.
        TargetTouchedEvent<public>:listenable(fort_target_info)

        @experimental
        # Information about all detected targets.
        var<epic_internal> DetectedTargets<public>:[]fort_target_info

    @experimental
    # Get the current fort_npc_perception interface for the specified character.
    (InCharacter:fort_character).GetFortNPCPerception<native><public>()<transacts><decides>:fort_npc_perception

    navigation_target<native><public> := class<epic_internal>:

    # Generate a navigation_target from any position
    MakeNavigationTarget<native><public>(Position:(/UnrealEngine.com/Temporary/SpatialMath:)vector3):navigation_target

    # Generate a navigation_target from an agent
    MakeNavigationTarget<native><public>(Target:agent):navigation_target

    @experimental
    # Generate a navigation_target from an entity
    MakeNavigationTarget<native><public>(Entity:entity)<decides>:navigation_target

    # Result of a navigation request
    navigation_result<native><public> := enum:
        # The destination has been reached
        Reached
        # The destination has been partially reached (AllowPartialPath was used)
        PartiallyReached
        # Navigation has been interrupted before completion
        Interrupted
        # The navigating agent is blocked
        Blocked
        # The destination cannot be reached
        Unreachable

    movement_type<native><public> := class<concrete><final><computes><epic_internal>:

    # Module import path: /Fortnite.com/AI/movement_types
    movement_types<public> := module:
        Walking<public>:movement_type = external {}

        Running<public>:movement_type = external {}

    navigatable<native><public> := interface<epic_internal>:
        # Return the current destination of the character
        GetCurrentDestination<public>()<transacts><decides>:(/UnrealEngine.com/Temporary/SpatialMath:)vector3

        # Navigate toward the specified target
        NavigateTo<public>(Target:navigation_target, ?MovementType:movement_type = external {}, ?ReachRadius:float = external {}, ?AllowPartialPath:logic = external {})<suspends>:navigation_result

        # Stop navigation
        StopNavigation<public>():void

        # Wait for a specific duration
        Wait<public>(?Duration:float = external {})<suspends>:void

        # Apply a multiplier on the movement speed (Multiplier is clamped between 0.5 and 2)
        SetMovementSpeedMultiplier<public>(Multiplier:float):void

    # Get the navigatable interface for the specified character
    (InCharacter:fort_character).GetNavigatable<native><public>()<transacts><decides>:navigatable

    # Inherit from this to create a custom NPC behavior.
    # The npc_behavior can be defined for a character in a CharacterDefinition asset, or in a npc_spawner_device.
    npc_behavior<native><public> := class<abstract>:
        # This function is called when the NPC is added to the simulation.
        OnBegin<native_callable><public>()<suspends>:void = external {}

        # This function is called when the NPC is removed from the simulation.
        OnEnd<native_callable><public>():void = external {}

        # Returns the agent associated with this behavior.
        GetAgent<native><public>()<transacts><decides>:agent

    # Returns the `npc_behavior` for `InAgent`.
    (InAgent:agent).GetNPCBehavior<native><public>()<transacts><decides>:npc_behavior

    @experimental
    # Returns the `entity` for `InNPCBehavior`.
    (InNPCBehavior:npc_behavior).GetEntity<native><public>()<reads><decides>:entity

    @experimental
    # The different alert levels of a guard.
    fort_guard_alert_level<native><public> := enum:
        # The guard has not detected any hostile target.
        Unaware
        # The guard has seen a hostile target but hasn't identified it yet.
        Suspicious
        # The guard has identified a hostile target but can't see it any more.
        LostTarget
        # The guard has identified a hostile target.
        Alerted

    @experimental
    # Information about a perceived target.
    fort_target_info<native><public> := class<epic_internal>:
        @experimental
        # The source of the detection stimuli.
        Target<native><public>:entity

        @experimental
        # True if the target can be seen.
        HasLineOfSight<native><public>:logic

        @experimental
        # Attitude toward this target.
        Attitude<native><public>:team_attitude

    @experimental
    # Create a `navigation_target` from a `fort_target_info`.
    MakeNavigationTarget<native><public>(TargetInfo:fort_target_info):navigation_target

# Module import path: /Fortnite.com/Animation
Animation<public> := module:
    # Module import path: /Fortnite.com/Animation/PlayAnimation
    PlayAnimation<public> := module:
        # Result of a PlayAndAwait request.
        play_animation_result<native><public> := enum:
            # The animation completed successfully.
            Completed
            # The animation was interrupted whilst playing.
            Interrupted
            # The animation encountered an error during initialization or whilst playing.
            Error

        # An interface for playing an animation on an object.
        play_animation_controller<native><public> := interface<epic_internal>:
            # Play an animation sequence.
            PlayAndAwait<public>(AnimationSequence:animation_sequence, ?PlayRate:float = external {}, ?PlayCount:float = external {}, ?StartPositionSeconds:float = external {}, ?BlendInTime:float = external {}, ?BlendOutTime:float = external {})<suspends>:play_animation_result

            # Start an animation sequence and obtain an instance to query and manipulate.
            Play<public>(AnimationSequence:animation_sequence, ?PlayRate:float = external {}, ?PlayCount:float = external {}, ?StartPositionSeconds:float = external {}, ?BlendInTime:float = external {}, ?BlendOutTime:float = external {}):play_animation_instance

        # An animation instance created from play_animation_controller.Play that can be queried and manipulated.
        play_animation_instance<native><public> := class<epic_internal>:
            # Returns the state of the animation playback.
            GetState<native><public>()<transacts>:play_animation_state

            # Stops the animation.
            Stop<native><public>():void

            # Event triggered when the animation is completed.
            CompletedEvent<native><public>:listenable(tuple()) = external {}

            # Event triggered when the animation is interrupted.
            InterruptedEvent<native><public>:listenable(tuple()) = external {}

            # Event triggered when the animation has finished to blend out.
            BlendedInEvent<native><public>:listenable(tuple()) = external {}

            # Event triggered when the animation is beginning to blend out.
            BlendingOutEvent<native><public>:listenable(tuple()) = external {}

            # Helper function that waits for the animation to complete or be interrupted.
            Await<public>()<suspends>:play_animation_result = external {}

            # Helper function that succeeds if the state is Playing, BlendingIn, or BlendingOut.
            IsPlaying<public>()<transacts><decides>:void = external {}

        # The potential states of a play animation instance.
        play_animation_state<native><public> := enum:
            # The animation is blending in.
            BlendingIn
            # The animation has blended in, is playing, and has not begun blending out.
            Playing
            # The animation is playing and is blending out.
            BlendingOut
            # The animation completed successfully.
            Completed
            # The animation was stopped internally.
            Stopped
            # The animation was interrupted externally.
            Interrupted
            # An error occurred at creation or during playback.
            Error

        # Get the play_animation_controller for the specified character.
        (InCharacter:fort_character).GetPlayAnimationController<native><public>()<transacts><decides>:play_animation_controller

# Module import path: /Fortnite.com/Characters
Characters<public> := module:
    # Main API implemented by Fortnite characters.
    fort_character<native><public> := interface<unique><epic_internal>(positional, healable, healthful, damageable, shieldable, game_action_instigator, game_action_causer):
        # Returns the agent associated with this `fort_character`. Use this when interacting with APIs that require an `agent` reference.
        GetAgent<public>()<transacts><decides>:agent

        # Signaled when this `fort_character` is eliminated from the match.
        EliminatedEvent<public>():listenable(elimination_result)

        # Returns the rotation where this `fort_character` is looking or aiming at.
        GetViewRotation<public>()<transacts>:(/UnrealEngine.com/Temporary/SpatialMath:)rotation

        # Returns the location where this `fort_character` is looking or aiming from.
        GetViewLocation<public>()<transacts>:(/UnrealEngine.com/Temporary/SpatialMath:)vector3

        # Signaled when this `fort_character` jumps. Returns a listenable with a payload of this `fort_character`.
        JumpedEvent<public>():listenable(fort_character)

        # Signaled when this `fort_character` changes crouch state.
        # Sends `tuple` payload:
        #  - 0: the `fort_character` that changed crouch states.
        #  - 1: `true` if the character is crouching. `false` if the character is not crouching.
        CrouchedEvent<public>():listenable(tuple(fort_character, logic))

        # Signaled when this `fort_character` changes sprint state.
        # Sends `tuple` payload:
        #  - 0: the `fort_character` that changed sprint state.
        #  - 1: `true` if the character is sprinting. `false` if the character stopped sprinting.
        SprintedEvent<public>():listenable(tuple(fort_character, logic))

        # Succeeds if this `fort_character` is in the world and has not been eliminated. Most fort_character actions will silently fail if this fails. Please test IsActive if you want to handle these failure cases rather than allow them to silently fail.
        IsActive<public>()<transacts><decides>:void

        # Succeeds if this `fort_character` is in the 'Down But Not Out' state. In this state the character is down but can still be revived by teammates for a period of time.
        IsDownButNotOut<public>()<transacts><decides>:void

        # Succeeds if this `fort_character` is crouching.
        IsCrouching<public>()<transacts><decides>:void

        # Succeeds if this `fort_character` is standing on the ground.
        IsOnGround<public>()<transacts><decides>:void

        # Succeeds if this `fort_character` is standing in the air.
        IsInAir<public>()<transacts><decides>:void

        # Succeeds if this `fort_character` is inside water volume.
        IsInWater<public>()<transacts><decides>:void

        # Succeeds if this `fort_character` is in falling locomotion state.
        IsFalling<public>()<transacts><decides>:void

        # Succeeds if this `fort_character` is in gliding locomotion state.
        IsGliding<public>()<transacts><decides>:void

        # Succeeds if this `fort_character` is in flying locomotion state.
        IsFlying<public>()<transacts><decides>:void

        # Puts this `fort_character` into stasis, preventing certain types of movement specified by `Args`.
        PutInStasis<public>(Args:stasis_args)<transacts>:void

        # Release this `fort_character` from stasis.
        ReleaseFromStasis<public>()<transacts>:void

        # Sets this `fort_character` visibility to visible.
        Show<public>():void

        # Sets this `fort_character` visibility to invisible.
        Hide<public>():void

        # Control if this `fort_character` can be damaged.
        SetVulnerability<public>(Vulnerable:logic)<transacts>:void

        # Succeeds if this `fort_character` can be damaged. Fails if this `fort_character` cannot be damaged.
        IsVulnerable<public>()<transacts><decides>:void

        # Teleports this `fort_character` to the provided `Position` and applies the yaw and pitch of `Rotation`. Will fail if the `Position` specified is e.g. outside of the playspace or specifies a place where the character cannot fit.
        TeleportTo<public>(Position:(/UnrealEngine.com/Temporary/SpatialMath:)vector3, Rotation:(/UnrealEngine.com/Temporary/SpatialMath:)rotation)<transacts><decides>:void

    # Returns the `fort_character` for `InAgent`. Fails if `InAgent` is not a `fort_character`.
    (InAgent:agent).GetFortCharacter<native><public>()<transacts><decides>:fort_character

    # Returns a `game_action_instigator` interface for `InAgent`.
    (InAgent:agent).GetInstigator<native><public>()<transacts>:game_action_instigator

    # Returns the `agent` for `InInstigator`. Fails if `InInstigator` is not an `agent`.
    (InInstigator:game_action_instigator).GetInstigatorAgent<native><public>()<transacts><decides>:agent

    # Parameters for `fort_character.PutInStasis` function.
    stasis_args<native><public> := struct:
        # Controls if `fort_character` can still turn while in stasis.
        AllowTurning<native><public>:logic = external {}

        # Controls if `fort_character` can still fall while in stasis.
        AllowFalling<native><public>:logic = external {}

        # Controls if `fort_character` can still perform emotes while in stasis.
        AllowEmotes<native><public>:logic = external {}

# Module import path: /Fortnite.com/FortPlayerUtilities
FortPlayerUtilities<public> := module:
    # Sends `InPlayer` back to the main game lobby.
    (InPlayer:player).SendToLobby<native><public>()<transacts>:void

    # Succeeds if `InAgent` is spectating the experience. Fails otherwise.
    (InAgent:agent).IsSpectator<native><public>()<transacts><decides>:void

    # Returns an `[]player`s currently spectating `InPlayer`.
    (InPlayer:player).GetPlayersSpectating<native><public>()<transacts>:[]player

    # Returns the agent a spectator is currently spectating, fails if the spectator isn't spectating or isn't spectating an agent.
    (InAgent:agent).GetSpectatedAgent<native><public>()<reads><decides>:agent

    # Returns an `[]agent`s currently spectating `InAgent`.
    (InAgent:agent).GetSpectators<native><public>()<transacts>:[]agent

    # Respawns the character for `InAgent` at the provided `RespawnPosition` and applies the yaw of `RespawnRotation`.
    (InAgent:agent).Respawn<native><public>(RespawnPosition:(/UnrealEngine.com/Temporary/SpatialMath:)vector3, RespawnRotation:(/UnrealEngine.com/Temporary/SpatialMath:)rotation)<transacts>:void

# Module import path: /Fortnite.com/Game
Game<public> := module:
    # Result data for `fort_character` elimination events.
    elimination_result<native><public> := struct<epic_internal>:
        # The `fort_character` eliminated from the match by `EliminatingCharacter`.
        EliminatedCharacter<native><public>:fort_character

        # `fort_character` that eliminated `EliminatedCharacter` from the match. `EliminatingCharacter` will be false when `EliminatedCharacter` was eliminated through non-character actions, such as environmental damage.
        EliminatingCharacter<native><public>:?fort_character

    @experimental
    # This interface is implemented by the round manager living on the simulation entity.
    fort_round_manager<native><public> := interface<epic_internal>:
        # Subscribed callbacks will be invoked in two scenarios:
        #  - When a new Fortnite round starts
        #  - If a round is ongoing, your callback will be invoked immediately
        # Upon the round ending, all callbacks will be canceled
        SubscribeRoundStarted<public>(Callback:type {_()<suspends>:void}):cancelable

        # Subscribed callbacks will be invoked when a Fortnite round ends
        SubscribeRoundEnded<public>(Callback:type {_():void}):cancelable

    @experimental
    # Returns the round manager from the simulation entity.
    (InEntity:entity).GetFortRoundManager<public>()<transacts><decides>:fort_round_manager = external {}

    # Implemented by objects to allow reading position information.
    positional<native><public> := interface<epic_internal>:
        # Returns the transform of the object.
        GetTransform<public>()<transacts>:(/UnrealEngine.com/Temporary/SpatialMath:)transform

    # Implemented by Fortnite objects that have health state and can be eliminated.
    healthful<native><public> := interface<epic_internal>:
        # Returns the health state of the object. This value will be between 0.0 and `GetMaxHealth`
        GetHealth<public>()<transacts>:float

        # Sets the health state of the object to `Health`.
        #  * Health state will be clamped between 1.0 and `GetMaxHealth`.
        #  * Health state cannot be directly set to 0.0. To eliminate `healthful` objects use the `damageable.Damage` functions instead.
        SetHealth<public>(Health:float)<transacts>:void

        # Returns the maximum health of the object. This value will be between 1.0 and Inf.
        GetMaxHealth<public>()<transacts>:float

        # Sets the maximum health state of the object.
        #  * MaxHealth will be clamped between 1.0 and Inf.
        #  * Current health state will be scaled up or down based on the scale difference between the old and new MaxHealth state.
        SetMaxHealth<public>(MaxHealth:float)<transacts>:void

    # Implemented by Fortnite objects that have shields. A shield is a method of protection that can take incoming damage while leaving the health state unchanged.
    shieldable<native><public> := interface<epic_internal>:
        # Returns the shield state of the object. This value will be between 0.0 and `MaxShield`
        GetShield<public>()<transacts>:float

        # Sets the shield state of the object.
        #  * Shield state will be clamped between 0.0 and `MaxShield`.
        SetShield<public>(Shield:float)<transacts>:void

        # Returns the maximum shield state of the object. This value will be between 0.0 and Inf.
        GetMaxShield<public>()<transacts>:float

        # Sets the maximum shield state of the object.
        #  * MaxShield will be clamped between 0.0 and Inf.
        #  * Current shield state will be scaled up or down based on the scale difference between the old and new MaxShield state.
        SetMaxShield<public>(MaxShield:float)<transacts>:void

        # Signaled when the shield is damaged.
        DamagedShieldEvent<public>():listenable(damage_result)

        # Signaled when the shield is healed.
        HealedShieldEvent<public>():listenable(healing_result)

    # Implemented by Fortnite objects that can be damaged.
    damageable<native><public> := interface<epic_internal>:
        # Damage the `damageable` object anonymously by `Amount`. Setting `Amount` to less than 0 will cause no damage.
        # Use `Damage(:damage_args):void` when damage is being applied from a known instigator and source.
        Damage<public>(Amount:float):void

        # Damage the `damageable` object by `Args.Amount`. Setting `Amount` to less than 0 will cause no damage.
        Damage<public>(Args:damage_args):void

        # Signaled when damage is applied to the `damageable` object.
        DamagedEvent<public>():listenable(damage_result)

    # Implemented by Fortnite objects that can be healed.
    healable<native><public> := interface<epic_internal>:
        # Heal the `healable` object anonymously by `Amount`. Setting `Amount` to less than 0 will cause no healing.
        # Use `Heal(:healing_args):void` when healing is being applied from a known instigator and source.
        Heal<public>(Amount:float):void

        # Cause `Args.Amount` damage to the `damageable` object. Setting `Amount` to less than 0 will cause no damage.
        Heal<public>(Args:healing_args):void

        # Signaled when healing is applied to the `healable` object.
        HealedEvent<public>():listenable(healing_result)

    # Parameters for common damage functions on Fortnite objects.
    damage_args<native><public> := struct:
        # Player, agent, etc. that instigated the damage to the object.
        Instigator<native><public>:?game_action_instigator = external {}

        # Player, weapon, vehicle, etc. that damaged the object.
        Source<native><public>:?game_action_causer = external {}

        # Amount of damage to apply to the object.
        Amount<native><public>:float

    # Results for damage events on Fortnite objects.
    damage_result<native><public> := struct<epic_internal>:
        # Object that was damaged.
        Target<native><public>:damageable

        # Amount of damage applied to `Target`.
        Amount<native><public>:float

        # Player, agent, etc. that instigated the damage to `Target`. Can be false when damage is instigated by code, the environment, etc.
        Instigator<native><public>:?game_action_instigator = external {}

        # Player, weapon, vehicle, etc. that damaged `Target`. Can be false when damage is caused by code, the environment, etc.
        Source<native><public>:?game_action_causer = external {}

    # Parameters for common healing functions on Fortnite objects.
    healing_args<native><public> := struct:
        # Player, agents, etc. that instigated the healing of the object.
        Instigator<native><public>:?game_action_instigator = external {}

        # Player, weapon, vehicle, etc. that healed the object.
        Source<native><public>:?game_action_causer = external {}

        # Amount of healing to apply to the object.
        Amount<native><public>:float

    # Results for healing events on Fortnite objects.
    healing_result<native><public> := struct<epic_internal>:
        # Object that was healed.
        Target<native><public>:healable

        # Amount of healing applied to `Target`.
        Amount<native><public>:float

        # Player, agent, etc. that instigated healing of the `Target`. Can be false when damage is instigated by code, the environment, etc.
        Instigator<native><public>:?game_action_instigator = external {}

        # Player, weapon, vehicle, etc. that healed `Target`. Can be false when damage is caused by code, the environment, etc.
        Source<native><public>:?game_action_causer = external {}

    # Implemented by Fortnite objects that initiate game actions, such as damage and heal. For example, player or agents.
    # Event listeners often use `game_action_instigators` to calculate player damage scores.
    game_action_instigator<native><public> := interface<epic_internal>:

    # Implemented by Fortnite objects that can be passed through game action events, such as damage and heal.
    # For example: player, vehicle, or weapon.
    # 
    # Event Listeners often use `game_action_causer` to pass along additional information about what weapon caused the damage.
    # Systems will then use that information for completing quests or processing game specific event logic.
    game_action_causer<native><public> := interface:

# Module import path: /Fortnite.com/Playspaces
Playspaces<public> := module:
    # A nested container that scopes objects, style, gameplay rules, visuals, etc. All objects and players in an experience will belong to a fort_playspace. There is typically one `fort_playspace` for an entire experience, though this may change in the future as the platform evolves.
    # 
    # To access the `fort_playspace` for a `creative_device` use `creative_device.GetPlayspace`.
    fort_playspace<native><public> := interface<epic_internal>:
        # Get all human `player`s that are in the current `fort_playspace`.
        GetPlayers<public>()<transacts>:[]player

        # Get the `fort_team_collection` for the current `fort_playspace`.
        GetTeamCollection<public>()<transacts>:fort_team_collection

        # Signaled when a human `player` joins the `fort_playspace`. Returns a subscribable with a payload of the`player` that entered the `fort_playspace`.
        PlayerAddedEvent<public>():listenable(player)

        # Signaled when a human `player` leaves the `fort_playspace`. Returns a subscribable with a payload of the`player` that left the `fort_playspace`.
        PlayerRemovedEvent<public>():listenable(player)

        # Get all `agent`s that are participating in the current `fort_playspace` experience. Participants might be human players (of `player` type) or AI-controlled characters (of `agent` type) that are registered and affecting the participant's count.
        GetParticipants<public>()<transacts>:[]agent

        # Signaled when a participant `agent` joins the `fort_playspace`. Returns a subscribable with a payload of the`agent` that entered the `fort_playspace`.
        ParticipantAddedEvent<public>():listenable(agent)

        # Signaled when a participant `agent` leaves the `fort_playspace`. Returns a subscribable with a payload of the`agent` that left the `fort_playspace`.
        ParticipantRemovedEvent<public>():listenable(agent)

    @experimental
    # Returns an associated `fort_playspace` for this entity.  * Fails if this entity is not in the scene, and therefore not associated with a playspace.
    (InEntity:entity).GetPlayspaceForEntity<native><public>()<transacts><decides>:fort_playspace

# Module import path: /Fortnite.com/Teams
Teams<public> := module:
    # A generic set of team attitudes. Use this enum to model relationship behavior between your experience's agents/teams.
    team_attitude<native><public> := enum:
        # Agents/teams are friends. In Fortnite games two `agent`s on the same `team` are `friendly`.
        Friendly
        # Agents/teams are neutral. In Fortnite games items and AI not belonging to a `friendly` or `hostile` team are `neutral`.
        Neutral
        # Agents/teams are hostile. In fortnite games two `agent`s on opposing `team`s are `hostile`.
        Hostile

    # Collection used to manage `team`s and `agent`s on those teams.
    # Use `fort_playspace.GetTeamCollection()` to get the `team_collection` for the active experience.
    fort_team_collection<native><public> := interface<epic_internal>:
        # Returns an array of all the `team`s known to this `fort_team_collection`
        GetTeams<public>()<transacts>:[]team

        # Adds `InAgent` to `InTeam`.
        # Fails if `InTeam` is not part of the `fort_team_collection`.
        AddToTeam<public>(InAgent:agent, InTeam:team)<transacts><decides>:void

        # Succeeds if `InAgent` is on `InTeam`.
        # Fails if:
        #  * `InAgent` is not on `InTeam`.
        #  * `InTeam` is not part of the `fort_team_collection`.
        IsOnTeam<public>(InAgent:agent, InTeam:team)<transacts><decides>:void

        # Returns an array of all `agent`s on `InTeam`.
        # Fails if `InTeam` is not part of the `fort_team_collection`.
        GetAgents<public>(InTeam:team)<transacts><decides>:[]agent

        # Get the `team` that `InAgent` is on.
        # Fails if `InAgent` is not on a team in this `fort_team_collection`.
        GetTeam<public>(InAgent:agent)<transacts><decides>:team

        # Returns the `team_attitude` between `Team1` and `Team2`.
        # Fails if:
        #  * `Team1` is not in this `fort_team_collection`.
        #  * `Team2` is not in this `fort_team_collection`.
        GetTeamAttitude<public>(Team1:team, Team2:team)<transacts><decides>:team_attitude

        # Returns the `team_attitude` between `Agent1` and `Agent2`.
        # Fails if:
        #  * `Agent1` is not on a team in this `fort_team_collection`.
        #  * `Agent2` is not on a team in this `fort_team_collection`.
        GetTeamAttitude<public>(Agent1:agent, Agent2:agent)<transacts><decides>:team_attitude

# Module import path: /Fortnite.com/Vehicles
Vehicles<public> := module:
    # Returns the `fort_vehicle` for `InCharacter`.
    # Fails if `InCharacter` is not associated with a `fort_vehicle`.
    (InCharacter:fort_character).GetVehicle<native><public>()<transacts><decides>:fort_vehicle

    # Main API implemented by Fortnite vehicles.
    fort_vehicle<native><public> := interface<unique><epic_internal>(positional, healthful, damageable, game_action_causer):
        # Succeeds if this `fort_vehicle` is standing on ground.
        IsOnGround<public>()<transacts><decides>:void

        # Succeeds if this `fort_vehicle` is standing in air.
        IsInAir<public>()<transacts><decides>:void

        # Succeeds if this `fort_vehicle` is standing in water.
        IsInWater<public>()<transacts><decides>:void

        # Returns an array with all the passengers of the vehicle.
        GetPassengers<public>()<transacts>:[]fort_character

        # Returns the fuel state of the vehicle. If the vehicle uses fuel, this value will be between 0.0 and `GetFuelCapacity`. Otherwise, this value will be -1.0.
        GetFuelRemaining<public>()<transacts>:float

        # Returns the maximum fuel capacity of the vehicle. If the vehicle uses fuel, this value will be between 1.0 and Inf. Otherwise, this value will be -1.0.
        GetFuelCapacity<public>()<transacts>:float

        # Teleports the `fort_vehicle` to the specified `Position` and `Rotation`.
        TeleportTo<public>(Position:(/UnrealEngine.com/Temporary/SpatialMath:)vector3, Rotation:(/UnrealEngine.com/Temporary/SpatialMath:)rotation)<transacts><decides>:void
