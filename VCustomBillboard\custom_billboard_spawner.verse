using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
using. VCustomBillboard.Assets
using. VPropSpawner
using. VGiga.Assets.Mats

custom_billboard<public> := class<internal>(i_disposable):
    Props:[]creative_prop_unique

    Dispose<override>():void=
        for(Prop:Props):
            Prop.Dispose()
            
custom_billboard_spawner<public> := class(auto_creative_device, i_init):
    @editable BillboardAsset:creative_prop_asset = DefaultCreativePropAsset
    var PropSpawner:?prop_spawner = false
    
    Init<override>(Container:vcontainer):void=
        set PropSpawner = Container.ResolveErrOp(prop_spawner)

    SpawnLineRecur(Text:string, StartIndexPrefix:int, Translation:vector3, Scale:vector3)<suspends>:[]creative_prop_unique=
        var StartIndex :int= StartIndexPrefix

        IsGreen := if(Text[StartIndex] = '<'
            Text[StartIndex+1] = 'g'
            Text[StartIndex+2] = 'r'
            Text[StartIndex+3] = 'e'
            Text[StartIndex+4] = 'e'
            Text[StartIndex+5] = 'n'
            Text[StartIndex+6] = '>'
        ):
            set StartIndex += 7
            true
        else:
            false

        End := LineEndIndex(Text, StartIndex)
        if(End > StartIndex):
            MProp := PropSpawner.G().Spawn(BillboardAsset, transform:
                Translation := Translation
                Rotation := rotation{}
                Scale := Scale
            )
            if(Prop := MProp?):
                if(IsGreen?):
                    SetTextForPropGreen(Prop.Prop, Text, StartIndex, End)
                else:
                    SetTextForPropWhite(Prop.Prop, Text, StartIndex, End)
            
                NextStart := End + 1
                if(NextStart < Text.Length):
                    return array{Prop} + SpawnLineRecur(Text, NextStart, Translation - vector3{Z:=40.0}, Scale)
                else:
                    return array{Prop}

        return array{}
        
    Spawn<public>(Text:string, Translation:vector3, Scale:vector3)<suspends>:custom_billboard=
        Props := SpawnLineRecur(Text, 0, Translation, Scale)
        custom_billboard:
            Props := Props

SetTextForPropGreen(Prop:creative_prop, Text:string, Start:int, End:int)<transacts>:void=
    var NotFoundOffset :int= 0
    if(StartPosition := Quotient[(32 - (End - Start)), 2]):
        for(I := 0..Start-1):
            Prop.SetMaterial(Invisible_M, ?Index := I)
        for(I := End-1..31):
            Prop.SetMaterial(Invisible_M, ?Index := I)
        for(I := Start..End-1
            Char := Text[I]
        ):
            Material := option. case(Char):
                '#' => TextMaterialsGreen.MI_RotateToPlayer_Text_Token
                '$' => TextMaterialsGreen.MI_RotateToPlayer_Text_Cash
                '0' => TextMaterialsGreen.MI_RotateToPlayer_Text_0
                '1' => TextMaterialsGreen.MI_RotateToPlayer_Text_1
                '2' => TextMaterialsGreen.MI_RotateToPlayer_Text_2
                '3' => TextMaterialsGreen.MI_RotateToPlayer_Text_3
                '4' => TextMaterialsGreen.MI_RotateToPlayer_Text_4
                '5' => TextMaterialsGreen.MI_RotateToPlayer_Text_5
                '6' => TextMaterialsGreen.MI_RotateToPlayer_Text_6
                '7' => TextMaterialsGreen.MI_RotateToPlayer_Text_7
                '8' => TextMaterialsGreen.MI_RotateToPlayer_Text_8
                '9' => TextMaterialsGreen.MI_RotateToPlayer_Text_9
                'Q' => TextMaterialsGreen.MI_RotateToPlayer_Text_Q
                'W' => TextMaterialsGreen.MI_RotateToPlayer_Text_W
                'E' => TextMaterialsGreen.MI_RotateToPlayer_Text_E
                'R' => TextMaterialsGreen.MI_RotateToPlayer_Text_R
                'T' => TextMaterialsGreen.MI_RotateToPlayer_Text_T
                'Y' => TextMaterialsGreen.MI_RotateToPlayer_Text_Y
                'U' => TextMaterialsGreen.MI_RotateToPlayer_Text_U
                'I' => TextMaterialsGreen.MI_RotateToPlayer_Text_I
                'O' => TextMaterialsGreen.MI_RotateToPlayer_Text_O
                'P' => TextMaterialsGreen.MI_RotateToPlayer_Text_P
                'A' => TextMaterialsGreen.MI_RotateToPlayer_Text_A
                'S' => TextMaterialsGreen.MI_RotateToPlayer_Text_S
                'D' => TextMaterialsGreen.MI_RotateToPlayer_Text_D
                'F' => TextMaterialsGreen.MI_RotateToPlayer_Text_F
                'G' => TextMaterialsGreen.MI_RotateToPlayer_Text_G
                'H' => TextMaterialsGreen.MI_RotateToPlayer_Text_H
                'J' => TextMaterialsGreen.MI_RotateToPlayer_Text_J
                'K' => TextMaterialsGreen.MI_RotateToPlayer_Text_K
                'L' => TextMaterialsGreen.MI_RotateToPlayer_Text_L
                'Z' => TextMaterialsGreen.MI_RotateToPlayer_Text_Z
                'X' => TextMaterialsGreen.MI_RotateToPlayer_Text_X
                'C' => TextMaterialsGreen.MI_RotateToPlayer_Text_C
                'V' => TextMaterialsGreen.MI_RotateToPlayer_Text_V
                'B' => TextMaterialsGreen.MI_RotateToPlayer_Text_B
                'N' => TextMaterialsGreen.MI_RotateToPlayer_Text_N
                'M' => TextMaterialsGreen.MI_RotateToPlayer_Text_M
                'q' => TextMaterialsGreen.MI_RotateToPlayer_Text_Q
                'w' => TextMaterialsGreen.MI_RotateToPlayer_Text_W
                'e' => TextMaterialsGreen.MI_RotateToPlayer_Text_E
                'r' => TextMaterialsGreen.MI_RotateToPlayer_Text_R
                't' => TextMaterialsGreen.MI_RotateToPlayer_Text_T
                'y' => TextMaterialsGreen.MI_RotateToPlayer_Text_Y
                'u' => TextMaterialsGreen.MI_RotateToPlayer_Text_U
                'i' => TextMaterialsGreen.MI_RotateToPlayer_Text_I
                'o' => TextMaterialsGreen.MI_RotateToPlayer_Text_O
                'p' => TextMaterialsGreen.MI_RotateToPlayer_Text_P
                'a' => TextMaterialsGreen.MI_RotateToPlayer_Text_A
                's' => TextMaterialsGreen.MI_RotateToPlayer_Text_S
                'd' => TextMaterialsGreen.MI_RotateToPlayer_Text_D
                'f' => TextMaterialsGreen.MI_RotateToPlayer_Text_F
                'g' => TextMaterialsGreen.MI_RotateToPlayer_Text_G
                'h' => TextMaterialsGreen.MI_RotateToPlayer_Text_H
                'j' => TextMaterialsGreen.MI_RotateToPlayer_Text_J
                'k' => TextMaterialsGreen.MI_RotateToPlayer_Text_K
                'l' => TextMaterialsGreen.MI_RotateToPlayer_Text_L
                'z' => TextMaterialsGreen.MI_RotateToPlayer_Text_Z
                'x' => TextMaterialsGreen.MI_RotateToPlayer_Text_X
                'c' => TextMaterialsGreen.MI_RotateToPlayer_Text_C
                'v' => TextMaterialsGreen.MI_RotateToPlayer_Text_V
                'b' => TextMaterialsGreen.MI_RotateToPlayer_Text_B
                'n' => TextMaterialsGreen.MI_RotateToPlayer_Text_N
                'm' => TextMaterialsGreen.MI_RotateToPlayer_Text_M
                ',' => TextMaterialsGreen.MI_RotateToPlayer_Text_Comma
                '.' => TextMaterialsGreen.MI_RotateToPlayer_Text_Dot
                '/' => TextMaterialsGreen.MI_RotateToPlayer_Text_Slash
                0oc3 => block:
                    Char2 := Text[I+1]
                    case(Char2):
                        0oB3 => TextMaterialsGreen.MI_RotateToPlayer_Text_Oo
                        0o93 => TextMaterialsGreen.MI_RotateToPlayer_Text_Oo
                        _ => Invisible_M
                0oc4 => block:
                    Char2 := Text[I+1]
                    case(Char2):
                        0o84 => TextMaterialsGreen.MI_RotateToPlayer_Text_Al
                        0o85 => TextMaterialsGreen.MI_RotateToPlayer_Text_Al
                        0o98 => TextMaterialsGreen.MI_RotateToPlayer_Text_El
                        0o99 => TextMaterialsGreen.MI_RotateToPlayer_Text_El
                        0o87 => TextMaterialsGreen.MI_RotateToPlayer_Text_Ci
                        0o86 => TextMaterialsGreen.MI_RotateToPlayer_Text_Ci
                0oc5 => block:
                    Char2 := Text[I+1]
                    case(Char2):
                        0oBA => TextMaterialsGreen.MI_RotateToPlayer_Text_Zi
                        0oB9 => TextMaterialsGreen.MI_RotateToPlayer_Text_Zi
                        0oBC => TextMaterialsGreen.MI_RotateToPlayer_Text_Zy
                        0oBB => TextMaterialsGreen.MI_RotateToPlayer_Text_Zy
                        0o84 => TextMaterialsGreen.MI_RotateToPlayer_Text_Ni
                        0o83 => TextMaterialsGreen.MI_RotateToPlayer_Text_Ni
                        0o9B => TextMaterialsGreen.MI_RotateToPlayer_Text_Si
                        0o9A => TextMaterialsGreen.MI_RotateToPlayer_Text_Si
                        0o82 => TextMaterialsGreen.MI_RotateToPlayer_Text_Ly
                        0o81 => TextMaterialsGreen.MI_RotateToPlayer_Text_Ly
                # 0oc4 => TextMaterialsGreen.MI_RotateToPlayer_Text_Al
                # 'ę' => TextMaterialsGreen.MI_RotateToPlayer_Text_El
                # 'Ą' => TextMaterialsGreen.MI_RotateToPlayer_Text_Al
                # 'ą' => TextMaterialsGreen.MI_RotateToPlayer_Text_Al
                # 'Ó' => TextMaterialsGreen.MI_RotateToPlayer_Text_Oo
                # 'ó' => TextMaterialsGreen.MI_RotateToPlayer_Text_Oo
                ' ' => Invisible_M
            # Prop.SetMaterial(Material, ?Index := StartPosition + I - Start)
            if(Mat := Material?):
                Prop.SetMaterial(Mat, ?Index := StartPosition + I - Start - NotFoundOffset)
            else:
                set NotFoundOffset += 1

    
SetTextForPropWhite(Prop:creative_prop, Text:string, Start:int, End:int)<transacts>:void=
    if(StartPosition := Quotient[(32 - (End - Start)), 2]):
        var NotFoundOffset :int= 0
        for(I := 0..Start-1):
            Prop.SetMaterial(Invisible_M, ?Index := I)
        for(I := End-1..31):
            Prop.SetMaterial(Invisible_M, ?Index := I)
        for(I := Start..End-1
            Char := Text[I]
        ):
            Material :?material= option. case(Char):
                '#' => TextMaterials.MI_RotateToPlayer_Text_Token
                '$' => TextMaterials.MI_RotateToPlayer_Text_Cash
                '0' => TextMaterials.MI_RotateToPlayer_Text_0
                '1' => TextMaterials.MI_RotateToPlayer_Text_1
                '2' => TextMaterials.MI_RotateToPlayer_Text_2
                '3' => TextMaterials.MI_RotateToPlayer_Text_3
                '4' => TextMaterials.MI_RotateToPlayer_Text_4
                '5' => TextMaterials.MI_RotateToPlayer_Text_5
                '6' => TextMaterials.MI_RotateToPlayer_Text_6
                '7' => TextMaterials.MI_RotateToPlayer_Text_7
                '8' => TextMaterials.MI_RotateToPlayer_Text_8
                '9' => TextMaterials.MI_RotateToPlayer_Text_9
                'Q' => TextMaterials.MI_RotateToPlayer_Text_Q
                'W' => TextMaterials.MI_RotateToPlayer_Text_W
                'E' => TextMaterials.MI_RotateToPlayer_Text_E
                'R' => TextMaterials.MI_RotateToPlayer_Text_R
                'T' => TextMaterials.MI_RotateToPlayer_Text_T
                'Y' => TextMaterials.MI_RotateToPlayer_Text_Y
                'U' => TextMaterials.MI_RotateToPlayer_Text_U
                'I' => TextMaterials.MI_RotateToPlayer_Text_I
                'O' => TextMaterials.MI_RotateToPlayer_Text_O
                'P' => TextMaterials.MI_RotateToPlayer_Text_P
                'A' => TextMaterials.MI_RotateToPlayer_Text_A
                'S' => TextMaterials.MI_RotateToPlayer_Text_S
                'D' => TextMaterials.MI_RotateToPlayer_Text_D
                'F' => TextMaterials.MI_RotateToPlayer_Text_F
                'G' => TextMaterials.MI_RotateToPlayer_Text_G
                'H' => TextMaterials.MI_RotateToPlayer_Text_H
                'J' => TextMaterials.MI_RotateToPlayer_Text_J
                'K' => TextMaterials.MI_RotateToPlayer_Text_K
                'L' => TextMaterials.MI_RotateToPlayer_Text_L
                'Z' => TextMaterials.MI_RotateToPlayer_Text_Z
                'X' => TextMaterials.MI_RotateToPlayer_Text_X
                'C' => TextMaterials.MI_RotateToPlayer_Text_C
                'V' => TextMaterials.MI_RotateToPlayer_Text_V
                'B' => TextMaterials.MI_RotateToPlayer_Text_B
                'N' => TextMaterials.MI_RotateToPlayer_Text_N
                'M' => TextMaterials.MI_RotateToPlayer_Text_M
                'q' => TextMaterials.MI_RotateToPlayer_Text_Q
                'w' => TextMaterials.MI_RotateToPlayer_Text_W
                'e' => TextMaterials.MI_RotateToPlayer_Text_E
                'r' => TextMaterials.MI_RotateToPlayer_Text_R
                't' => TextMaterials.MI_RotateToPlayer_Text_T
                'y' => TextMaterials.MI_RotateToPlayer_Text_Y
                'u' => TextMaterials.MI_RotateToPlayer_Text_U
                'i' => TextMaterials.MI_RotateToPlayer_Text_I
                'o' => TextMaterials.MI_RotateToPlayer_Text_O
                'p' => TextMaterials.MI_RotateToPlayer_Text_P
                'a' => TextMaterials.MI_RotateToPlayer_Text_A
                's' => TextMaterials.MI_RotateToPlayer_Text_S
                'd' => TextMaterials.MI_RotateToPlayer_Text_D
                'f' => TextMaterials.MI_RotateToPlayer_Text_F
                'g' => TextMaterials.MI_RotateToPlayer_Text_G
                'h' => TextMaterials.MI_RotateToPlayer_Text_H
                'j' => TextMaterials.MI_RotateToPlayer_Text_J
                'k' => TextMaterials.MI_RotateToPlayer_Text_K
                'l' => TextMaterials.MI_RotateToPlayer_Text_L
                'z' => TextMaterials.MI_RotateToPlayer_Text_Z
                'x' => TextMaterials.MI_RotateToPlayer_Text_X
                'c' => TextMaterials.MI_RotateToPlayer_Text_C
                'v' => TextMaterials.MI_RotateToPlayer_Text_V
                'b' => TextMaterials.MI_RotateToPlayer_Text_B
                'n' => TextMaterials.MI_RotateToPlayer_Text_N
                'm' => TextMaterials.MI_RotateToPlayer_Text_M
                ',' => TextMaterials.MI_RotateToPlayer_Text_Comma
                '.' => TextMaterials.MI_RotateToPlayer_Text_Dot
                '/' => TextMaterials.MI_RotateToPlayer_Text_Slash
                0oc3 => block:
                    Char2 := Text[I+1]
                    case(Char2):
                        0oB3 => TextMaterials.MI_RotateToPlayer_Text_Oo
                        0o93 => TextMaterials.MI_RotateToPlayer_Text_Oo
                0oc4 => block:
                    Char2 := Text[I+1]
                    case(Char2):
                        0o84 => TextMaterials.MI_RotateToPlayer_Text_Al
                        0o85 => TextMaterials.MI_RotateToPlayer_Text_Al
                        0o98 => TextMaterials.MI_RotateToPlayer_Text_El
                        0o99 => TextMaterials.MI_RotateToPlayer_Text_El
                        0o87 => TextMaterials.MI_RotateToPlayer_Text_Ci
                        0o86 => TextMaterials.MI_RotateToPlayer_Text_Ci
                0oc5 => block:
                    Char2 := Text[I+1]
                    case(Char2):
                        0oBA => TextMaterials.MI_RotateToPlayer_Text_Zi
                        0oB9 => TextMaterials.MI_RotateToPlayer_Text_Zi
                        0oBC => TextMaterials.MI_RotateToPlayer_Text_Zy
                        0oBB => TextMaterials.MI_RotateToPlayer_Text_Zy
                        0o84 => TextMaterials.MI_RotateToPlayer_Text_Ni
                        0o83 => TextMaterials.MI_RotateToPlayer_Text_Ni
                        0o9B => TextMaterials.MI_RotateToPlayer_Text_Si
                        0o9A => TextMaterials.MI_RotateToPlayer_Text_Si
                        0o82 => TextMaterials.MI_RotateToPlayer_Text_Ly
                        0o81 => TextMaterials.MI_RotateToPlayer_Text_Ly
                ' ' => Invisible_M
            if(Mat := Material?):
                Prop.SetMaterial(Mat, ?Index := StartPosition + I - Start - NotFoundOffset)
            else:
                set NotFoundOffset += 1
        

        # NewLinesCount := CountNewLines(Text)
        # # Lines := SplitNewLines(Text)

        # MProps := for(X := 0..NewLinesCount-1):
        # 	PropSpawner.Spawn(D.BillboardAsset, transform:
        # 		Translation := Translation
        # 		Rotation := rotation{}
        # 		Scale := Scale
        # 	)

        # if(Prop := MProp?
        # 	TextLen := Text.Length
        # 	StartPosition := Quotient[(32 - TextLen), 2]
        # ):
        # 	for(I->Char:Text
        # 		Material := case(Char):
        # 			'$' => MI_RotateToPlayer_Text_Cash
        # 			'0' => MI_RotateToPlayer_Text_0
        # 			'1' => MI_RotateToPlayer_Text_1
        # 			'2' => MI_RotateToPlayer_Text_2
        # 			'3' => MI_RotateToPlayer_Text_3
        # 			'4' => MI_RotateToPlayer_Text_4
        # 			'5' => MI_RotateToPlayer_Text_5
        # 			'6' => MI_RotateToPlayer_Text_6
        # 			'7' => MI_RotateToPlayer_Text_7
        # 			'8' => MI_RotateToPlayer_Text_8
        # 			'9' => MI_RotateToPlayer_Text_9
        # 			'Q' => MI_RotateToPlayer_Text_Q
        # 			'W' => MI_RotateToPlayer_Text_W
        # 			'E' => MI_RotateToPlayer_Text_E
        # 			'R' => MI_RotateToPlayer_Text_R
        # 			'T' => MI_RotateToPlayer_Text_T
        # 			'Y' => MI_RotateToPlayer_Text_Y
        # 			'U' => MI_RotateToPlayer_Text_U
        # 			'I' => MI_RotateToPlayer_Text_I
        # 			'O' => MI_RotateToPlayer_Text_O
        # 			'P' => MI_RotateToPlayer_Text_P
        # 			'A' => MI_RotateToPlayer_Text_A
        # 			'S' => MI_RotateToPlayer_Text_S
        # 			'D' => MI_RotateToPlayer_Text_D
        # 			'F' => MI_RotateToPlayer_Text_F
        # 			'G' => MI_RotateToPlayer_Text_G
        # 			'H' => MI_RotateToPlayer_Text_H
        # 			'J' => MI_RotateToPlayer_Text_J
        # 			'K' => MI_RotateToPlayer_Text_K
        # 			'L' => MI_RotateToPlayer_Text_L
        # 			'Z' => MI_RotateToPlayer_Text_Z
        # 			'X' => MI_RotateToPlayer_Text_X
        # 			'C' => MI_RotateToPlayer_Text_C
        # 			'V' => MI_RotateToPlayer_Text_V
        # 			'B' => MI_RotateToPlayer_Text_B
        # 			'N' => MI_RotateToPlayer_Text_N
        # 			'M' => MI_RotateToPlayer_Text_M
        # 			'q' => MI_RotateToPlayer_Text_Q
        # 			'w' => MI_RotateToPlayer_Text_W
        # 			'e' => MI_RotateToPlayer_Text_E
        # 			'r' => MI_RotateToPlayer_Text_R
        # 			't' => MI_RotateToPlayer_Text_T
        # 			'y' => MI_RotateToPlayer_Text_Y
        # 			'u' => MI_RotateToPlayer_Text_U
        # 			'i' => MI_RotateToPlayer_Text_I
        # 			'o' => MI_RotateToPlayer_Text_O
        # 			'p' => MI_RotateToPlayer_Text_P
        # 			'a' => MI_RotateToPlayer_Text_A
        # 			's' => MI_RotateToPlayer_Text_S
        # 			'd' => MI_RotateToPlayer_Text_D
        # 			'f' => MI_RotateToPlayer_Text_F
        # 			'g' => MI_RotateToPlayer_Text_G
        # 			'h' => MI_RotateToPlayer_Text_H
        # 			'j' => MI_RotateToPlayer_Text_J
        # 			'k' => MI_RotateToPlayer_Text_K
        # 			'l' => MI_RotateToPlayer_Text_L
        # 			'z' => MI_RotateToPlayer_Text_Z
        # 			'x' => MI_RotateToPlayer_Text_X
        # 			'c' => MI_RotateToPlayer_Text_C
        # 			'v' => MI_RotateToPlayer_Text_V
        # 			'b' => MI_RotateToPlayer_Text_B
        # 			'n' => MI_RotateToPlayer_Text_N
        # 			'm' => MI_RotateToPlayer_Text_M
        # 			',' => MI_RotateToPlayer_Text_M
        # 			'.' => MI_RotateToPlayer_Text_M
        # 			' ' => Invisible_M
        # 	):
        # 		Prop.Prop.SetMaterial(Material, ?Index := StartPosition + I)

        # 	option. custom_billboard:
        # 		Prop := Prop
        # else:
        # 	false
            
CountNewLines<public>(Text:string):int=
    var Count:int= 0

    for(I->Char:Text
        Char = '\n'
    ):
        set Count += 1

    Count			

LineEndIndex<public>(Text:string, StartIndex:int):int=
    var I:int = StartIndex
    loop:
        if(Char := Text[I]):
            if(Char = '\n'):
                break
        else:
            break
        
        set I += 1
    I
        
    