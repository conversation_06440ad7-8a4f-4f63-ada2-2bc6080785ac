using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/UI
using. /Fortnite.com/UI
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
canvas_panel_34_generated := class:
	UEFN_TextBlock_C_6:text_block
	UEFN_TextBlock_C_3:text_block
	UEFN_TextBlock_C_2:text_block
	UEFN_TextBlock_C_8:text_block
	UEFN_TextBlock_C_1:text_block
	UEFN_TextBlock_C:text_block
	UEFN_TextBlock_C_5:text_block
	UEFN_TextBlock_C_4:text_block
	StackBox_41:stack_box
	UEFN_Button_Regular_C:button_loud
	Image_73fColorBlock:color_block
	Image_73rColorBlock:color_block
	Overlay_34:overlay
	Image_73ColorBlock:color_block
	CanvasPanel_34:canvas

UEFN_TextBlock_C_6TextVar<localizes>:message =  "- Eliminating <PERSON><PERSON> Grants Xp"
UEFN_TextBlock_C_3TextVar<localizes>:message =  "- Xp Collectible Pickups"
UEFN_TextBlock_C_2TextVar<localizes>:message =  "- Daily Playtime Xp Rewards (Reset fixed  in 1.41!)"
UEFN_TextBlock_C_8TextVar<localizes>:message =  "UPDATE 1.41"
UEFN_TextBlock_C_1TextVar<localizes>:message =  "- Fixed Buyable Heals"
UEFN_TextBlock_CTextVar<localizes>:message =  "- Fixed Football Disappearing Sometimes"
UEFN_TextBlock_C_5TextVar<localizes>:message =  "- Power Ups And Skills Based On Football Players!"
UEFN_TextBlock_C_4TextVar<localizes>:message =  "UPDATE 1.3"
UEFN_Button_Regular_CTextVar<localizes>:message = "PLAY"
make_canvas_panel_34_generated():canvas_panel_34_generated=

	UEFN_TextBlock_C_6 :text_block= text_block:
		DefaultText := UEFN_TextBlock_C_6TextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
	UEFN_TextBlock_C_3 :text_block= text_block:
		DefaultText := UEFN_TextBlock_C_3TextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
	UEFN_TextBlock_C_2 :text_block= text_block:
		DefaultText := UEFN_TextBlock_C_2TextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
	UEFN_TextBlock_C_8 :text_block= text_block:
		DefaultText := UEFN_TextBlock_C_8TextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
	UEFN_TextBlock_C_1 :text_block= text_block:
		DefaultText := UEFN_TextBlock_C_1TextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
	UEFN_TextBlock_C :text_block= text_block:
		DefaultText := UEFN_TextBlock_CTextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
	UEFN_TextBlock_C_5 :text_block= text_block:
		DefaultText := UEFN_TextBlock_C_5TextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
	UEFN_TextBlock_C_4 :text_block= text_block:
		DefaultText := UEFN_TextBlock_C_4TextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
	StackBox_41 :stack_box= stack_box:
		Orientation := orientation.Vertical
		Slots := array:
			stack_box_slot:
				Padding := margin:
					Top := 5.000000
				HorizontalAlignment := horizontal_alignment.Center
				VerticalAlignment := vertical_alignment.Top
				Distribution := option. 1.0 
				Widget := UEFN_TextBlock_C_4
			stack_box_slot:
				Padding := margin:
					Top := 5.000000
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Top
				Distribution := option. 1.0 
				Widget := UEFN_TextBlock_C_5
			stack_box_slot:
				Padding := margin:
					Top := 5.000000
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Top
				Distribution := option. 1.0 
				Widget := UEFN_TextBlock_C
			stack_box_slot:
				Padding := margin:
					Top := 5.000000
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Top
				Distribution := option. 1.0 
				Widget := UEFN_TextBlock_C_1
			stack_box_slot:
				Padding := margin:
					Top := 50.000000
				HorizontalAlignment := horizontal_alignment.Center
				VerticalAlignment := vertical_alignment.Top
				Distribution := option. 1.0 
				Widget := UEFN_TextBlock_C_8
			stack_box_slot:
				Padding := margin:
					Top := 5.000000
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Top
				Distribution := option. 1.0 
				Widget := UEFN_TextBlock_C_2
			stack_box_slot:
				Padding := margin:
					Top := 5.000000
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Top
				Distribution := option. 1.0 
				Widget := UEFN_TextBlock_C_3
			stack_box_slot:
				Padding := margin:
					Top := 5.000000
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Top
				Distribution := option. 1.0 
				Widget := UEFN_TextBlock_C_6
	UEFN_Button_Regular_C :button_loud= button_loud:
		DefaultText := UEFN_Button_Regular_CTextVar
	Image_73fColorBlock :color_block= color_block:
		DefaultColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
		DefaultOpacity := 1.000000
		DefaultDesiredSize := vector2:
			X := 32.000000
			Y := 32.000000
	Image_73rColorBlock :color_block= color_block:
		DefaultColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultOpacity := 0.999000
		DefaultDesiredSize := vector2:
			X := 32.000000
			Y := 32.000000
	Overlay_34 :overlay= overlay:
		Slots := array:
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := Image_73rColorBlock
			overlay_slot:
				Padding := margin:
					Left := 3.000000
					Top := 3.000000
					Right := 3.000000
					Bottom := 3.000000
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := Image_73fColorBlock
			overlay_slot:
				Padding := margin:
					Bottom := 51.000000
				HorizontalAlignment := horizontal_alignment.Center
				VerticalAlignment := vertical_alignment.Bottom
				Widget := UEFN_Button_Regular_C
			overlay_slot:
				Padding := margin:
					Left := 50.000000
					Top := 50.000000
					Right := 50.000000
					Bottom := 50.000000
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Top
				Widget := StackBox_41
	Image_73ColorBlock :color_block= color_block:
		DefaultColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
		DefaultOpacity := 0.720000
		DefaultDesiredSize := vector2:
			X := 32.000000
			Y := 32.000000
	CanvasPanel_34 :canvas= canvas:
		Slots := array:
			canvas_slot:
				Offsets := margin:
					Right := 0.000000
					Bottom := 0.000000
				Anchors := anchors:
					Maximum := vector2:
						X := 1.000000
						Y := 1.000000
				SizeToContent := false
				Widget := Image_73ColorBlock
			canvas_slot:
				Alignment := vector2:
					X := 0.500000
					Y := 0.500000
				Offsets := margin:
					Left := 200.000000
					Top := 200.000000
					Right := 200.000000
					Bottom := 200.000000
				Anchors := anchors:
					Maximum := vector2:
						X := 1.000000
						Y := 1.000000
				SizeToContent := false
				Widget := Overlay_34


	canvas_panel_34_generated:
		UEFN_TextBlock_C_6 := UEFN_TextBlock_C_6
		UEFN_TextBlock_C_3 := UEFN_TextBlock_C_3
		UEFN_TextBlock_C_2 := UEFN_TextBlock_C_2
		UEFN_TextBlock_C_8 := UEFN_TextBlock_C_8
		UEFN_TextBlock_C_1 := UEFN_TextBlock_C_1
		UEFN_TextBlock_C := UEFN_TextBlock_C
		UEFN_TextBlock_C_5 := UEFN_TextBlock_C_5
		UEFN_TextBlock_C_4 := UEFN_TextBlock_C_4
		StackBox_41 := StackBox_41
		UEFN_Button_Regular_C := UEFN_Button_Regular_C
		Image_73fColorBlock := Image_73fColorBlock
		Image_73rColorBlock := Image_73rColorBlock
		Overlay_34 := Overlay_34
		Image_73ColorBlock := Image_73ColorBlock
		CanvasPanel_34 := CanvasPanel_34
