using. /Fortnite.com/AI
using. /Fortnite.com/Characters
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Game
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Teams
using. /Fortnite.com/UI
using. /Fortnite.com/Vehicles
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/Curves
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/UI
using. /Verse.org/Assets
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Concurrency
using. /Verse.org/Native
using. /Verse.org/Random
using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Verse
using. VCustomBillboard
using. VGiga


house_unlocks_counter := class<concrete>:
    @editable Billboard:?custom_billboard_editable = false

    var UnlockIds:[]string = array{}
    var UnlockedCount:int = 0
    var TotalCount:int = 0

    InitManual<public>(PUnlockIds:[]string, UnlockEvent:event(unlocked_unlocked_event_data)):void=
        set UnlockIds = PUnlockIds
        set TotalCount = UnlockIds.Length
        UnlockEvent.Subscribe1(OnUnlockableUnlocked)
        UpdateBillboard()
    
    OnUnlockableUnlocked(Data:unlocked_unlocked_event_data):void=
        if(UnlockIds.ArrayContains[Data.UnlockableId]):
            IncrementUnlocked()
    
    IncrementUnlocked<public>():void=
        set UnlockedCount = UnlockedCount + 1
        UpdateBillboard()
    
    UpdateBillboard():void=
        if(Board := Billboard?):
            Board.SetTextGreen("{UnlockedCount}/{TotalCount}")
        else:
            LError()
