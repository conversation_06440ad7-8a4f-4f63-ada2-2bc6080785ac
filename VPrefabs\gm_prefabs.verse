using { /Fortnite.com/AI }
using { /Fortnite.com/Characters }
using { /Fortnite.com/Devices }
using { /Fortnite.com/Devices/CreativeAnimation }
using { /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes }
using { /Fortnite.com/FortPlayerUtilities }
using { /Fortnite.com/Game }
using { /Fortnite.com/Playspaces }
using { /Fortnite.com/Teams }
using { /Fortnite.com/UI }
using { /Fortnite.com/Vehicles }
using { /UnrealEngine.com/Temporary }
using { /UnrealEngine.com/Temporary/Curves }
using { /UnrealEngine.com/Temporary/Diagnostics }
using { /UnrealEngine.com/Temporary/SpatialMath }
using { /UnrealEngine.com/Temporary/UI }
using { /Verse.org/Assets }
using { /Verse.org/Colors }
using { /Verse.org/Colors/NamedColors }
using { /Verse.org/Concurrency }
using { /Verse.org/Native }
using { /Verse.org/Random }
using { /Verse.org/Simulation }
using { /Verse.org/Simulation/Tags }
using { /Verse.org/Verse }
using { VArrowSystem }
using { VChestsSystem }
using { VCreaturesSystem }
using { VGiga }
using { VGiga.Pool }
using { VGoldGranter }
using { VHouseSystem }
using { VLocalization }
using { VMapBuilder }
using { VMapCode }
using { VMatchmakingDisabler }
using { VMinigames }
using { VNotifications }
using { VPropSpawner }
using { VQuestSystem }
using { VRaceMinigame }
using { VResourcesSystem }
using { VCoop }

gm_prefabs_devic<public> := class(auto_creative_device, i_init):
    @editable Container<public>:player_events_manager_devic = player_events_manager_devic{}
    # @editable MatchmakingDisablerDevic<public>:matchmaking_disabler_devic = matchmaking_disabler_devic{}
    @editable TriggerDevicePool<public>:trigger_device_pool_devic = trigger_device_pool_devic{}

    @editable MEntryCinematicStartedChannel:?channel_device = false
    @editable MEntryCinematicEndedChannel:?channel_device = false
    # @editable BillboardDevicePool<public>:billboard_device_pool_devic = billboard_device_pool_devic{}
    # @editable GameManagerXPDevice:game_manager_xp_devic = game_manager_xp_devic{}

    var Coop :?coop= false

    OnEntryCinematicStartedChannel(Houses:houses_manager)<suspends>:void=
        if(EntryCinematicStartedChannel := MEntryCinematicStartedChannel?):
            loop:
                MAgent := EntryCinematicStartedChannel.ReceivedTransmitEvent.Await()
                if(Agent := MAgent?):
                    AdminOrAgent := Coop.G().AdminOrAgent(Agent) 
                    Houses.ClaimFreeHouse(AdminOrAgent)
                    if(House := Houses.GetAgentHouse[AdminOrAgent]):
                        House.D.PlayerCheckPoint.Register(Agent)
                        
    OnEntryCinematicEndedChannel(Houses:houses_manager)<suspends>:void=
        if(EntryCinematicEndedChannel := MEntryCinematicEndedChannel?):
            loop:
                MAgent := EntryCinematicEndedChannel.ReceivedTransmitEvent.Await()
                if(Agent := MAgent?):
                    Houses.TeleportAgentToHouse(Agent) #fix for house not moved under ground
                    Sleep(1.1)
                    Houses.TeleportAgentToHouseEntryMinigame(Agent)

    Init<override>(C:vcontainer):void=
        set Coop = C.ResolveOp[coop] or Err()
        
    InitPrefabsAsync<public>(PBalance:gm_balance)<suspends>:?gm_prefabs=
        Print("0")
        var Balance:gm_balance = PBalance
        Balance.ChangeIfTestBalance()
        # NotificationsSystem := notifications_system{}
        # Container.Register(NotificationsSystem)

        # MatchmakingDisablerDevic.Init(Balance.MatchmakingDisableAfterMinutes)
        Print("00")
        Logger := giga_logger{}
        SpawnBetweenPoints := GetDevices(spawn_between_points_devic)
        SpawnMaps := GetDevices(spawn_map_devic)
        # PetsManagers := GetDevices(pets_manager_devic)
        Container.Register(Balance)
        Container.Register(Balance.BalanceSpawners)
        Container.Register(Balance.HouseBalance)
        Container.Register(Balance.Rewards)

        Container.Register(save_system_balance:
            SaveEnabled := true
            SaveGoldEnabled := true
            MTimeToSaveInSec := option. 60 * 5.0 
            # MTimeToSaveInSec := option. 10.0
            # MTimeToSaveInSec := option. 60.0 * 5.0 
        )

        Container.Register(resources_manager_balance:
            FansEnabled := false
            MoneyAsGold := false
            EnableWoodResource := false
            UiShowGoldPerSecond := false
            UiShowPets := false
            UiShowWood := false
            UiShowName := true
            UiShowRebirths := false
            HideOnlyTokensOverlay := true
            HideOnlyGoldOverlay := true
        )

        Container.PreInitAsync()


        Houses := Container.ResolveErr(houses_manager)
        spawn. OnEntryCinematicEndedChannel(Houses)
        spawn. OnEntryCinematicStartedChannel(Houses)

        Balance.QuestBalance.AddCompleter("claim house", Houses.HouseClaimedEvent)
        Balance.QuestBalance.AddCompleter("build whole house", Houses.AllUnlocksBoughtEv)

        RaceMinigames := Container.ResolveErr(race_minigame)
        RaceMinigames.AgentFinishedEv.Subscribe1(Houses.TeleportAgentToHouse)

        Phones := Container.ResolveErr(VPhone.phones)
        Balance.QuestBalance.AddCompleter(simple_quest_completer:
            TypeId := "register account"
            Event := Phones.RegisteredAccountEv
        )
        Balance.QuestBalance.AddArrowGetter("register account", Houses.GetPhonePositionForAgent)


        Balance.QuestBalance.AddCompleter(
            "make deposit",
            Phones.MadeDepositEv
        )
        # Balance.QuestBalance.AddArrowGetter("make deposit", Houses.GetPhonePositionForAgent)
        Race := Container.ResolveErr(VRaceMinigame.race_minigame)
        Balance.QuestBalance.AddCompleter(
            "car race",
            Race.AgentFinishedCarRaceEv
        )
        Balance.QuestBalance.AddCompleter(
            "parkour",
            Race.AgentFinishedParkourEv
        )
        SquidPlates := Container.ResolveErr(squid_plates)

        Balance.QuestBalance.AddCompleter(
            "glassbridge",
            SquidPlates.PlayerCompletedMinigameEv
        )

        Buyables := Container.ResolveErr(VBuyables.buyables)
        Balance.QuestBalance.AddCompleter("buy building", Buyables.BuildingBoughtEv)
        Balance.QuestBalance.AddCompleter("buy cosmetic", Buyables.CosmeticBoughtEv)
        Balance.QuestBalance.AddCompleter("buy house", Buyables.HouseBoughtEv)
        
        Resources := Container.ResolveErr(VResourcesSystem.resources_manager)
        Balance.QuestBalance.AddCompleter(
            "have cash 1mln",
            Resources.Has1mlnGoldEv
        )

        spawn. OnRebirth(Resources)
        
        PropSpawner := Container.ResolveErr(prop_spawner)
        ResourcesManager := Container.ResolveErr(resources_manager)
        ArrowSystem := Container.ResolveErr(arrow_system)
        
        # if(# MinigameFloorCleaning := GetDevice[minigame_floor_cleaning_devic]
            # MinigameLeaflets := GetDevice[minigame_leaflets_manager_devic]
        # 	ArrowSystemDevic := GetDevice[arrow_system_devic]
        # ):
            TriggerPool := TriggerDevicePool.GetPool()
            Container.Register(TriggerPool)

            # ArrowSystem := ArrowSystemDevic.Init(Container, PropSpawner)
            
            ChestManagerDevices := GetDevices(chest_manager_devic)
            for(Chest:ChestManagerDevices):
                Chest.Init(Container)
        
            # for(X:PetsManagers):
            # 	X.Init(Container, PropSpawner, Logger, PBalance.PetsBalance)
                
            # var MGrantFromKills:?grant_from_kills = false
            # if(GrantGoldFromKillsDevice := GetDevice[grant_from_kills_devic]):
            # 	GrantFromKills := GrantGoldFromKillsDevice.Init(Container, CreaturesSystem)
            # 	set MGrantFromKills = option. GrantFromKills
            # else:
            # 	LPrint("GrantGoldFromKills device missing")

            
            # if(MusicManager := GetDevice[music_manager_devic]):
            # 	MusicManager.Init(Container)

            #quests
            KillPlayerCompleter := Container.GetKillerKilledQuestCompleter()
            Balance.QuestBalance.AddCompleter(KillPlayerCompleter)

            # CreaturesSystem := Container.ResolveErr(creatures_system)
            # KillCreaturesCompleter := CreaturesSystem.GetKillCreaturesQuestCompleter()
            # Balance.QuestBalance.AddCompleter(KillCreaturesCompleter)

            Balance.QuestBalance.AddCompleter(GetPlayTimeQuestCompleter(Container))

            QuestSystem := quest_system_c(Container, Balance.QuestBalance)
            spawn. ResetQuestsArrowOnHouseClaim(Houses, QuestSystem)
            #end quest

            # MusicManager.Init(Container)
            
            # spawners with tag are always enabled at start
            for(Sp:SpawnBetweenPoints):
                Sp.Init(Container, PropSpawner, true)
                
            for(Sp:SpawnMaps):
                Sp.Init(Container, PropSpawner, true)

            for(Obj:FindCreativeObjectsWithTag(destroy_on_start_tag{})
                Prop := creative_prop[Obj]
            ):
                Prop.Dispose()
                
            
            Print("5")
            # map_code{}.Init(Container)

            GM := gm_prefabs:
                D := Self
                Container := Container
                # GameManagerXPDevice := GameManagerXPDevice
                ResourcesManager := ResourcesManager
                # MinigameFloorCleaningDevic := MinigameFloorCleaning
                # MinigameLeafletsDevic := MinigameLeaflets
                # MusicManagerDevic := MusicManager
                Balance := Balance
                ArrowSystem := ArrowSystem
                # CreaturesSystem :=  CreaturesSystem
                # MGrantFromKills := MGrantFromKills
                PropSpawner := PropSpawner
                TriggerPool := TriggerPool
            
            option. GM
        # else:
        # 	false
    OnRebirth(Resources:resources_manager)<suspends>:void=
        loop:
            Agent := Resources.RebirthEvent.Await()
            spawn. Container.SimulatePlayerLeaving(Agent)

        
    ResetQuestsArrowOnHouseClaim(Houses:houses_manager, Quests:quest_system)<suspends>:void=
        loop:
            Owner := Houses.HouseClaimedEvent.Await()
            LPrint("Owner claimed house")
            
            Sleep(0.1)
            Quests.RestartArrowsEv.Signal(Owner)
                


        

gm_prefabs<public> := class<internal>:
    D:gm_prefabs_devic
    Container<public>:player_events_manager_devic
    # GameManagerXPDevice:game_manager_xp_devic
    ResourcesManager<public> : resources_manager
    # MinigameFloorCleaningDevic : minigame_floor_cleaning_devic
    # MinigameLeafletsDevic : minigame_leaflets_manager_devic
    Balance<public>:gm_balance
    ArrowSystem<public>:arrow_system
    # CreaturesSystem<public>:creatures_system
    # MGrantFromKills<public>:?grant_from_kills
    PropSpawner<public>:prop_spawner
    TriggerPool<public>:trigger_device_pool

    InitAsync<public>()<suspends>:gm_prefabs=
        Container.InitAsync()
        Self

GetPlayTimeQuestCompleter<public>(Container:player_events_manager_devic):simple_quest_completer=
    TenSecPassed := event(agent){}
    spawn. TickEveryMin(Container, TenSecPassed)
    simple_quest_completer:
        TypeId := "playtime"
        Event := TenSecPassed

TickEveryMin(Container:player_events_manager_devic, TenSecPassed:event(agent))<suspends>:void=
    loop:
        Sleep(60.0)
        for(Player->voidd:Container.GetRegisteredPlayers()):
            TenSecPassed.Signal(Player)
            
        

    
    