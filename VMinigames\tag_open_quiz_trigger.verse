using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 

tag_open_knowledge_quiz := class(tag):
	GetAll<public>(D:creative_device)<transacts>:[]trigger_device=
		for(Device:D.FindCreativeObjectsWithTag(Self),
			Casted := trigger_device[Device]
		):
			Casted

tag_open_math_quiz := class(tag):
	GetAll<public>(D:creative_device)<transacts>:[]trigger_device=
		for(Device:D.FindCreativeObjectsWithTag(Self),
			Casted := trigger_device[Device]
		):
			Casted
		
tag_open_banking_quiz := class(tag):
	GetAll<public>(D:creative_device)<transacts>:[]trigger_device=
		for(Device:D.FindCreativeObjectsWithTag(Self),
			Casted := trigger_device[Device]
		):
			Casted

tag_open_christmas_quiz := class(tag):
	GetAll<public>(D:creative_device)<transacts>:[]trigger_device=
		for(Device:D.FindCreativeObjectsWithTag(Self),
			Casted := trigger_device[Device]
		):
			Casted
			
# device_tag<public>(t:castable_subtype(creative_object_interface)) := class(tag):
# 	GetAll<public>(D:creative_device)<transacts>:[]t=
# 		for(Device:D.FindCreativeObjectsWithTag(Self),
# 			Casted := t[Device]
# 		):
# 			Casted

# some_trigger_tag := class(tag_device(trigger_device)){}

# usage:
# SomeTriggers := some_trigger_tag{}.GetAll(Self) # Self as used from creative_device