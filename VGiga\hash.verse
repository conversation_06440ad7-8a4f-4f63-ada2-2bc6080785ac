HashInt<public>(Val:int, Salt:int):int=
	var Hash:int= Salt
    set Hash = (Hash * 31) + Val
	if:
		Val2 := (Hash * 31) + Floor((Val / 7))
		set Hash = Val2
	if:
		Val2 := (Hash * 31) + Floor((Val / 13))
		set Hash = Val2
	if:
		Val2 := Floor((Hash / 31)) + Floor((Val / 41))
		set Hash = Val2
	return Hash

HashIntWithTimeInFront<public>(Val:int, Salt:int, LongTime:int):string=
	
	Time := (LongTime * 3571).TrimLeft(3)
	var Hash:int= Salt + Time
    set Hash = (Hash * 31) + Val + Time
	# LPrint("Hash {Hash}")
	
	if:
		Val2 := (Hash * 31) + Floor((Val / 7)) + Time
		set Hash = Val2
	# LPrint("Hash {Hash}")
	if:
		Val2 := (Hash * 31) + Floor((Val / 13)) + Time
		set Hash = Val2
	# LPrint("Hash {Hash}")

	var HashStringReverse :string= "{Hash}".Reverse()
	# LPrint("HashStringReverse {HashStringReverse}")
	if(HashStringReverse.Length > 6, Sliced := HashStringReverse.Slice[0,6]):
		set HashStringReverse = Sliced
	return "{Time}A{HashStringReverse}"