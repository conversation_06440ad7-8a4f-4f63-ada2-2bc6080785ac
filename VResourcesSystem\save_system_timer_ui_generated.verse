using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/UI
using. /Fortnite.com/UI
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
save_timer_generated := class:
	TimerText:text_block
	Image:texture_block
	Overlay_186:overlay
	SaveTimer:canvas

TimerTextTextVar<localizes>:message =  "5:55"
make_save_timer_generated():save_timer_generated=

	TimerText :text_block= text_block:
		DefaultText := TimerTextTextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
	Image :texture_block= texture_block:
		DefaultImage := VResourcesSystem.Assets.T_SaveTimerBg
		DefaultDesiredSize := vector2:
			X := 512.000000
			Y := 124.000000
	Overlay_186 :overlay= overlay:
		Slots := array:
			overlay_slot:
				Padding := margin:
					Right := -250.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := Image
			overlay_slot:
				Padding := margin:
					Left := 170.000000
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Center
				Widget := TimerText
	SaveTimer :canvas= canvas:
		Slots := array:
			canvas_slot:
				Offsets := margin:
					Left := 8.000000
					Top := 424.000000
					Right := 512.000000
					Bottom := 128.000000
				SizeToContent := false
				Widget := Overlay_186


	save_timer_generated:
		TimerText := TimerText
		Image := Image
		Overlay_186 := Overlay_186
		SaveTimer := SaveTimer
