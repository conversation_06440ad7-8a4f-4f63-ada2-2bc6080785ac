using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/UI
using. /Fortnite.com/UI
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
points_canvas_generated := class:
	PointsValue:text_block
	UEFN_TextBlock_C_0:text_block
	PointsCanvas:canvas

PointsValueTextVar<localizes>:message =  "100"
UEFN_TextBlock_C_0TextVar<localizes>:message =  "POINTS"
make_points_canvas_generated():points_canvas_generated=

	PointsValue :text_block= text_block:
		DefaultText := PointsValueTextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 0.688870
			B := 0.000000
		DefaultTextOpacity := 1.000000
		DefaultShadowOffset := option. vector2:
			X := 2.000000
			Y := 2.000000
		DefaultShadowColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
	UEFN_TextBlock_C_0 :text_block= text_block:
		DefaultText := UEFN_TextBlock_C_0TextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
		DefaultShadowOffset := option. vector2:
			X := 2.000000
			Y := 2.000000
		DefaultShadowColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
	PointsCanvas :canvas= canvas:
		Slots := array:
			canvas_slot:
				Alignment := vector2:
					X := 0.500000
					Y := 0.000000
				Offsets := margin:
					Top := -245.081055
					Right := 128.963959
					Bottom := 41.000000
				Anchors := anchors:
					Minimum := vector2:
						X := 0.500000
						Y := 1.000000
					Maximum := vector2:
						X := 0.500000
						Y := 1.000000
				SizeToContent := true
				Widget := UEFN_TextBlock_C_0
			canvas_slot:
				Alignment := vector2:
					X := 0.500000
					Y := 0.000000
				Offsets := margin:
					Top := -301.081055
					Right := 128.963959
					Bottom := 41.000000
				Anchors := anchors:
					Minimum := vector2:
						X := 0.500000
						Y := 1.000000
					Maximum := vector2:
						X := 0.500000
						Y := 1.000000
				SizeToContent := true
				Widget := PointsValue


	PointsValue.SetShadowOpacity(0.531000)
	UEFN_TextBlock_C_0.SetShadowOpacity(0.531000)
	points_canvas_generated:
		PointsValue := PointsValue
		UEFN_TextBlock_C_0 := UEFN_TextBlock_C_0
		PointsCanvas := PointsCanvas
