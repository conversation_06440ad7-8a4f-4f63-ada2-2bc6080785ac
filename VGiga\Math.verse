using { /UnrealEngine.com/Temporary/SpatialMath }
using { /Verse.org/Random }
<#> Float to Int operators
    Usage:
        MyFloat := Float(MyInt)
        MyFloat := MyInt + MyFloat
(Number:int).F<public>()<computes>:float=
    Number * 1.0

operator'+'<public>(I:int, F:float)<computes>:float=
    I * 1.0 + F

operator'+'<public>(F:float, I:int)<computes>:float=
    F + I * 1.0

operator'-'<public>(I:int, F:float)<computes>:float=
    I * 1.0 - F

operator'-'<public>(F:float, I:int)<computes>:float=
    F - I * 1.0

operator'/'<public>(I:int, F:float)<computes>:float=
    I * 1.0 / F

operator'/'<public>(F:float, I:int)<computes>:float=
    F / (I * 1.0)

(V:vector3).Mul<public>(F:float)<computes>:vector3=
	vector3:
		X := V.X * F
		Y := V.Y * F
		Z := V.Z * F

(V:vector3).Divide<public>(F:float)<computes>:vector3=
	vector3:
		X := V.X / F
		Y := V.Y / F
		Z := V.Z / F

(Number:float).ToInt<public>()<computes><reads><decides>:int=
	Floor[Number]

Vector2<public>(X:float, Y:float)<transacts>:vector2 = vector2{X := X, Y := Y}
Vec2<public>(X:float, Y:float)<transacts>:vector2 = vector2{X := X, Y := Y}

Vector3<public>(X:float, Y:float, Z:float)<transacts>:vector3 = vector3{X := X, Y := Y, Z := Z}
Vec3<public>(X:float, Y:float, Z:float)<transacts>:vector3 = vector3{X := X, Y := Y, Z := Z}

Vector3One<public>:vector3 = vector3{X := 1.0, Y := 1.0, Z := 1.0}
Vector3Up<public>:vector3 = vector3{Z := 1.0}
Vector3Zero<public>:vector3 = vector3{}
Rotation0<public>:rotation = IdentityRotation()
Vector2One<public>:vector2 = vector2{X := 1.0, Y := 1.0}

TrOne<public>:transform = transform{
    Translation := vector3{X := 1.0, Y := 1.0, Z := 1.0}
    Rotation := IdentityRotation()
    Scale := Vector3One
}

transform_c<public>(Pos:vector3, Rot:rotation, Scale:vector3)<transacts>:transform=
	transform:
		Translation := Pos
		Rotation := Rot
		Scale := Scale
	
TrFarAway<public>:transform = transform{
    Translation := vector3{Z := -999.0}
    Rotation := IdentityRotation()
    Scale := Vector3One
}

RotationZero<public>:rotation = IdentityRotation()


(A:vector3).IsSmaller(B:vector3)<decides><transacts>:void={
    A.X < B.X
    A.Y < B.Y
    A.Z < B.Z
}



(Tr:rotation).Yaw<public>(ValueDegrees:float)<transacts>:rotation={
    Tr.RotateBy(MakeRotationFromYawPitchRollDegrees(ValueDegrees,0.0,0.0))
}
(Tr:rotation).Yaw<public>(ValueDegrees:int)<transacts>:rotation={
    Tr.RotateBy(MakeRotationFromYawPitchRollDegrees(ValueDegrees * 1.0,0.0,0.0))
}
(Tr:vector3).Yaw<public>(ValueDegrees:float)<transacts>:vector3={
    MakeRotationFromYawPitchRollDegrees(ValueDegrees,0.0,0.0).RotateVector(Tr)
}
(Tr:vector3).Yaw<public>(ValueDegrees:int)<transacts>:vector3={
    MakeRotationFromYawPitchRollDegrees(ValueDegrees * 1.0,0.0,0.0).RotateVector(Tr)
}

FloatToOddInt<public>(Value:float)<decides><transacts>:int={
    Floored := Floor[Value]
    if(Mod[Floored, 2] = 0){
        return Floored + 1
    }else{
        return Floored
    }
}
GetRandomIntExcludingInt<public>(MinRange:int, MaxRange:int, Exclude:int)<transacts>:int={
    Range := MaxRange - MinRange
    MaxValue :=
        if(Exclude < MinRange or Exclude > MaxRange){
            MaxRange
        }else{
            MaxRange - 1
        }
    if(Range < 0){
        LError()
        return 0
    }
    if(Range = 0){
        return MaxRange
    }else{
        RandomInt := GetRandomInt(MinRange, MaxValue)
        if(RandomInt = Exclude){
            return MaxRange
        }else{
            return RandomInt
        }
    }
}

IsRotationClockwise<public>(Cur:float, New:float)<decides><transacts>:void={
     var Dif : float = New - Cur;
     if(Dif < 0.0){
         set Dif += 360.0
     }
     Dif < 180.0
}


RotateIndex<public>(NewIndex:int, Length:int)<decides><transacts>:int={
	Mod[NewIndex + Length, Length]
}

IncRotateIndex<public>(Index:int, Length:int)<decides><transacts>:int=
	Mod[Index + 1, Length]

DecRotateIndex<public>(Index:int, Length:int)<decides><transacts>:int=
	Mod[Index + Length + -1, Length]
