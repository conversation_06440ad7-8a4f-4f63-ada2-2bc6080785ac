using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
using. VCustomBillboard
using. VAudio

saving_account := class(auto_creative_device, i_init_async, resetable):
	@editable ButtonDeposit:?button_device = false
	@editable ButtonWithdraw:?button_device = false

	@editable CurAmountBillboard:?custom_billboard_editable = false

	ResetEv:event() = event(){}

	
	Reset<override>():void=
		ResetEv.Signal()
	
	InitAsync<override>(Container:vcontainer)<suspends>:void=
		ActionAmount := 1000
		Resources := Container.Resolve[VResourcesSystem.resources_manager] or Err()
		Audio := Container.Resolve[audio] or Err()


		loop:
			var CurAmount:int= 0
			CurAmountBillboard.G().SetTextGreen("$" + ToString(CurAmount))
			race:
				ResetEv.Await()
				loop:
					Ag := ButtonDeposit.G().InteractedWithEvent.Await()
					Took := Resources.TryToTakeGold(Ag, ActionAmount)
					if(Took?):
						Audio.GotCashSmall.Play(Ag)
						set CurAmount += ActionAmount
						CurAmountBillboard.G().SetTextGreen("$" + ToString(CurAmount))
				loop:
					Ag := ButtonWithdraw.G().InteractedWithEvent.Await()
					if(CurAmount >= ActionAmount):
						Audio.GotCash.Play(Ag)
						set CurAmount -= ActionAmount
						CurAmountBillboard.G().SetTextGreen("$" + ToString(CurAmount))
						Resources.GiveGoldText(Ag, ActionAmount, "Savings account")
				loop:
					#Sleep(10.0)
					Sleep(0.1)
					if(CurAmount > 0):
						if:
							Val := Min(100000,CurAmount + Max(1,Floor[CurAmount * 0.00001]))
							set CurAmount = Val
							CurAmountBillboard.G().SetTextGreen("$" + ToString(CurAmount))
# loop:
# 	race:
# 		Event.Await()
# 		block:
# 			(...)
# 			sync:
# 				loop:
# 					(...)
# 				loop:
# 					(...)
# 				loop:
# 					(...)