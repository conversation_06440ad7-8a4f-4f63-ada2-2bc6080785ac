﻿using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/UI
using. /Fortnite.com/UI
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
time_rewards_timer_generated := class:
	TimerText:text_block
	RewardImage:texture_block
	Overlay_48:overlay
	TimeRewardsTimer:canvas

TimerTextTextVar<localizes>:message =  "5:99"
make_time_rewards_timer_generated():time_rewards_timer_generated=

	TimerText :text_block= text_block:
		DefaultText := TimerTextTextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
		DefaultShadowOffset := option. vector2:
			X := 2.000000
			Y := 2.000000
		DefaultShadowColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
		DefaultJustification := text_justification.Center
	RewardImage :texture_block= texture_block:
		DefaultImage := StoreGiftPacks.TwoDAssets.PNG.Gold_05
		DefaultDesiredSize := vector2:
			X := 1024.000000
			Y := 1024.000000
	Overlay_48 :overlay= overlay:
		Slots := array:
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Left
				VerticalAlignment := vertical_alignment.Top
				Widget := RewardImage
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Center
				VerticalAlignment := vertical_alignment.Center
				Widget := TimerText
	TimeRewardsTimer :canvas= canvas:
		Slots := array:
			canvas_slot:
				Offsets := margin:
					Left := 16.000000
					Top := -433.081055
					Right := 133.103668
					Bottom := 133.103668
				Anchors := anchors:
					Minimum := vector2:
						X := 0.000000
						Y := 1.000000
					Maximum := vector2:
						X := 0.000000
						Y := 1.000000
				SizeToContent := false
				Widget := Overlay_48


	TimerText.SetShadowOpacity(0.846000)
	time_rewards_timer_generated:
		TimerText := TimerText
		RewardImage := RewardImage
		Overlay_48 := Overlay_48
		TimeRewardsTimer := TimeRewardsTimer
