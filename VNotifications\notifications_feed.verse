using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 


notifications_feed_devic<public> := class(auto_creative_device, i_init_async, i_init_per_player_async):
	@editable NotifAudioPlayer:?audio_player_device = false
	
	PlayerMap:map_agent_notifications_feed_player_ui = map_agent_notifications_feed_player_ui{}

	InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
		Print("notifications_feed InitPlayerAsync"); Sleep(0.0)
		FeedPlayerUi := notifications_feed_player_ui_c()
		PlayerMap.Set(Agent, FeedPlayerUi)
		Agent.AddToPlayerUi(FeedPlayerUi.Canvas)
		# PlayerUi.AddWidget(FeedPlayerUi.Canvas)

		PlayerRemoved.Await()

		if(player[Agent].IsActive[]):
			Agent.RemoveFromPlayerUi(FeedPlayerUi.Canvas)
		PlayerMap.Remove(Agent)
		# else:
		# 	LError()

	InitAsync<override>(Container:vcontainer)<suspends>:void=
		loop:
			for(PlayerFeed : PlayerMap.DataMap):
				Sleep(0.0)

				Time := GetSimulationElapsedTime()
				
				# Hide expired slots
				for(I := 0..PlayerFeed.FeedSlots.Length-1
					# CurId := -I
					FeedSlot := PlayerFeed.FeedSlots[I]
					Time > FeedSlot.HideAtTime
				):
					FeedSlot.Hide()
					# if:
					# 	Val := PlayerFeed.TakenFeedSlots.RemoveElement[CurId]
					# 	set PlayerFeed.TakenFeedSlots = Val
					# set PlayerFeed.FreeFeedSlots = array{FeedSlot} + PlayerFeed.FreeFeedSlots
					
					
			Sleep(0.1)

	Show<public>(Agent:agent, Icon:message, Text:message, ?Color:color=NamedColors.White):void=
		ShowShared(Agent, Text, ?Color := Color, ?IconText := option. Icon)

	Show<public>(Agent:agent, Icon:texture, Text:message, ?Color:color=NamedColors.White):void=
		ShowShared(Agent, Text, ?Color := Color, ?IconTexture := option. Icon)
		
	ShowShared(Agent:agent, Text:message, ?Color:color=NamedColors.White, ?IconTexture:?texture = false, ?IconText:?message = false):void=
		if(PlayerFeed := PlayerMap.Get[Agent]):
			var MFreeSlot :?feed_player_ui_slot= false
			if(var FreestSlot :feed_player_ui_slot= PlayerFeed.FeedSlots[0]):
				Time := GetSimulationElapsedTime()

				# find free slot
				var I:int = 0
				loop:
					if(Slot := PlayerFeed.FeedSlots[I]):
						if(Time > Slot.HideAtTime):
							set MFreeSlot = option. Slot
							break
						else if(FreestSlot.HideAtTime > Slot.HideAtTime):
							set FreestSlot = Slot
						
					set I += 1
					if(I >= PlayerFeed.FeedSlots.Length):
						break

				FreeSlot := MFreeSlot? or FreestSlot
				if(Icon := IconTexture?):
					FreeSlot.SetIcon(Icon)
				if(Icon := IconText?):
					FreeSlot.SetIcon(Icon)
				FreeSlot.Show(Text, 2.0, Color)
				if(Audio := NotifAudioPlayer?):
					Audio.Play(Agent)
