using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using.  VGiga 
<#

VAR1_pool_devic<public> := class(creative_device):
    @editable Area:area_box_devic = area_box_devic{}

    var MInited:?VAR1_pool = false

    GetPool<public>()<transacts>:VAR1_pool=
        if(Inited := MInited?):
            Inited
        else:
            Inited := VAR1_pool:
                Area := Area
                D := Self
            .Init()
            set MInited = option. Inited
            Inited


VAR1_pool<public> := class(class_interface):
    var FreeCreativeDevices : []VAR1 = array{}
    var AllCreativeDevices : []VAR1 = array{}
    Area<public>:area_interface
    D<public>:creative_device

    var Inited:logic = false
    
    Init<public>()<transacts>:VAR1_pool=
        if(Inited?):
            Self
        else:
            set Inited = true
            set FreeCreativeDevices = for(Obj : D.GetDevicesInArea(VAR1, Area)):
                Obj
            set AllCreativeDevices = FreeCreativeDevices
            Self

    GetAllFree<public>()<transacts>:[]VAR1=
        if(not Inited?):
            LError()
        FreeCreativeDevices

    GetAll<public>()<transacts>:[]VAR1=
        if(not Inited?):
            LError()
        AllCreativeDevices
        
    Rent<public>()<decides><transacts>:VAR1=
        if(CreativeDevice := FreeCreativeDevices[0]
            Val := FreeCreativeDevices.RemoveElement[0]
            set FreeCreativeDevices = Val
        ):
            return CreativeDevice
        else:
            FailError[]
            return VAR1{}

    RentDisposable<public>()<decides><transacts>:pooled_VAR1=
        Device := Rent[]
        pooled_VAR1:
            Device := Device
            MyPool := Self

    Return<public>(CreativeDevice:VAR1):void=
        if. CreativeDevice.TeleportTo[Area.GetCenterPoint(), rotation{}]
        set FreeCreativeDevices += array. CreativeDevice


pooled_VAR1<public> := class(i_disposable):
    MyPool<public>:VAR1_pool
    Device<public>:VAR1

    Dispose<override>():void=
        MyPool.Return(Device)
#>

    