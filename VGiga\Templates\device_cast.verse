#prefix device_cast

using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using {/Verse.org/Random}
using { VGiga }
using { VGiga.Defaults }
DEVICETYPE_devic<public> := class(creative_device):
	
	Init<public>(Container:player_events_manager_devic
	):DEVICETYPE=
		Manager := DEVICETYPE:
			D := Self
		.Init()
		Container.Register(Manager)
		Manager


DEVICETYPE<public> := class(i_init_per_player_async):
	D<public>:DEVICETYPE_devic
	var PlayerDataMap : map_agent_p_data_test = map_agent_p_data_test{}

	Init():DEVICETYPE=
		Self
	
	InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
		PlayerDataMap.Set(Agent, p_data_test{})
		PlayerRemoved.Await()
		PlayerDataMap.Remove(Agent)