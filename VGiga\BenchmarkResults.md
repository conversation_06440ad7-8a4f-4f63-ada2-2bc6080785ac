
		profile("Get from device_tag1"):
			Tracks := GetDevices(race_track)
		Sleep(0.0)
		profile("Get from device_tag2"):
			Tracks := GetDevices(race_track)
		Sleep(0.0)
		profile("Get from device_tag2"):
			Tracks := GetDevices(race_track)
		Sleep(0.0)

		profile("Get from normal tag1"):
			Tracks := GetDevicesWithNormalTag(race_track_cast, race_track_tag{})
		Sleep(0.0)
		profile("Get from normal tag2"):
			Tracks := GetDevicesWithNormalTag(race_track_cast, race_track_tag{})
		Sleep(0.0)
		profile("Get from normal tag2"):
			Tracks := GetDevicesWithNormalTag(race_track_cast, race_track_tag{})
		Sleep(0.0)
			
		profile("Get from comparable tag1"):
			Tracks := GetDevicesWithTag(race_track_cast, race_track_comparable_tag{})
		Sleep(0.0)
		profile("Get from comparable tag2"):
			Tracks := GetDevicesWithTag(race_track_cast, race_track_comparable_tag{})
		Sleep(0.0)
		profile("Get from comparable tag2"):
			Tracks := GetDevicesWithTag(race_track_cast, race_track_comparable_tag{})
		Sleep(0.0)

(0,0) 2024.11.01-11.18.54:561:[VerseProfile] Get from device_tag1 0.113800 ms
(0,0) 2024.11.01-11.18.54:582:[VerseProfile] Get from device_tag2 0.095900 ms
(0,0) 2024.11.01-11.18.54:615:[VerseProfile] Get from device_tag2 0.098200 ms
(0,0) 2024.11.01-11.18.54:648:[VerseProfile] Get from normal tag1 0.058000 ms
(0,0) 2024.11.01-11.18.54:682:[VerseProfile] Get from normal tag2 0.048600 ms
(0,0) 2024.11.01-11.18.54:715:[VerseProfile] Get from normal tag2 0.053800 ms
(0,0) 2024.11.01-11.18.54:749:[VerseProfile] Get from comparable tag1 0.067200 ms
(0,0) 2024.11.01-11.18.54:783:[VerseProfile] Get from comparable tag2 0.059100 ms
(0,0) 2024.11.01-11.18.54:816:[VerseProfile] Get from comparable tag2 0.054800 ms

Conclusion use WithNormaTag, that is new tag_castable



StartBankArr :arr_char= arr_char:
    Arr := "Start your first Bank!"

InitPlayer<override>(Agent:agent):void=
    profile("string"):
        for(X:=1..1000):
            Loc.G(Agent, "Start your first Bank!")

    profile("arrchar"):
        for(X:=1..1000):
            Loc.G(Agent, StartBankArr)

Not much difference