using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 

(Items:[]VAR1).SortVAR1<public>(IsAscending:logic, Comparer:type{_(:VAR1, :VAR1)<transacts>:int})<transacts>:[]VAR1 = 
    if (Items.Length > 1, Pivot := Items[Floor(Items.Length/2)]):
        Left := for(Item : Items, Comparer(Item, Pivot) < 0) do Item 
        Middle := for(Item : Items, Comparer(Item, Pivot) = 0) do Item
        Right := for(Item : Items, Comparer(Item, Pivot) > 0) do Item
        if(IsAscending?):
            Left.SortVAR1(IsAscending, Comparer) + Middle + Right.SortVAR1(IsAscending, Comparer)
        else: 
            Right.SortVAR1(IsAscending, Comparer) + Middle + Left.SortVAR1(IsAscending, Comparer)
    else:    
        Items 
 
# CompareInts(A : int, B : int)<transacts>: int = 
#     if(A < B) then -1
#     else if(A > B) then 1
#     else 0