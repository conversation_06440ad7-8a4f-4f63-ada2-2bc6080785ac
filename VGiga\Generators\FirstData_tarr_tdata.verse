# (Arr:[]VAR1).FirstData<public>(Check:type{_(:VAR1,:VAR2)<decides><transacts>:void}, Data:VAR2)<decides><transacts>:VAR1={
# 	var Ret:?VAR1 = false
# 	N := Arr.Length
# 	var X :int= 0 
# 	loop{
# 		if(Item := Arr[X], Check[Item, Data]){
# 			set Ret = option. Item
# 			break
# 		}
# 		set X += 1
# 		if(X >= N){
# 			break
# 		}
# 	}
# 	if(Item := Ret?){
# 		Item
# 	}else{
# 		Fail[]
# 		Err()
# 	}
# }