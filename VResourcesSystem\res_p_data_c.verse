using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }

# RebirthAdditiveMultiplierPerLevel:float = 0.10
# StartMultiplier :float= 1.0

res_p_data<public> := class<internal>:
    Agent<public>:agent
    Widgets:resources_manager_ui_generated
    
    Save<public>:save_data_var

    CurWood<private> :observable_value_int= observable_value_int:
        Value := 0
    var CurFans : int = 0
    GoldPerSec<public> : observable_value_int= observable_value_int:
        Value := 0
    var FansPerSec : int = 0

    var PetsTotalLevel : int = 0
    var UiVisible : logic
    var BoostMulti:float = 1.0

    var<private> BonusMultiplier<public> : float
    var<private> CachedFinalMultiplier<public> :float = 0.0

    ShowTimeDaily<public>():void=
        Widgets.OverlayTimeDaily.Show()
        
    HideTimeDaily<public>():void=
        Widgets.OverlayTimeDaily.Collapse()

    UpdateTimeDaily<public>(Minutes:int):void=
        Widgets.TextTimeDaily.SetText("{Minutes}min".ToMessage())
        
    UpdateTimeDailyMsg<public>(Msg:message):void=
        Widgets.TextTimeDaily.SetText(Msg)

    GetGold<public>()<transacts>:int=
        Save.Gold.Get()
    GetPoints<public>()<transacts>:int=
        Save.Points.Get()

    # GetTokens()<transacts>:int=
    # 	Tokens.Get()
    # TakeTokens(Value:int):void=
    # 	Tokens.Add(-Value)

    GetWood()<transacts>:int=
        Save.Wood.Get()

    TakeGold(Value:int):void=
        Save.Gold.Add(-Value)
    
    GetGoldChangeEvent<public>()<transacts>:event(int)=
        Save.Gold.ChangedEvent
    
    SetGold(Val:int):void=
        Save.Gold.Set(Val)

    AddGold(Val:int):void=
        Save.Gold.Add(Val)

    SetWood(Val:int):void=
        CurWood.Set(Val)

    RecalculateMultiplier()<transacts>:void=
        set CachedFinalMultiplier = 1.0 
        # set CachedFinalMultiplier = 1.0 + BonusMultiplier + GetMultiplierFromRebirth()
        # set CachedFinalMultiplier = StartMultiplier + BonusMultiplier + RebirthLevel * RebirthAdditiveMultiplierPerLevel
        # LPrint(" settings cached multi: {CachedFinalMultiplier} = {1.0} + BonusMultiplier {BonusMultiplier} + GetMultiplierFromRebirth() {GetMultiplierFromRebirth()}")

    RebirthMath(Level:int)<transacts>:float=
        var x:float = 0.1
        var sum:float = 0.0

        if(Level = 0):
            return 0.0

        for(I:= 0..Level-1):
            set sum = sum + x
            set x = x * 0.9 + 0.0005
        sum

    GetMultiplierFromRebirth()<transacts>:float=
        RebirthMath(Save.Rebirths)

    GetNextRebirthMultiplierPct()<transacts>:int=
        X := Save.Rebirths

        FloatVal := if(X = 0):
            RebirthMath(X+1)
        else: 
            RebirthMath(X+1) - RebirthMath(X)

        if(Val := Round[FloatVal * 100.0]):
            Val
        else:
            1

    # GetMultiplierFromRebirth():float=
    # 	X := RebirthLevel
    # 	Log(90.0, X + 10.0) - 0.431

    # IncreaseRebirthLevel<public>()<transacts>:void=
    # 	set Save.Rebirths += 1
    # 	RecalculateMultiplier()

    SetBonusMultiplier<public>(Val:float)<transacts>:void=
        set BonusMultiplier = Val
        RecalculateMultiplier()

agent_with_res_p_data := class():
    Agent:agent
    Data:res_p_data
    
#gen
#map_t1_t2_priv
#agent
#res_p_data
#id-99895e3b-fca5-4967-9a62-7b0e2be3908b
map_agent_res_p_data := class:
	var DataMap:[agent]res_p_data = map{}
	var MDataSetEvent:?event(tuple(agent, res_p_data)) = false

	Get(Key:agent)<decides><transacts>:res_p_data=
		DataMap[Key]

	GetErr(Key:agent)<decides><transacts>:res_p_data=
		if(Data := DataMap[Key]):
			Data
		else:
			FailError[]
			Err()

	GetMap()<transacts>:[agent]res_p_data=
		DataMap

	GetOrAwait<public>(Key:agent)<suspends>:res_p_data=
		if(Data := DataMap[Key]):
			Data
		else:
			if(DataSetEvent := MDataSetEvent?):
				var Return:?res_p_data = false
				loop:
					Data := DataSetEvent.Await()
					if(Data(0) = Key):
						set Return = option. Data(1)
						break
				Return.G()
				# DataSetEvent.AwaitForData(Key)
			else:
				Ev := event(tuple(agent, res_p_data)){}
				set MDataSetEvent = option. Ev
				var Return:?res_p_data = false
				# Ev.AwaitForData(Key)
				loop:
					Data := Ev.Await()
					if(Data(0) = Key):
						set Return = option. Data(1)
						break
				Return.G()
			

	Set(Key:agent, Data:res_p_data):void=
		if. set DataMap[Key] = Data
		if(DataSetEvent := MDataSetEvent?):
			DataSetEvent.Signal((Key, Data))

	Remove(Key:agent)<transacts>:void=
		set DataMap = DataMap.WithRemoved(Key)
#id-99895e3b-fca5-4967-9a62-7b0e2be3908b

# map_agent_p_data<public> := class{
# 	DataMap<public> : [agent]p_data = map{}
# 	DataCreatedEvent:event(tuple(agent, p_data)) = event(tuple(agent, p_data)){}

# 	Get<public>(Player:agent)<decides><transacts>:p_data={
# 		if(Data := DataMap[Player]){
# 			Data
# 		}else{
# 			FailError[]
# 			Err()
# 		}
# 	}

# 	GetOrAwait<public>(Player:agent)<suspends>:p_data={
# 		if(Data := DataMap[Player]){
# 			Data
# 		}else{
# 			DataCreatedEvent.AwaitForData(Player)
# 		}
# 	}


# 	Add<public>(Player:agent, Data:p_data):map_agent_p_data={
# 		Created := map_agent_p_data{
# 			DataMap := ConcatenateMaps(DataMap, map{Player => Data})
# 			DataCreatedEvent := DataCreatedEvent
# 		}
# 		DataCreatedEvent.Signal((Player, Data))
# 		Created
# 	}

# 	Remove<public>(Player:agent)<transacts>:map_agent_p_data={
# 		map_agent_p_data{
# 			DataMap := DataMap.WithRemoved(Player)
# 			DataCreatedEvent := DataCreatedEvent
# 		}
# 	}
# }