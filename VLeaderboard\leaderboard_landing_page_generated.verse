using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/UI
using. /Fortnite.com/UI
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
leaderboard_landing_page_generated := class:
	OkButton:button_loud
	TextVerification:text_block
	TextPoints:text_block
	Image_63ColorBlock:color_block
	Overlay_49:overlay
	LeaderboardLandingPage:canvas

OkButtonTextVar<localizes>:message = "OK"
TextVerificationTextVar<localizes>:message =  "Verification Code:\r\n9999"
TextPointsTextVar<localizes>:message =  "Points: 9999"
make_leaderboard_landing_page_generated():leaderboard_landing_page_generated=

	OkButton :button_loud= button_loud:
		DefaultText := OkButtonTextVar
	TextVerification :text_block= text_block:
		DefaultText := TextVerificationTextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
		DefaultJustification := text_justification.Center
	TextPoints :text_block= text_block:
		DefaultText := TextPointsTextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
	Image_63ColorBlock :color_block= color_block:
		DefaultColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
		DefaultOpacity := 0.218000
		DefaultDesiredSize := vector2:
			X := 32.000000
			Y := 32.000000
	Overlay_49 :overlay= overlay:
		Slots := array:
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := Image_63ColorBlock
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Center
				VerticalAlignment := vertical_alignment.Center
				Widget := TextPoints
			overlay_slot:
				Padding := margin:
					Top := 70.000000
				HorizontalAlignment := horizontal_alignment.Center
				VerticalAlignment := vertical_alignment.Center
				Widget := TextVerification
			overlay_slot:
				Padding := margin:
					Bottom := 30.000000
				HorizontalAlignment := horizontal_alignment.Center
				VerticalAlignment := vertical_alignment.Bottom
				Widget := OkButton
	LeaderboardLandingPage :canvas= canvas:
		Slots := array:
			canvas_slot:
				Alignment := vector2:
					X := 0.500000
					Y := 0.500000
				Offsets := margin:
					Right := 813.076721
					Bottom := 511.181824
				Anchors := anchors:
					Minimum := vector2:
						X := 0.500000
						Y := 0.500000
					Maximum := vector2:
						X := 0.500000
						Y := 0.500000
				SizeToContent := false
				Widget := Overlay_49


	leaderboard_landing_page_generated:
		OkButton := OkButton
		TextVerification := TextVerification
		TextPoints := TextPoints
		Image_63ColorBlock := Image_63ColorBlock
		Overlay_49 := Overlay_49
		LeaderboardLandingPage := LeaderboardLandingPage
