
<#
using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using {/Verse.org/Random}
using {Giga}
using {ZInterfaces}

MANAGERTYPE_manager<public> := class(class_interface, i_init_per_player, i_removable_per_player, i_init_everytime_per_player){
    var PlayerDataMap : player_data_map = player_data_map{}

    Init<public>():MANAGERTYPE_manager={
        Self
    }

    InitPlayer<override>(Agent:agent):void={
        Data := player_data{}
        PlayerDataMap.Set(Agent, Data)
    }

    InitEverytimePlayer<override>(Agent:agent):void={
    }

    RemovePlayer<override>(Agent:agent):void={
        PlayerDataMap.Remove(Agent)
    }
}

player_data := class{
    var CurrentMusic : ?radio_device = false
    var CurrentMusicIndex : int = -1
    var IsPlayingMusic : logic = false
}

player_data_map := class{
    var DataMap : [agent]player_data = map{}

    Get<public>(Player:agent)<decides><transacts>:player_data={
        if(Data := DataMap[Player]){
            Data
        }else{
            FailError[]
            Err()
        }
    }

    Set<public>(Player:agent, Data:player_data):void={
        if{set DataMap[Player] = Data}
    }

    Remove<public>(Player:agent):void={
        set DataMap = DataMap.WithRemoved(Player)
    }
}

#>