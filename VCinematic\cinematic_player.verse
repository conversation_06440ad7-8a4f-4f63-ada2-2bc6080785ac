using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
using. VGiga.Pool
using. VCoop

cinematic_player<public> := class(auto_creative_device, i_init_async, i_init_per_player_async):
    @editable MActivatingVolume:?volume_device = false
    @editable_container:
        AllowReordering := true
    CinematicSteps:[]cinematic_step = array{}
    @editable PlayForAllInstantlyInCoop:logic = false
    var Loc:i_localization = empty_i_localization{}

    var PlayerEvents :?player_events_manager_devic= false
    var Coop :?coop= false
    var Config :?cinematic_players_config= false

    ManualPlayEv:event(agent) := event(agent){}
    CinematicEndedEvent<public>:event(agent) := event(agent){}

    Play<public>(Agent:agent):void=
        ManualPlayEv.Signal(Agent)

    InitAsync<override>(Container:vcontainer)<suspends>:void=
        set Config = Container.ResolveOp[cinematic_players_config] or Err()
        set Coop = Container.ResolveOp[coop] or Err()
        set PlayerEvents = Container.ResolveOp[player_events_manager_devic] or Err()
        set Loc = Container.Resolve_i_localization()

    InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
        Print("cinematic_player InitPlayerAsync"); Sleep(0.0)
        race:
            PlayerRemoved.Await()
            loop:
                race:
                    block:
                        if(ActivatingVolume := MActivatingVolume?):
                            ActivatingVolume.AgentEntersEvent.AwaitFor(Agent)
                        else:
                            Sleep(Inf)
                    ManualPlayEv.AwaitFor(Agent)

                if(Admin := Coop.G().CoopAdmin?, PlayForAllInstantlyInCoop?):
                    if(Admin = Agent):
                        spawn. PlayCinematicsForNonAdmins(Admin)
                        PlayCinematics(Admin)
                    else:
                        ManualPlayEv.Signal(Admin)
                        Sleep(0.1) # Make sure this finished for admin first (for ex. to claim house first)

                        for(Player->Data:PlayerEvents.G().RegisteredPlayers):
                            if(Player <> Admin, Player <> Agent):
                                ManualPlayEv.Signal(Player)
                        PlayCinematics(Agent)
                else:
                    PlayCinematics(Agent)

    PlayCinematicsForNonAdmins(Admin:agent)<suspends>:void=
        Sleep(0.1)
        for(Player->Data:PlayerEvents.G().RegisteredPlayers, Player <> Admin):
            ManualPlayEv.Signal(Player)


    PlayCinematics(Agent:agent)<suspends>:void=
        CinematicsToDispose := list_pooled_editable_cinematic_sequence_device{}
        race:
            PlayerEvents.G().PlayerRemovedOncePerGame.AwaitFor(Agent) # TODO Handle player deaths
            block:
                for(Step:CinematicSteps):
                    PlayCinematic(Agent, Step, CinematicsToDispose)
                    if(Next := Step.MNextCinematicPlayer?):
                        Next.PlayCinematics(Agent)

        for(Dispo:CinematicsToDispose.GetAll()):
            Dispo.Device.GoToEndAndStop(Agent)
            Dispo.Dispose()

        # CinematicsToDispose.Clear() who cares not needed

        CinematicEndedEvent.Signal(Agent)

    PlayCinematic(Agent:agent, Step:cinematic_step, CinematicsToDispose:list_pooled_editable_cinematic_sequence_device)<suspends>:void=
        
        if(StartAnalytic := Step.MStartAnalytic?):
            StartAnalytic.Submit(Agent)

        if(DelayStart := Step.MDelayStart?):
            Sleep(DelayStart)

        if(TeleporterForPlayer := Step.MTeleporterForPlayer?):
            TeleporterForPlayer.Teleport(Agent)

        if(HudMessage := Step.MHudMessage?):
            if(Msg := Step.MHudMessageTextToTranslate?):
                TrMsg := Loc.G(Agent, Msg)
                HudMessage.Show(Agent, TrMsg.ToMessage())
            else:
                HudMessage.Show(Agent)

        if(ChannelToTransmit := Step.MChannelToTransmit?):
            ChannelToTransmit.Transmit(option. Agent)

        if(Step.MHidePlayer??, Char := Agent.GetFortCharacterActive[]):
            Char.Hide()

        if(Step.MUnhidePlayer??, Char := Agent.GetFortCharacterActive[]):
            Char.Show()
            
        if(Step.MFreezePlayer??, Char := Agent.GetFortCharacterActive[]):
            Char.PutInStasis(stasis_args:
                AllowTurning := false
                AllowFalling := false
                AllowEmotes := false
            )
        if(Step.MUnfreezePlayer??, Char := Agent.GetFortCharacterActive[]):
            Char.ReleaseFromStasis()
        
        var MCinematic:?pooled_editable_cinematic_sequence_device = false
            
        if(not Config.G().SkipCinematics?
            CinematicPool := Step.MCinematicPool?
            Cinematic := CinematicPool.RentDisposable[]
        ):
            set MCinematic = option. Cinematic
            CinematicsToDispose.Add(Cinematic)
            Cinematic.Device.SetPlaybackTime(Step.CinematicPlaybackTime)
            Cinematic.Device.SetPlayRate(Step.CinematicPlayRate)
            LPrint("Cinematic.Device.Play(Agent)")
            Cinematic.Device.Play(Agent)

        if((MCinematic? and Step.WaitForCinematicEnd?) or
            Step.MWaitForInputClick? or
            Step.MWaitForVolumeEnter? or
            Step.MWaitForTimeout?
        ):
            race:
                block:
                    if(Cinematic := MCinematic?, Step.WaitForCinematicEnd?):
                        Cinematic.Device.StoppedEvent.Await()
                    else:
                        Sleep(Inf)
                block:
                    if(WaitForInputClick := Step.MWaitForInputClick?):
                        WaitForInputClick.Register(Agent)
                        WaitForInputClick.PressedEvent.AwaitFor(Agent)
                    else:
                        Sleep(Inf)
                block:
                    if(WaitForVolumeEnter := Step.MWaitForVolumeEnter?):
                        WaitForVolumeEnter.AgentEntersEvent.AwaitFor(Agent)
                    else:
                        Sleep(Inf)
                Sleep(Step.MWaitForTimeout? or Inf)
            
        if(WaitForInputClick := Step.MWaitForInputClick?):
            WaitForInputClick.Unregister(Agent)

        if(HudMessage := Step.MHudMessage?):
            HudMessage.Hide(Agent)

        # if(Cinematic := MCinematic?):
        # 	Sleep(0.0)
        # 	Cinematic.Device.GoToEndAndStop(Agent)
        # 	Sleep(0.0)
        
        if(EndAnalytic := Step.MEndAnalytic?):
            EndAnalytic.Submit(Agent)

        
                        

            
            

                    
