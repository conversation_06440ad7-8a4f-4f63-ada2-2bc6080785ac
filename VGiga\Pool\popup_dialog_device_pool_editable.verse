#gen
#pool_editable
#popup_dialog_device
#id-21bfbbf7-ea32-4841-8c31-fc7c7851c8fe
using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using.  VGiga
	
popup_dialog_device_pool_editable<public> := class<concrete>:
	@editable var FreeCreativeDevices : []popup_dialog_device = array{}

	GetAllFree<public>():[]popup_dialog_device=
		FreeCreativeDevices

	Rent<public>()<decides><transacts>:popup_dialog_device=
		if(CreativeDevice := FreeCreativeDevices[0]
			Val := FreeCreativeDevices.RemoveElement[0]
			set FreeCreativeDevices = Val
		):
			return CreativeDevice
		else:
			FailError[]
			return popup_dialog_device{}

	RentDisposable<public>()<decides><transacts>:pooled_editable_popup_dialog_device=
		Device := Rent[]
		pooled_editable_popup_dialog_device:
			Device := Device
			MyPool := Self

	Return<public>(CreativeDevice:popup_dialog_device):void=
		set FreeCreativeDevices += array. CreativeDevice


pooled_editable_popup_dialog_device<public> := class(i_disposable):
	MyPool<public>:popup_dialog_device_pool_editable
	Device<public>:popup_dialog_device

	Dispose<override>():void=
		MyPool.Return(Device)

	
#id-21bfbbf7-ea32-4841-8c31-fc7c7851c8fe