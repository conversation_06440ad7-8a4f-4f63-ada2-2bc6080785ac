using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI} 
using { VGiga }
using { VHouseSystem }
using. VCreaturesSystem
using. VResourcesSystem
using. VQuestSystem

gm_xp_devic := class(auto_creative_device, i_init_async, i_init_per_player_async):
	# @editable FloorCleanAccolade : accolades_device = accolades_device{}
	@editable HouseClaimAccolade : accolades_device = accolades_device{}
	# @editable HouseResetAccolade : accolades_device = accolades_device{}
	@editable UnlockableAccolades : []accolades_device = array{}
	@editable QuestFinishedAccolade2 : ?accolades_device = false
	@editable PiggyBreakAccolade2 : ?accolades_device = false

	# @editable MobKillAccolade : accolades_device = accolades_device{}

	# @editable TimeXpTrackers:[]time_xp_track = array{}
	# @editable TimeXpDeviceTrackers:[]tracker_device = array{}
	var MResourcesManager:?resources_manager = false
	# var HousesManager:?houses_manager = false

	InitAsync<override>(Container:vcontainer)<suspends>:void=
		PiggyBreakAccolade2.G()
		QuestFinishedAccolade2.G()
		set MResourcesManager = Container.ResolveOp[resources_manager] or Err()
		Houses := Container.Resolve[houses_manager] or Err()
		Quests := Container.Resolve[quest_system] or Err()
		PiggyBrokenEvs := for(Piggy:Container.ResolveAllErr(VMinigames.use_button_for_gold)):
			Piggy.PiggyBrokenEv
		# MinigameFloorCleaning.FloorCleanedEvent.Subscribe1(OnFloorCleaned)
			# OnFloorCleaned(Agent:agent):void=
			# 	FloorCleanAccolade.Award(Agent)
		sync:
			# OnCreatureEliminatedByAgentEvent(Agent:agent):void=
			# 	MobKillAccolade.Award(Agent)
			# loop:
			# 	Agent := Houses.HouseResetEvent.Await()
			# 	HouseResetAccolade.Award(Agent)
			loop:
				Agent := PiggyBrokenEvs.Await()
				PiggyBreakAccolade2.G().Award(Agent)
			loop:
				Agent := Quests.QuestCompletedEv.Await().Agent
				QuestFinishedAccolade2.G().Award(Agent)
			loop:
				Agent := Houses.HouseClaimedEvent.Await()
				HouseClaimAccolade.Award(Agent)
			loop:
				Data := Houses.UnlockableUnlockedEvent.Await()
				if(not Data.WasLoaded?
					Ac := UnlockableAccolades.GetRandom[]
				):
					# LPrint("award exp")
					Ac.Award(Data.Agent)
				else:
					# LPrint("no exp")
				
				# if(UnlockableOptions.FansCost = 0
				#     Percent := UnlockableOptions.GoldCost * 1.0 / MaxGoldCost * 1.0
				#     AccodaleIndex := Floor[Percent * GoldUnlockableAccolades.Length * 1.0]
				#     Accolade := GoldUnlockableAccolades[AccodaleIndex]
				# ){
				#     Accolade.Award(Agent)
				# }else if(UnlockableOptions.GoldCost = 0
				#     Percent := UnlockableOptions.FansCost * 1.0 / MaxFansCost * 1.0
				#     AccodaleIndex := Floor[Percent * FansUnlockableAccolades.Length * 1.0]
				#     Accolade := FansUnlockableAccolades[AccodaleIndex]
				# ){
				#     Accolade.Award(Agent)
				# }

		# CreatureSystem.CreatureEliminatedByAgentEvent.Subscribe1(OnCreatureEliminatedByAgentEvent)

	InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void={}
		# race:
		# 	PlayerRemoved.Await()
			# block:
			# 	# if(FirstTracker := TimeXpTrackers[0]
			# 	# var LastTimeTracked :float= 0.0
			# 	for(Tracker:TimeXpTrackers):
			# 		Tracker.Device.SetDescriptionText("${Tracker.GoldReward} + XP Reward".ToMessage())
			# 		Tracker.Device.Assign(Agent)
			# 		Tracker.Device.CompleteEvent.AwaitFor(Agent)

			# 		# Sleep(Tracker.TimeToTrackInMins * 60.0 - LastTimeTracked)
			# 		# set LastTimeTracked = Tracker.TimeToTrackInMins * 60.0
			# 		Tracker.AccoladeDevice.Award(Agent)
			# 		if(RM := MResourcesManager?):
			# 			RM.GiveGoldText(Agent, Tracker.GoldReward, "Big Reward!")
					


time_xp_track := class<concrete>:
	# @editable TimeToTrackInMins:float = 5.0
	@editable AccoladeDevice:accolades_device = accolades_device{}
	@editable Device:tracker_device = tracker_device{}
	@editable GoldReward:int = 0
	
	