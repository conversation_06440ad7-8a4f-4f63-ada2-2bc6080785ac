#gen
#pool_devic
#billboard_device
#id-8c8de2e5-6fdd-417e-ba3d-d1fe93ab91d8
using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using.  VGiga 

billboard_device_pool_devic<public> := class(creative_device):
    @editable Area:area_box_devic = area_box_devic{}

    var MInited:?billboard_device_pool = false

    GetPool<public>()<transacts>:billboard_device_pool=
        if(Inited := MInited?):
            Inited
        else:
            Inited := billboard_device_pool:
                Area := Area
                D := Self
            .Init()
            set MInited = option. Inited
            Inited


billboard_device_pool<public> := class(class_interface):
    var FreeCreativeDevices : []billboard_device = array{}
    var AllCreativeDevices : []billboard_device = array{}
    Area<public>:area_interface
    D<public>:creative_device

    var Inited:logic = false
    
    Init<public>()<transacts>:billboard_device_pool=
        if(Inited?):
            Self
        else:
            set Inited = true
            set FreeCreativeDevices = for(Obj : D.GetDevicesInArea(billboard_device, Area)):
                Obj
            set AllCreativeDevices = FreeCreativeDevices
            Self

    GetAllFree<public>()<transacts>:[]billboard_device=
        if(not Inited?):
            LError()
        FreeCreativeDevices

    GetAll<public>()<transacts>:[]billboard_device=
        if(not Inited?):
            LError()
        AllCreativeDevices
        
    Rent<public>()<decides><transacts>:billboard_device=
        if(CreativeDevice := FreeCreativeDevices[0]
            Val := FreeCreativeDevices.RemoveElement[0]
            set FreeCreativeDevices = Val
        ):
            return CreativeDevice
        else:
            FailError[]
            return billboard_device{}

    RentDisposable<public>()<decides><transacts>:pooled_billboard_device=
        Device := Rent[]
        pooled_billboard_device:
            Device := Device
            MyPool := Self

    Return<public>(CreativeDevice:billboard_device):void=
        if. CreativeDevice.TeleportTo[Area.GetCenterPoint(), rotation{}]
        set FreeCreativeDevices += array. CreativeDevice


pooled_billboard_device<public> := class(i_disposable):
    MyPool<public>:billboard_device_pool
    Device<public>:billboard_device

    Dispose<override>():void=
        MyPool.Return(Device)

    
#id-8c8de2e5-6fdd-417e-ba3d-d1fe93ab91d8