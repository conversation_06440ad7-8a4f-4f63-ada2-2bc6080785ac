using { /Fortnite.com/Devices }
using { /Verse.org/Simulation }
using. VGiga

cinematic_players_config<public> := class(class_interface):
    SkipCinematics<public> : logic = false

(Container:vcontainer).Resolve_cinematic_players_config<public>()<transacts>:cinematic_players_config=
	if(Resolved := Container.ResolveNoPrint[cinematic_players_config]):
		Resolved
	else:
		LPrint("i_localization missing, using empty")
		cinematic_players_config{}	