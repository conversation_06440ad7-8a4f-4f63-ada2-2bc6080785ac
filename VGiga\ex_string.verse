using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI


(Text:string).Wrap<public>(CharsLimit:int):string=
	var Lines:[]string = array{}

	var LineStart :int= 0
	var LineLength :int= Text.Length -1

	loop:
		if(LineLength <= 0):
			break
		if(LineStart > Text.Length -1):
			break
		if(LineLength <= CharsLimit):
			Line2 := Text.Slice[LineStart, LineStart + LineLength + 1] or block:
				LError()
				Text
			set Lines += array. Line2
			break

		NewLineStart := if(SpaceIndex := Text.FindBetweenLast[LineStart, LineStart + CharsLimit, ' ']):
			# LPrint("SpaceIndex {SpaceIndex}")
			
			set LineLength = SpaceIndex - 1 - LineStart
			SpaceIndex + 1
		else: # split in between word
			set LineLength = CharsLimit
			CharsLimit + 1

		Line := Text.Slice[LineStart, LineStart + LineLength + 1] or block:
			LError()
			Text
		set Lines += array. Line
		set LineStart = NewLineStart
		set LineLength = Text.Length - 1 - NewLineStart
	

	Join(Lines, "\n")


	