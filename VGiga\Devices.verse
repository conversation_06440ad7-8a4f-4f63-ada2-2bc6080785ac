using { /Verse.org/Simulation/Tags }
using { /Fortnite.com/Devices }
using { /Verse.org/Simulation }
using { /Fortnite.com/FortPlayerUtilities }
using { /UnrealEngine.com/Temporary/SpatialMath }
using { /Fortnite.com/Devices/CreativeAnimation }


WaitForPopupClick<public>(PopUpDialog:popup_dialog_device, Agent:agent)<suspends>:int={
    if(Char := Agent.GetFortCharacterActive[]){
        Result := race{
            block{
                R := PopUpDialog.RespondingButtonEvent.Await()
                R(1)
            }
            block{
                Char.EliminatedEvent().Await()
                PopUpDialog.Hide(Agent)
                0
            }
        }
        Result
    }else{
        0
    }
}

WaitForOkPopupClick<public>(Title:string, Description:string, Player:player, Popup:popup_dialog_device)<suspends>:int={
    Popup.SetTitleText(Title.ToMessage())
    Popup.SetDescriptionText(Description.ToMessage())
    Popup.SetButtonCount(1)
    Popup.SetButtonText("OK".ToMessage(), 0)
    Popup.ShowDelayed(Player)
    WaitForPopupClick(Popup, Player)
}

(ScoreManager:score_manager_device).ResetScoreCustom<public>(Agent:agent, ?Score:int= 0):void={
    PrevScoreAward := ScoreManager.GetScoreAward()
    CurAgentScore := ScoreManager.GetCurrentScore(Agent)
    ScoreManager.SetScoreAward(-CurAgentScore + Score)
    ScoreManager.Activate(Agent)
    ScoreManager.SetScoreAward(PrevScoreAward)
}

PopupDialogShowDelay : float = 1.3

(PopupDialog:popup_dialog_device).ShowDelayed<public>(Agent:agent)<suspends>:void={
    Sleep(PopupDialogShowDelay)
    PopupDialog.Show(Agent)
}