#gen
#arr_t1
#char
#id-81aacecc-4cb5-4b3d-8f94-c4d469a359b1
arr_char<public> := class<computes>:
	Arr<public>:[]char = array{}
		
arr_char_c<public>(Arr:[]char)<computes>:arr_char=
	arr_char:
		Arr := Arr
#id-81aacecc-4cb5-4b3d-8f94-c4d469a359b1
ToMsg<public>(Text:arr_char):message = ToMsg(Text.Arr)
ArrCharOk<public> :arr_char= arr_char:
		Arr := "Ok"
ArrCharCancel<public> :arr_char= arr_char:
		Arr := "Cancel"

ToString<public>(Val:arr_char)<computes><reads>:string=Val.Arr
#gen
#arr_t1
#int
#id-4c94a820-55b7-40c8-876e-3dcdb9c3519e
arr_int<public> := class<computes>:
	Arr<public>:[]int = array{}
		
arr_int_c<public>(Arr:[]int)<computes>:arr_int=
	arr_int:
		Arr := Arr
#id-4c94a820-55b7-40c8-876e-3dcdb9c3519e