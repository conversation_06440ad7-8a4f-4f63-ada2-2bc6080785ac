using { /Fortnite.com/Devices }

using { /Verse.org/Simulation }

using { /UnrealEngine.com/Temporary/Diagnostics }

 

TranslatingPropsTip<localizes>:message = "The props that translate (move) using animation."

RotatingPropsTip<localizes>:message = "The props that rotate using animation."

ScalingPropsTip<localizes>:message = "The props that scale using animation."

MoveAndRotatePropsTip<localizes>:message = "The props that both move and rotate using animation."

 

# Coordinates moving props through animation by calling each moveable_prop's Setup() method.

prop_animator := class(creative_device):

 

    # Array of moveable_props that translate using animation. 

    @editable {ToolTip := TranslatingPropsTip}

    TranslatingProps:[]translating_prop = array{}

 

    # Array of moveable_props that rotate using animation.

    @editable {ToolTip := RotatingPropsTip}

    RotatingProps:[]rotating_prop = array{}

 

    @editable {ToolTip := ScalingPropsTip}

    ScalingProps:[]scaling_prop = array{}

 

    # Array of moveable_props that both move and rotate using animation.

    @editable {ToolTip := MoveAndRotatePropsTip}

    AnimatingProps:[]animating_prop= array{}


 

    # Runs when the device is started in a running game

    OnBegin<override>()<suspends>:void=

 

        # For each prop, call Setup() to begin animating.

        for:

            Prop:TranslatingProps

        do:

            Prop.Setup()

        for:

            Prop:RotatingProps

        do:

            Prop.Setup()

        for:

            Prop:ScalingProps

        do:

            Prop.Setup()

        for:

            Prop:AnimatingProps

        do:

            Prop.Setup()