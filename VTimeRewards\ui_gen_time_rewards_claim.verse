using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/UI
using. /Fortnite.com/UI
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
time_rewards_claim_generated := class:
	ButtonClaimPlay:button_quiet
	Image_319ColorBlock:color_block
	Overlay_497:overlay
	Timer6:text_block
	Timer5:text_block
	Timer4:text_block
	Timer3:text_block
	Timer2:text_block
	Timer1:text_block
	Image_68:texture_block
	Overlay_400:overlay
	Image_214ColorBlock:color_block
	TimeRewardsClaim:canvas

ButtonClaimPlayTextVar<localizes>:message =  ""
Timer6TextVar<localizes>:message =  "10:99"
Timer5TextVar<localizes>:message =  "10:99"
Timer4TextVar<localizes>:message =  "10:99"
Timer3TextVar<localizes>:message =  "10:99"
Timer2TextVar<localizes>:message =  "10:99"
Timer1TextVar<localizes>:message =  "10:99"
make_time_rewards_claim_generated():time_rewards_claim_generated=

	ButtonClaimPlay :button_quiet= button_quiet:
		DefaultText := ButtonClaimPlayTextVar
	Image_319ColorBlock :color_block= color_block:
		DefaultColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultOpacity := 0.000000
		DefaultDesiredSize := vector2:
			X := 338.855255
			Y := 63.317814
	Overlay_497 :overlay= overlay:
		Slots := array:
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Center
				VerticalAlignment := vertical_alignment.Center
				Widget := Image_319ColorBlock
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := ButtonClaimPlay
	Timer6 :text_block= text_block:
		DefaultText := Timer6TextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
		DefaultJustification := text_justification.Center
	Timer5 :text_block= text_block:
		DefaultText := Timer5TextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
		DefaultJustification := text_justification.Center
	Timer4 :text_block= text_block:
		DefaultText := Timer4TextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
		DefaultJustification := text_justification.Center
	Timer3 :text_block= text_block:
		DefaultText := Timer3TextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
		DefaultJustification := text_justification.Center
	Timer2 :text_block= text_block:
		DefaultText := Timer2TextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
		DefaultJustification := text_justification.Center
	Timer1 :text_block= text_block:
		DefaultText := Timer1TextVar
		DefaultTextColor := color:
			R := 1.000000
			G := 1.000000
			B := 1.000000
		DefaultTextOpacity := 1.000000
		DefaultJustification := text_justification.Center
	Image_68 :texture_block= texture_block:
		DefaultImage := VTimeRewards.T_TimeRewardBg
		DefaultDesiredSize := vector2:
			X := 32.000000
			Y := 32.000000
	Overlay_400 :overlay= overlay:
		Slots := array:
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := Image_68
			overlay_slot:
				Padding := margin:
					Top := 139.000000
					Right := 500.000000
				HorizontalAlignment := horizontal_alignment.Center
				VerticalAlignment := vertical_alignment.Center
				Widget := Timer1
			overlay_slot:
				Padding := margin:
					Top := 139.000000
					Right := 300.000000
				HorizontalAlignment := horizontal_alignment.Center
				VerticalAlignment := vertical_alignment.Center
				Widget := Timer2
			overlay_slot:
				Padding := margin:
					Top := 139.000000
					Right := 100.000000
				HorizontalAlignment := horizontal_alignment.Center
				VerticalAlignment := vertical_alignment.Center
				Widget := Timer3
			overlay_slot:
				Padding := margin:
					Left := 98.000000
					Top := 139.000000
				HorizontalAlignment := horizontal_alignment.Center
				VerticalAlignment := vertical_alignment.Center
				Widget := Timer4
			overlay_slot:
				Padding := margin:
					Left := 295.000000
					Top := 139.000000
				HorizontalAlignment := horizontal_alignment.Center
				VerticalAlignment := vertical_alignment.Center
				Widget := Timer5
			overlay_slot:
				Padding := margin:
					Left := 495.000000
					Top := 139.000000
				HorizontalAlignment := horizontal_alignment.Center
				VerticalAlignment := vertical_alignment.Center
				Widget := Timer6
			overlay_slot:
				Padding := margin:
					Top := 250.000000
				HorizontalAlignment := horizontal_alignment.Center
				VerticalAlignment := vertical_alignment.Center
				Widget := Overlay_497
	Image_214ColorBlock :color_block= color_block:
		DefaultColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
		DefaultOpacity := 0.534000
		DefaultDesiredSize := vector2:
			X := 32.000000
			Y := 32.000000
	TimeRewardsClaim :canvas= canvas:
		Slots := array:
			canvas_slot:
				Alignment := vector2:
					X := 0.500000
					Y := 0.500000
				Offsets := margin:
					Right := 0.000000
					Bottom := 0.000000
				Anchors := anchors:
					Maximum := vector2:
						X := 1.000000
						Y := 1.000000
				SizeToContent := false
				Widget := Image_214ColorBlock
			canvas_slot:
				Alignment := vector2:
					X := 0.500000
					Y := 0.500000
				Offsets := margin:
					Right := 2048.000000
					Bottom := 1024.000000
				Anchors := anchors:
					Minimum := vector2:
						X := 0.500000
						Y := 0.500000
					Maximum := vector2:
						X := 0.500000
						Y := 0.500000
				ZOrder := 1
				SizeToContent := false
				Widget := Overlay_400


	time_rewards_claim_generated:
		ButtonClaimPlay := ButtonClaimPlay
		Image_319ColorBlock := Image_319ColorBlock
		Overlay_497 := Overlay_497
		Timer6 := Timer6
		Timer5 := Timer5
		Timer4 := Timer4
		Timer3 := Timer3
		Timer2 := Timer2
		Timer1 := Timer1
		Image_68 := Image_68
		Overlay_400 := Overlay_400
		Image_214ColorBlock := Image_214ColorBlock
		TimeRewardsClaim := TimeRewardsClaim
