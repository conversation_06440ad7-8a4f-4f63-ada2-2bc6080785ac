using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
using. VResourcesSystem
using. VArrowSystem
using. VPropSpawner


chest_manager_devic<public> := class(creative_device):
	@editable Timer:timer_device = timer_device{}
	@editable Button:button_device = button_device{}
	@editable InitProp:creative_prop = creative_prop{}
	@editable ClosedPropAsset:creative_prop_asset = DefaultCreativePropAsset
	@editable OpenAnimPropAsset:creative_prop_asset = DefaultCreativePropAsset
	@editable OpenAnimTime:float = 3.0
	
	var MInited:?chest_manager = false
	
	Init<public>(Container:player_events_manager_devic
	):chest_manager=
		if(Inited := MInited?):
			Inited
		else:
			Manager := chest_manager:
				D := Self
				InitTr := InitProp.GetTransform()
			.Init()
			InitProp.Dispose()
			Container.Register(Manager)
			set MInited = option. Manager
			Manager

chest_manager<public> := class<internal>(i_init_async):
	D<public>:chest_manager_devic
	OpenedEvnet<public>:event(agent) = event(agent){}

	var AgentForBeacon:?agent = false
	InitTr:transform

	Init():chest_manager=
		Self

	SetOwnerForNotif<public>(Agent:?agent):void=
		set AgentForBeacon = Agent

	var MClosedChest :?creative_prop_unique= false

	ResetExternalEvent:event() = event(){}

	ResetForJoinInProgress<public>():void=
		ResetExternalEvent.Signal()
		
	InitAsync<override>(Container:vcontainer)<suspends>:void=
		# LPrint("InitAsync")
		if(ResourcesManager := Container.Resolve[resources_manager]
			ArrowSystem := Container.Resolve[arrow_system]
			PropSpawner := Container.Resolve[prop_spawner]
		):
			# LPrint("loop")
			spawn. ReplaceOpenWithClosedChest(false, PropSpawner)

			loop:
				D.Button.Disable()
				D.Timer.Reset()
				Sleep(0.0)
				D.Timer.Start()
				D.Timer.SuccessEvent.Await()
				D.Button.Enable()
				MBeacon :?i_disposable= if(Ag := AgentForBeacon?):
					ArrowSystem.ShowBeacon(Ag, D.Button.GetTransform().Translation)
				else:
					false

				MAgent :?agent= race:
					block:
						ResetExternalEvent.Await()
						false
					block:
						Ag := D.Button.InteractedWithEvent.Await()
						option. Ag

				if(N := MClosedChest?):
					N.Dispose()
				MOpenAnimProp := PropSpawner.Spawn(D.OpenAnimPropAsset, InitTr)

				if(Agent := MAgent?):
					OpenedEvnet.Signal(Agent)
					if( PData := ResourcesManager.GetPlayerDataMap[Agent]):
						GoldPerSec := PData.GoldPerSec.Get()
						ResourcesManager.GiveGoldText(Agent, GoldPerSec * 30, "Chest")
				if( Beacon := MBeacon?):
					Beacon.Dispose()
					
				spawn. ReplaceOpenWithClosedChest(MOpenAnimProp, PropSpawner)
		else:
			LError()
		
	ReplaceOpenWithClosedChest(MOpenAnimProp:?creative_prop_unique, PropSpawner:prop_spawner)<suspends>:?creative_prop_unique=
		if(N := MOpenAnimProp?):
			Sleep(D.OpenAnimTime)
			N.Dispose()
		set MClosedChest = PropSpawner.Spawn(D.ClosedPropAsset, InitTr)
		
	# InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
		# PlayerDataMap.Set(Agent, p_data{})
		# PlayerRemoved.Await()
		# PlayerDataMap.Remove(Agent)
	

# p_data<public> := class():
