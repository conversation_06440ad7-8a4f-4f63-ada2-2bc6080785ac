using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga
using. VArrowSystem


# this device is put out of player counter so i_init_per_player is not called (i mean it is but after voting)

coop<public> := class(auto_creative_device, i_init, i_init_per_player_async):
    @editable VotingPlayerCounters:[]player_counter_device = array{}
    @editable PlayerReferences:[]player_reference_device = array{}

    @editable VotingAreaVolume:?volume_device = false
    # @editable VotingAreaPlayerCounter:?player_counter_device = false

    @editable CheckpointRestartLobby:?player_checkpoint_device = false
    @editable CheckpointMapStartAfterVoting:?player_checkpoint_device = false
    @editable RestartMapRoundDevice:?round_settings_device = false

    var<private> CoopAdmin<public>:?agent = false
    IsCoop<public>()<decides><transacts>:agent=
        CoopAdmin?
    IsAdmin<public>(Agent:agent)<decides><transacts>:void=
        Agent = CoopAdmin?
    AdminOrAgent<public>(Agent:agent)<transacts>:agent=
        CoopAdmin? or Agent

    var<private> CoopAdminLeft:logic = false
    var VotingMachines:[int]?agent = map:
        0 => false
        1 => false
        2 => false
        3 => false

    var PlayerEvents :?player_events_manager_devic= false
    var Arrows :?arrow_system= false

    var WidgetSyncers:[widget_syncer_id]widget_syncer = map{}

    GetNonAdminPlayers<public>()<transacts>:[]agent=
        for(Agent->Data:PlayerEvents.G().GetRegisteredPlayers()
            Agent <> CoopAdmin?
        ):
            Agent

    Init<override>(Container:vcontainer):void=
        set Arrows = Container.ResolveOp[arrow_system] or Err()
        CheckpointMapStartAfterVoting.G()
        CheckpointRestartLobby.G()
        VotingAreaVolume.G().VolumeForEveryAgent(OnAgentEnterVoting)
        VotingAreaVolume.G().AgentEntersEvent.Subscribe(OnAgentEnterVoting)
        # VotingAreaPlayerCounter.G().CountedEvent.Subscribe(OnAgentEnterVoting)
        # VotingAreaPlayerCounter.G().TransmitForAllCounted()
        set PlayerEvents = Container.ResolveOp[player_events_manager_devic] or Err()
        PlayerEvents.G().PlayerRemovedOncePerGame.Subscribe1(OnPlayerLeftGame)
        for(I := 0..3
            Counter:=VotingPlayerCounters[I]
        ):
            Counter.CountSucceedsEvent.SubscribeAnything(OnAdminPicked, I)

    InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
            
        for(Id->Syncer:WidgetSyncers):
            Cloned := CloneWidget(Syncer.MainWidget)
            Agent.AddToPlayerUi(Cloned)
            Syncer.Add(Agent, Cloned)
            Syncer.Sync()

        PlayerRemoved.Await()

        for(Beacon:Arrows.G().BeaconsPoolDevice.GetAll()):
            Beacon.RemoveFromShowList(Agent)     

        for(Id->Syncer:WidgetSyncers):
            if(Widget := Syncer.WidgetsToSync[Agent]):
                Agent.RemoveFromPlayerUi(Widget)
            Syncer.Remove(Agent)

    GetWidgetSyncerOrFalse<public>(Agent:agent, MainWidget:widget):?widget_syncer=
        if(Agent = CoopAdmin?):
            return option. widget_syncer:
                MainWidget := MainWidget
        return false

    SyncWidget<public>(WidgetId:widget_syncer_id):void=
        if(Syncer := WidgetSyncers[WidgetId]):
            Syncer.Sync()
    
    AddWidgetDynamicSlot<public>(Agent:agent, StackBox:stack_box, Slot:stack_box_slot, SyncerId:widget_syncer_id):void=
        if(CoopAdmin? = Agent):
            if(Syncer := WidgetSyncers[SyncerId]):
                Syncer.AddWidgetDynamicSlot(Agent, StackBox, Slot)
                Syncer.Sync()
            else:
                LErrorPrint("Syncer not found for widget id")
        else if(CoopAdmin?):
            LErrorPrint("ERROR adding slot as not admin but in coop mode")
        else:
            StackBox.AddWidget(Slot)

    RemoveWidgetDynamicSlot<public>(Agent:agent, StackBox:stack_box, Widget:widget, SyncerId:widget_syncer_id):void=
        if(CoopAdmin? = Agent):
            if(Syncer := WidgetSyncers[SyncerId]):
                Syncer.RemoveWidgetDynamicSlot(Agent, StackBox, Widget)
                Syncer.Sync()
            else:
                LErrorPrint("Syncer not found for widget id")
        else if(CoopAdmin?):
            LErrorPrint("ERROR adding slot as not admin but in coop mode")
        else:
            StackBox.RemoveWidget(Widget)



    AddWidgetToSyncAsAdmin<public>(Widget:widget, Id:widget_syncer_id):void=
        Syncer := widget_syncer:
            MainWidget := Widget
        if. set WidgetSyncers[Id] = Syncer

        for(Agent:GetNonAdminPlayers()):
            Cloned := CloneWidget(Widget)
            Agent.AddToPlayerUi(Cloned)
            Syncer.Add(Agent, Cloned)

    OnAdminPicked(A:tuple(), Id:int):void=
        if(AdminRef := PlayerReferences[Id]):
            MAdmin := AdminRef.GetAgent()
            if(Admin := MAdmin?):
                set CoopAdmin = option. Admin
                for(Counter:PlayerReferences):
                    MAgent := Counter.GetAgent()
                    if(Agent := MAgent?):
                        spawn. MovePlayerToStartAfterVotingAsync(Agent)
            else:
                LError()
        else:
            LError()

    OnPlayerLeftGame(Agent:agent):void=
        LPrint(" player left the game")
        UpdateTargetPlayersToStart(TargetPlayersToStart - 1)
        for(I := 0..3
            VotingMachines[I] = Agent
        ):
            if. set VotingMachines[I] = false
        if(Agent = CoopAdmin?):
            LPrint("admin left the game")
            set CoopAdmin = false
            # RestartMapRoundDevice.G().EndRound(Agent)
            PullAllPlayersToRestartMapArea()

    PullAllPlayersToRestartMapArea():void=
        set CoopAdminLeft = true
        for(Agent->Data:PlayerEvents.G().GetRegisteredPlayers()):
            spawn. PullPlayerToRestartMapAreaAsync(Agent)

    PullPlayerToRestartMapAreaAsync(Agent:agent)<suspends>:void=
        CheckpointRestartLobby.G().Register(Agent)

        Char := Agent.WaitForFortCharacterActive()
        # Char.SetVulnerability(true)
        loop:
            if(Char.IsVulnerable[]):
                break
            Sleep(0.1)
        # Sleep(3.0)
        Sleep(0.1)
        Char2 := Agent.WaitForFortCharacterActive()
        Char2.Damage(500.0)

    
    var TargetPlayersToStart :int= 0

    UpdateTargetPlayersToStart(Val:int):void=
        LPrint("UpdateTargetPlayersToStart {Val}")
        set TargetPlayersToStart = Val
        for(Counter:VotingPlayerCounters):
            Counter.SetTargetCount(TargetPlayersToStart)

    HasAgent(PlayerRef:player_reference_device, Agent:agent)<decides><transacts>:void=
        PlayerRef.IsReferenced[Agent]

    OnAgentEnterVoting(Agent:agent):void=
        Arrows.G().SetMapIndicatorsToShowToAll()
        Arrows.G().SetBeaconsToShowToAll()
        
        ReapplyPlayerReferencesJIPBug()
        if(PlayerReferences.ArrayFirst1[HasAgent, Agent]):
            return
        UpdateTargetPlayersToStart(TargetPlayersToStart + 1)
        var FreeSlot :?int= false
        for(I := 0..3
            not VotingMachines[I]?
        ):
            set FreeSlot = option. I
        VFreeSlot := FreeSlot? or Err()
        if. set VotingMachines[VFreeSlot] = option. Agent
        Ref := PlayerReferences[VFreeSlot] or Err()
        Ref.Register(Agent)

        # todo if admin already set then probably player joined in progress
        LPrint("CoopAdminLeft {CoopAdminLeft.ToString()}")
        LPrint("CoopAdmin {CoopAdmin? and "true" or "false"}")
        if(CoopAdminLeft?):
            spawn. PullPlayerToRestartMapAreaAsync(Agent)
        else if(CoopAdmin?):
            spawn. MovePlayerToStartAfterVotingAsync(Agent)

    MovePlayerToStartAfterVotingAsync(Agent:agent)<suspends>:void=
        CheckpointMapStartAfterVoting.G().Register(Agent)
        Char := Agent.WaitForFortCharacterActive()
        Char.SetVulnerability(true)
        Char.Damage(500.0)

    ReapplyPlayerReferencesJIPBug():void=
        for(PlayerRef:PlayerReferences):
            MAgent := PlayerRef.GetAgent()
            if(Agent := MAgent?):
                PlayerRef.Register(Agent)
        

widget_syncer_id<public> := enum:
    resources
    quests
