VFootballSystem<public> := module:
	Assets<public> := module:
		Explo<public> := module:

VWheelPrize<public> := module:
	Assets<public> := module:
		Panels<public> := module:

VDailyRewards<public> := module:
	Assets<public> := module:
		Textures<public> := module:

VGiga<public> := module:
	Assets<public> := module:
		Mats<public> := module{}
		GigaTextures<public> := module{}

VTextures<public> := module:

VResourcesSystem<public> := module:
	Assets<public> := module:
		TTextures<public> := module:

VPhone<public> := module:
	Assets<public> := module:
		Materials<public> := module:
		VTextures<public> := module:

VPropSpawner<public> := module:
VQuestSystem<public> := module:
VNotifications<public> := module:
VCustomBillboard<public> := module:
	Assets<public> := module:
		TextTextures<public> := module:
		TextMaterials<public> := module{}
		TextMaterialsGreen<public> := module{}

StoreGiftPacks<public> := module:
	TwoDAssets<public> := module:
		PNG<public> := module:

Modele<public> := module:
	Piggy<public> := module: