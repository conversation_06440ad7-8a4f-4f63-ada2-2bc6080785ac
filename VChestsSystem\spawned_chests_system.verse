using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
using. VGiga.Pool
using. VResourcesSystem
using. VArrowSystem
using. VPropSpawner


spawned_chests_manager_devic<public> := class(creative_device):
	# @editable Area:area_box_devic = area_box_devic{}
	# @editable ButtonDevPoolDev:button_device_pool_devic = button_device_pool_devic{}
	@editable FromTopCastPropAsset:creative_prop_asset = DefaultCreativePropAsset

	@editable ClosedPropAsset:creative_prop_asset = DefaultCreativePropAsset
	@editable OpenAnimPropAsset:creative_prop_asset = DefaultCreativePropAsset
	@editable OpenAnimTime:float = 3.0

	@editable Area:area_box_editable = area_box_editable{}
	@editable ExcludedAreas:[]area_box_editable = array{}
	
	var MInited:?spawned_chests_manager = false
	
	Init<public>(Container:player_events_manager_devic)<decides><transacts>:spawned_chests_manager=
		if(Inited := MInited?):
			Inited
		else:
			if(ButtonDevPoolDev := GetDevice[button_device_pool_devic]):
				Manager := spawned_chests_manager:
					D := Self
					ButtonsPool := ButtonDevPoolDev.GetPool()
				.Init()
				Container.Register(Manager)
				set MInited = option. Manager
				Manager
			else:
				FailError[]
				Err()

spawned_chests_manager<public> := class<internal>(i_init_async):
	OpenChestMessage<localizes>:message = "Open Chest"
	
	D<public>:spawned_chests_manager_devic
	OpenedEvnet<public>:event(agent) = event(agent){}
	ButtonsPool:button_device_pool

	Init()<transacts>:spawned_chests_manager=
		D.Area.Init()
		for(Areaa:D.ExcludedAreas):
			Areaa.Init()
		Self

	CanSpawn(Pos:vector3)<decides><transacts>:void=
		for(ExcludedArea:D.ExcludedAreas):
			not ExcludedArea.IsInside[Pos]
			

	# var MClosedChest :?creative_prop_unique= false

	GetRandomPosInArea()<suspends>:vector3=
		var Rand:vector3 = vector3{}
		loop:
			set Rand = D.Area.GetRandomPointInside()
			if(CanSpawn[Rand]):
				break
		Rand

	RespawnEvent:event() = event(){}
	Respawn<public>():void=
		RespawnEvent.Signal()
			
	InitAsync<override>(Container:vcontainer)<suspends>:void=
		if(ResourcesManager := Container.Resolve[resources_manager]
			PropSpawner := Container.Resolve[prop_spawner]
		):
			loop:
				MFromTopToBotProp := PropSpawner.Spawn(D.FromTopCastPropAsset, GetRandomPosInArea().Snap96())
				Sleep(0.01)
				if(FromTopToBotProp := MFromTopToBotProp?):
					ChestPos := FromTopToBotProp.Prop.GetTransform().Translation
					FromTopToBotProp.Dispose()

					Rotation := MakeRotationFromYawPitchRollDegrees(
						GetRandomFloat(0.0, 360.0).SnapToGrid(90.0), 
						0.0, 
						0.0
					)

					# ###
					# LPrint("ChestPos {ChestPos}")
					# PropSpawner.Spawn(DefaultCreativePropAsset, transform:
					# 	Translation := ChestPos
					# 	Rotation := rotation{}
					# 	Scale := Vector3One * 3
					# )

					MClosedPropAsset := PropSpawner.Spawn(D.ClosedPropAsset, transform:
						Translation := ChestPos
						Rotation := Rotation
						Scale := Vector3One * 2.5
					)

					if(ClosedPropAsset := MClosedPropAsset?
						ButtonDisp := ButtonsPool.RentDisposable[]
						Button := ButtonDisp.Device
					):
						if. Button.TeleportTo[ChestPos, rotation{}]
						Button.SetInteractionTime(3.0)
						Button.SetInteractionText(OpenChestMessage)

						MAgent :?agent= 
							race:
								block:
									Ag := Button.InteractedWithEvent.Await()
									option. Ag
								block:
									Sleep(60.0)
									false
								block:
									RespawnEvent.Await()
									false

						ClosedPropAsset.Dispose()
						if. Button.TeleportTo[vector3{Z := 2000.0}, rotation{}]
						ButtonDisp.Dispose()

						if(Agent := MAgent?):
							MOpenAnimPropAsset := PropSpawner.Spawn(D.OpenAnimPropAsset, transform:
								Translation := ChestPos
								Rotation := Rotation
								Scale := Vector3One * 2.5
							)
							if( PData := ResourcesManager.GetPlayerDataMap[Agent]):
								GoldPerSec := PData.GoldPerSec.Get()
								ResourcesManager.GiveGoldText(Agent, GoldPerSec * 15, "Chest")
							Sleep(D.OpenAnimTime)
							if(OpenAnim := MOpenAnimPropAsset?):
								OpenAnim.Dispose()

							Sleep(60.0)
				Sleep(3.0)
		else:
			LError()

	# 	# LPrint("InitAsync")
	# 	if(ResourcesManager := Container.Resolve[resources_manager]
	# 		ArrowSystem := Container.Resolve[arrow_system]
	# 		PropSpawner := Container.Resolve[prop_spawner]
	# 	):
	# 		# LPrint("loop")
	# 		spawn. ReplaceOpenWithClosedChest(false, PropSpawner)

	# 		loop:
	# 			D.Button.Disable()
	# 			D.Timer.Reset()
	# 			Sleep(0.0)
	# 			D.Timer.Start()
	# 			D.Timer.SuccessEvent.Await()
	# 			D.Button.Enable()
	# 			MBeacon :?i_disposable= if(Ag := AgentForBeacon?):
	# 				ArrowSystem.ShowBeacon(Ag, D.Button.GetTransform().Translation)
	# 			else:
	# 				false

	# 			Agent := D.Button.InteractedWithEvent.Await()

	# 			if(N := MClosedChest?):
	# 				N.Dispose()
	# 			MOpenAnimProp := PropSpawner.Spawn(D.OpenAnimPropAsset, InitTr)

	# 			OpenedEvnet.Signal(Agent)
	# 			if( PData := ResourcesManager.PlayerDataMap.Get[Agent]):
	# 				GoldPerSec := PData.GoldPerSec.Get()
	# 				ResourcesManager.GiveGoldText(Agent, GoldPerSec * 30, "Chest")
	# 			if( Beacon := MBeacon?):
	# 				Beacon.Dispose()
					
	# 			spawn. ReplaceOpenWithClosedChest(MOpenAnimProp, PropSpawner)
	# 	else:
	# 		LError()
		
	# ReplaceOpenWithClosedChest(MOpenAnimProp:?creative_prop_unique, PropSpawner:prop_spawner)<suspends>:?creative_prop_unique=
	# 	if(N := MOpenAnimProp?):
	# 		Sleep(D.OpenAnimTime)
	# 		N.Dispose()
	# 	set MClosedChest = PropSpawner.Spawn(D.ClosedPropAsset, InitTr)
		