using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }
using { VMatchmakingDisabler }
using{VHouseSystem}
using { VMapCode }
using{VResourcesSystem}
using{VLocalization }
using{VGiga.Pool}
using{VPrefabs}
using{VQuestSystem}


gm_quests_device := class(creative_device):
	@editable ClaimHouseTracker:?tracker_device = false
	
# Załóż lokatę ✅
# Załóż konto ✅
# Przejedź trasę wyścigu w X ✅
# Pokonaj tor parkour ✅
# Kup własny biznes ✅
# Zarób pierwszy milion ✅
# Kup przedmiot kosmetyczny ✅
# Zbuduj swój bank
# Kup dom ✅
# Złów 10 ryb

	Init(PrefabsBalance:gm_balance):void=
		set PrefabsBalance.QuestBalance.Quests = array:
			quest_data:
				Id := "claim house1"
				Name := "Start your first Bank!"
				TypeId := "claim house"
				MaxProgress := 1
				GoldReward := 0
				FirstQuest := true
				ShowInUiInstantly := true
				NextQuestIds := array:
				NextVisibleQuestIds := array:
					# "buy building1"
					"build whole house"
					"have cash 1mln"
			quest_data:
				Id := "register account1"
				Name := "Register an account using phone"
				ShortName := option. "Register an account"
				TypeId := "register account"
				MaxProgress := 1
				GoldReward := 5000
				FirstQuest := true
				ShowInUiInstantly := true
				NextVisibleQuestIds := array:
					# "make deposit1"
			# quest_data:
			# 	Id := "make deposit1"
			# 	Name := "Make savings deposit using phone"
			# 	ShortName := option. "Make time deposit"
			# 	TypeId := "make deposit"
			# 	MaxProgress := 1
			# 	GoldReward := 10
			# 	ShowInUiInstantly := false
			# 	FirstQuest := true
			# 	NextVisibleQuestIds := array:
			# # quest_data:
			# # 	Id := "race"
			# # 	Name := "Complete car race minigame"
			# # 	ShortName := option. "Complete car race"
			# # 	TypeId := "car race"
			# # 	MaxProgress := 1
			# # 	GoldReward := 50000
			# # 	ShowInUiInstantly := false
			# # 	FirstQuest := true
			# # 	NextQuestIds := array:
			# # 	NextVisibleQuestIds := array:
			# # quest_data:
			# # 	Id := "parkour"
			# # 	Name := "Complete parkour"
			# # 	TypeId := "parkour"
			# # 	MaxProgress := 1
			# # 	GoldReward := 60000
			# # 	ShowInUiInstantly := false
			# # 	FirstQuest := true
			# # 	NextQuestIds := array:
			# # 	NextVisibleQuestIds := array:
			# # quest_data:
			# # 	Id := "glassbridge"
			# # 	Name := "Complete glass bridge"
			# # 	TypeId := "glassbridge"
			# # 	MaxProgress := 1
			# # 	GoldReward := 30000
			# # 	ShowInUiInstantly := false
			# # 	FirstQuest := true
			# # 	NextQuestIds := array:
			# # 	NextVisibleQuestIds := array:
			# # quest_data:
			# # 	Id := "parkour2"
			# # 	Name := "Complete parkour"
			# # 	ShortName := option. "Complete parkour"
			# # 	TypeId := "parkour"
			# # 	MaxProgress := 5
			# # 	GoldReward := 10000
			# # 	ShowInUiInstantly := true
			# # 	NextQuestIds := array:
			# # quest_data:
			# # 	Id := "buy building1"
			# # 	Name := "Buy a business building"
			# # 	ShortName := option. "Buy a business"
			# # 	TypeId := "buy building"
			# # 	MaxProgress := 1
			# # 	GoldReward := 10000
			# # 	ShowInUiInstantly := false
			# # 	FirstQuest := true
			# # 	NextVisibleQuestIds := array:
			# # 		"buy house1"
			# # 		"build whole house"
			# # 		"have cash 1mln"
			# # quest_data:
			# # 	Id := "buy house1"
			# # 	Name := "Buy a house"
			# # 	ShortName := option. "Buy a house"
			# # 	TypeId := "buy house"
			# # 	MaxProgress := 1
			# # 	GoldReward := 50000
			# # 	ShowInUiInstantly := false
			# # 	FirstQuest := true
			# # 	NextVisibleQuestIds := array:
			# # 		"buy cosmetic1"
			# # quest_data:
			# # 	Id := "buy cosmetic1"
			# # 	Name := "Buy a cosmetic item"
			# # 	ShortName := option. "Buy a cosmetic"
			# # 	TypeId := "buy cosmetic"
			# # 	MaxProgress := 1
			# # 	GoldReward := 60000
			# # 	ShowInUiInstantly := false
			# # 	FirstQuest := true
			# quest_data:
			# 	Id := "have cash 1mln"
			# 	Name := "Become a millionaire"
			# 	TypeId := "have cash 1mln"
			# 	MaxProgress := 1
			# 	GoldReward := 300000
			# 	ShowInUiInstantly := false
			# 	FirstQuest := true
			# quest_data:
			# 	Id := "build whole house"
			# 	Name := "Finish building your bank"
			# 	ShortName := option. "Build your bank"
			# 	TypeId := "build whole house"
			# 	MaxProgress := 1
			# 	GoldReward := 2000
			# 	ShowInUiInstantly := false
			# 	FirstQuest := true
			# 	# MTracker := ClaimHouseTracker
			# # quest_data:
			# # 	Id := "playtime1"
			# # 	Name := "Play 5 minutes"
			# # 	TypeId := "playtime"
			# # 	MaxProgress := 5
			# # 	GoldReward := 3000
			# # 	NextQuestIds := array:
			# # 		"playtime2"
			# # quest_data:
			# # 	Id := "playtime2"
			# # 	Name := "Play 10 minutes"
			# # 	TypeId := "playtime"
			# # 	MaxProgress := 10
			# # 	GoldReward := 30000
			# # 	NextQuestIds := array:
			# # 		"playtime3"
			# # quest_data:
			# # 	Id := "playtime3"
			# # 	Name := "Play 15 minutes"
			# # 	TypeId := "playtime"
			# # 	MaxProgress := 15
			# # 	GoldReward := 60000
			# # 	NextQuestIds := array:
			# # 		"playtime4"
			# # quest_data:
			# # 	Id := "playtime4"
			# # 	Name := "Play 20 minutes"
			# # 	TypeId := "playtime"
			# # 	MaxProgress := 20
			# # 	GoldReward := 100000

			# # quest_data:
			# # 	Id := "player1"
			# # 	Name := "Kill player"
			# # 	TypeId := "kill player"
			# # 	MaxProgress := 1
			# # 	GoldReward := 3000
			# # 	NextQuestIds := array:
			# # 		"player2"
			# # quest_data:
			# # 	Id := "player2"
			# # 	Name := "Kill 3 players"
			# # 	TypeId := "kill player"
			# # 	MaxProgress := 3
			# # 	GoldReward := 30000
			# # 	NextQuestIds := array:
			# # 		"player3"
			# # quest_data:
			# # 	Id := "player3"
			# # 	Name := "Kill 10 players"
			# # 	TypeId := "kill player"
			# # 	MaxProgress := 10
			# # 	GoldReward := 100000

