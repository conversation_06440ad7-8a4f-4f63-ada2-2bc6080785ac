using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 



notifications_feed_player_ui_c():notifications_feed_player_ui=
	Gen := make_notifications_feed_generated()

	notifications_feed_player_ui:
		Canvas := Gen.NotificationsFeed
		StackBox := Gen.StackBox
		FeedSlots := for:
			Slot: array:
				feed_player_ui_slot_c(Gen.Overlay, Gen.Image, Gen.TextRes, Gen.TextFull, Gen.ImageGold)
				feed_player_ui_slot_c(Gen.Overlay_1,Gen.Image_1, Gen.TextRes_1, Gen.TextFull_1, Gen.ImageGold_1)
				feed_player_ui_slot_c(Gen.Overlay_2,Gen.Image_2, Gen.TextRes_2, Gen.TextFull_2, Gen.ImageGold_2)
				feed_player_ui_slot_c(Gen.Overlay_3,Gen.Image_3, Gen.TextRes_3, Gen.TextFull_3, Gen.ImageGold_3)
				feed_player_ui_slot_c(Gen.Overlay_4,Gen.Image_4, Gen.TextRes_4, Gen.TextFull_4, Gen.ImageGold_4)
				feed_player_ui_slot_c(Gen.Overlay_5,Gen.Image_5, Gen.TextRes_5, Gen.TextFull_5, Gen.ImageGold_5)
				feed_player_ui_slot_c(Gen.Overlay_6,Gen.Image_6, Gen.TextRes_6, Gen.TextFull_6, Gen.ImageGold_6)
				feed_player_ui_slot_c(Gen.Overlay_7,Gen.Image_7, Gen.TextRes_7, Gen.TextFull_7, Gen.ImageGold_7)
		do:
			Slot.Hide()
			Slot
	

feed_player_ui_slot_c(POverlay : overlay, PBg:texture_block,
	PTextIcon:text_block,
	PTextInfo:text_block,
	PTextureIcon:texture_block
)<transacts>:feed_player_ui_slot=
	feed_player_ui_slot:
		Overlay := POverlay
		Bg := PBg
		TextIcon := PTextIcon
		TextInfo := PTextInfo
		TextureIcon := PTextureIcon

feed_player_ui_slot := class:
	Overlay : overlay
	Bg:texture_block
	TextIcon:text_block
	TextInfo:text_block
	TextureIcon:texture_block

	# var Taken:logic= false
   
	var HideAtTime :float= 0.0

	Show(Text:message, PHideAtTime:float, Color:color):void=
		Overlay.Show()
		TextInfo.SetText(Text)
		TextInfo.SetTextColor(Color)
		set HideAtTime = GetSimulationElapsedTime() + PHideAtTime
		
	SetIcon(Icon:message):void=
		TextureIcon.Hide()
		TextIcon.SetText(Icon)
		TextIcon.Show()
		
	SetIcon(Icon:texture):void=
		TextureIcon.Show()
		TextureIcon.SetImage(Icon)
		TextIcon.Hide()
		

	Hide():void=
		TextInfo.SetText(EmptyMessage)
		Overlay.SetVisibility(widget_visibility.Collapsed)	
	
	
notifications_feed_player_ui := class:
	Canvas:canvas
	StackBox:stack_box
	var FeedSlots:[]feed_player_ui_slot
	# var TakenFeedSlots:[]feed_player_ui_slot = array{}


#gen
#map_t1_t2_priv
#agent
#notifications_feed_player_ui
#id-c873c12a-dc25-4267-8a94-cca497a1bf2d
map_agent_notifications_feed_player_ui := class:
	var DataMap:[agent]notifications_feed_player_ui = map{}
	var MDataSetEvent:?event(tuple(agent, notifications_feed_player_ui)) = false

	Get(Key:agent)<decides><transacts>:notifications_feed_player_ui=
		DataMap[Key]

	GetErr(Key:agent)<decides><transacts>:notifications_feed_player_ui=
		if(Data := DataMap[Key]):
			Data
		else:
			FailError[]
			Err()

	GetMap()<transacts>:[agent]notifications_feed_player_ui=
		DataMap

	GetOrAwait<public>(Key:agent)<suspends>:notifications_feed_player_ui=
		if(Data := DataMap[Key]):
			Data
		else:
			if(DataSetEvent := MDataSetEvent?):
				var Return:?notifications_feed_player_ui = false
				loop:
					Data := DataSetEvent.Await()
					if(Data(0) = Key):
						set Return = option. Data(1)
						break
				Return.G()
				# DataSetEvent.AwaitForData(Key)
			else:
				Ev := event(tuple(agent, notifications_feed_player_ui)){}
				set MDataSetEvent = option. Ev
				var Return:?notifications_feed_player_ui = false
				# Ev.AwaitForData(Key)
				loop:
					Data := Ev.Await()
					if(Data(0) = Key):
						set Return = option. Data(1)
						break
				Return.G()
			

	Set(Key:agent, Data:notifications_feed_player_ui):void=
		if. set DataMap[Key] = Data
		if(DataSetEvent := MDataSetEvent?):
			DataSetEvent.Signal((Key, Data))

	Remove(Key:agent)<transacts>:void=
		set DataMap = DataMap.WithRemoved(Key)
#id-c873c12a-dc25-4267-8a94-cca497a1bf2d