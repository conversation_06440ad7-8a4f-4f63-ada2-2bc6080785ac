using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
using. VResourcesSystem
using { /UnrealEngine.com/Assets }
using. VAudio
points_button := class(auto_creative_device, i_init_async, resetable):

	@editable Button:?button_device = false
	@editable MClicksForSpecialAction:?int = false
	@editable MSpecialActionChannel:?channel_device = false
	@editable MGrantPointsForSpecialAction:?int = false

	var Resources :?resources_manager= false
	var Audio:?audio = false


	var RewardRemaining:int = 0
	
	Reset<override>():void=
		set RewardRemaining = MGrantPointsForSpecialAction? or 3

	InitAsync<override>(Container:vcontainer)<suspends>:void=
		VButton := Button.G()
		set Resources = Container.ResolveOp[resources_manager] or Err()
		Reset()
		# set Audio = Container.ResolveOp[audio] or Err()
	# 	Fruits := tag_fruit{}.GetAll(Self)

	# 	for(Fruit:Fruits):
	# 		spawn. RunFruit(Fruit)
			

	# RunFruit(Fruit:button_device)<suspends>:void=
	# 	InitScale := Fruit.GetTransform().Scale
		var ClickCounter:int = MClicksForSpecialAction? or 0
		loop:
			Agent := VButton.InteractedWithEvent.Await()
			LPrint("RewardRemaining: {RewardRemaining}")
			
			if(ClicksForSpecialAction := MClicksForSpecialAction?, RewardRemaining > 0):
				LPrint("ClickCounter: {ClickCounter}")
				set ClickCounter += 1
				LPrint("ClickCounter: {ClicksForSpecialAction}")
				if(ClickCounter >= ClicksForSpecialAction):
					set ClickCounter = 0
					if(SpecialActionChannel := MSpecialActionChannel?):
						SpecialActionChannel.Transmit(option. Agent)
					if(PointsForSpecialAction := MGrantPointsForSpecialAction?):
						Resources.G().GivePoints(Agent, RewardRemaining)
						set RewardRemaining = RewardRemaining - 1
					
			# Resources.G().GiveGoldWithMultiplier(Agent, 0.0, 0.30)
			# Resources.G().GivePoints(Agent, 1)
			# CancelFx := SpawnParticleSystem(Modele.Piggy.Blow_up_Piggy, Fruit.GetTransform().Translation)
			# if. Fruit.TeleportTo[Fruit.GetTransform().WithScale(Vector3One * 0.0001)]
			# Audio.G().GotCash.Play(Agent)
			# Fruit.Disable()
			# Sleep(GetRandomFloat(RespawnMinRandomTime, RespawnMaxRandomTime))
			# if. Fruit.TeleportTo[Fruit.GetTransform().WithScale(InitScale)]
			# CancelFx.Cancel()
			# Fruit.Enable()

		