using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using.  VGiga


		
<#
VAR1_pool_component_c<public>(Area:area_interface, D:creative_device)<transacts>:VAR1_pool_component=
	VAR1_pool_component:
		Area := Area
		D := D
	.Init()

VAR1_pool_component<public> := class:
	Area<public>:area_interface
	var FreeCreativeDevices : []VAR1 = array{}
	D<public>:creative_device

	Init()<transacts>:VAR1_pool_component=
		set FreeCreativeDevices = for(Obj : D.GetDevicesInArea(VAR1, Area)):
			Obj
		Self

	GetAllFree<public>():[]VAR1=
		FreeCreativeDevices

	Rent<public>()<decides><transacts>:VAR1=
		if(CreativeDevice := FreeCreativeDevices[0]
			Val := FreeCreativeDevices.RemoveElement[0]
			set FreeCreativeDevices = Val
		):
			return CreativeDevice
		else:
			FailError[]
			return VAR1{}

	RentDisposable<public>()<decides><transacts>:pooled_editable_VAR1=
		Device := Rent[]
		pooled_editable_VAR1:
			Device := Device
			MyPool := Self

	Return<public>(CreativeDevice:VAR1):void=
		if. CreativeDevice.TeleportTo[Area.GetCenterPoint(), rotation{}]
		set FreeCreativeDevices += array. CreativeDevice


pooled_editable_VAR1<public> := class(i_disposable):
	MyPool<public>:VAR1_pool_component
	Device<public>:VAR1

	Dispose<override>():void=
		MyPool.Return(Device)
#>

	