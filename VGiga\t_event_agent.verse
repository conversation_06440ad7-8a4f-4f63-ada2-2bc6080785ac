using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }

# per_player_event(t:type) := class{
#     AgentGotExpEvent:[agent]event(t) = map{}

#     Signal(Agent:agent, Val:t):void={
#         if(Ev := AgentGotExpEvent[Agent]){
#             Ev.Signal(Val)
#         }else{LError()}
#     }

#     Add(Agent:agent):event(t)={
#         if(Ev := AgentGotExpEvent[Agent]){
#             LError()
#             Ev
#         }else{
#             set AgentGotExpEvent[Agent] = event(t){}
#         }
#     }
# }
# per_player_event<public>:= class{
#     var Events:[agent]event() = map{}

#     Signal<public>(Agent:agent, Val:int):void={
#         if(Ev := Events[Agent]){
#             Ev.Signal()
#         }else{LError()}
#     }

#     Add<public>(Agent:agent):event()={
#         if(Ev := Events[Agent]){
#             LError()
#             return Ev
#         }else{
#             Ev := event(){}
#             if. set Events[Agent] = Ev
#             Ev
#         }
#     }
# }

# map_class(t:type) := class<abstract>(){
#     GetEvents()<transacts>:[agent]event(t)
#     SetEvents(Agent:agent, Val:event(t))<transacts>:void
#     RemoveEvents(Agent:agent)<transacts>:void
# }

# map_class_float := class(map_class(float)){
#     var Events:[agent]event(float) = map{}

#     GetEvents<override>()<transacts>:[agent]event(float)= {Events}
#     SetEvents<override>(Agent:agent, Val:event(float))<transacts>:void={ if. set Events[Agent] = Val }
#     RemoveEvents<override>(Agent:agent)<transacts>:void={ set Events = Events.WithRemoved(Agent) }
# }


# map_wrapper(key:subtype(comparable), val:type) := class<abstract>(){
#     GetEvents()<transacts>:[key]val
#     SetEvents(Key:key, Val:val)<transacts>:void
#     RemoveEvents(Key:key)<transacts>:void
# }

# map_wrapper_agent_float := class(map_wrapper(agent, event(float))){
#     var Events:[agent]event(float) = map{}

#     GetEvents<override>()<transacts>:[agent]event(float)= {Events}
#     SetEvents<override>(Agent:agent, Val:event(float))<transacts>:void={ if. set Events[Agent] = Val }
#     RemoveEvents<override>(Agent:agent)<transacts>:void={ set Events = Events.WithRemoved(Agent) }
# }
# map_wrapper_agent_string := class(map_wrapper(agent, event(string))){
#     var Events:[agent]event(string) = map{}

#     GetEvents<override>()<transacts>:[agent]event(string)= {Events}
#     SetEvents<override>(Agent:agent, Val:event(string))<transacts>:void={ if. set Events[Agent] = Val }
#     RemoveEvents<override>(Agent:agent)<transacts>:void={ set Events = Events.WithRemoved(Agent) }
# }
# map_wrapper_agent_int := class(map_wrapper(agent, event(int))){
#     var Events:[agent]event(int) = map{}

#     GetEvents<override>()<transacts>:[agent]event(int)= {Events}
#     SetEvents<override>(Agent:agent, Val:event(int))<transacts>:void={ if. set Events[Agent] = Val }
#     RemoveEvents<override>(Agent:agent)<transacts>:void={ set Events = Events.WithRemoved(Agent) }
# }

# per_player_event_base(t:type) := class(){
#     Events:map_wrapper(agent, event(t))

#     Signal<public>(Agent:agent, Val:t):void={
#         if(Ev := Events.GetEvents()[Agent]){
#             Ev.Signal(Val)
#         }else{LError()}
#     }

#     Add<public>(Agent:agent):event(t)={
#         if(Ev := Events.GetEvents()[Agent]){
#             LError()
#             return Ev
#         }else{
#             Ev := event(t){}
#             Events.SetEvents(Agent, Ev)
#             Ev
#         }
#     }

#     Remove<public>(Agent:agent):void={
#         Events.RemoveEvents(Agent)
#     }
# }

# per_player_event_float<public> := class(per_player_event_base(float)){
#     Events<override>:map_wrapper(agent, event(float)) = map_wrapper_agent_float{}
# }
# per_player_event_string<public> := class(per_player_event_base(string)){
#     Events<override>:map_wrapper(agent, event(string)) = map_wrapper_agent_string{}
# }
# per_player_event_int<public> := class(per_player_event_base(int)){
#     Events<override>:map_wrapper(agent, event(int)) = map_wrapper_agent_int{}
# }
# map_agent_event_autofill(t:type) := class(){
# 	Map:map_agent_event(t) = map_agent_event(t){}

# 	# Get<override>(Agent:agent)<decides><transacts>:event(t)={
# 	# 	if(Ev := Events[Agent]){
# 	# 		Ev
# 	# 	}else{
# 	# 		FailError[]
# 	# 		Err()
# 	# 	}
# 	# }
# }

map_agent_event<public> := class():
	var Events:[agent]event() = map{}

	Signal<public>(Key:agent):void=
		if(Ev := Events[Key]):
			Ev.Signal()
		else. LError()

	GetNoError<public>(Key:agent)<decides><transacts>:event()=
		Events[Key]

	Get<public>(Key:agent)<decides><transacts>:event()=
		if(Ev := Events[Key]):
			Ev
		else:
			FailError[]
			Err()

	Add<public>(Key:agent)<transacts>:event()=
		if(Ev2 := Events[Key]):
			LError()
			return Ev2
		Ev := event(){}
		if. set Events[Key] = Ev
		Ev

	Add<public>(Key:agent, Ev:event())<transacts>:void=
		if(Ev2 := Events[Key]):
			LError()
		else:
			if. set Events[Key] = Ev

	Remove<public>(Key:agent)<transacts>:void=
		set Events = Events.WithRemoved(Key)

#gen
#map_t1_event_t2
#agent
#string
#id-537ddc98-c110-4518-a86d-23050c7da4de
map_agent_event_string<public> := class():
	var Events:[agent]event(string) = map{}

	Signal<public>(Key:agent, Val:string):void=
		if(Ev := Events[Key]):
			Ev.Signal(Val)
		else. LError()

	Get<public>(Key:agent)<decides><transacts>:event(string)=
		if(Ev := Events[Key]):
			Ev
		else:
			FailError[]
			Err()

	Add<public>(Key:agent)<transacts>:event(string)=
		if(Ev2 := Events[Key]):
			LError()
			return Ev2
		Ev := event(string){}
		if. set Events[Key] = Ev
		Ev

	Add<public>(Key:agent, Ev:event(string))<transacts>:void=
		if(Ev2 := Events[Key]):
			LError()
		else:
			if. set Events[Key] = Ev

	Remove<public>(Key:agent)<transacts>:void=
		set Events = Events.WithRemoved(Key)
#id-537ddc98-c110-4518-a86d-23050c7da4de

#gen
#map_t1_event_t2
#agent
#int
#id-03aee3ce-8d15-458d-b80e-30394e4e9048
map_agent_event_int<public> := class():
	var Events:[agent]event(int) = map{}

	Signal<public>(Key:agent, Val:int):void=
		if(Ev := Events[Key]):
			Ev.Signal(Val)
		else. LError()

	Get<public>(Key:agent)<decides><transacts>:event(int)=
		if(Ev := Events[Key]):
			Ev
		else:
			FailError[]
			Err()

	Add<public>(Key:agent)<transacts>:event(int)=
		if(Ev2 := Events[Key]):
			LError()
			return Ev2
		Ev := event(int){}
		if. set Events[Key] = Ev
		Ev

	Add<public>(Key:agent, Ev:event(int))<transacts>:void=
		if(Ev2 := Events[Key]):
			LError()
		else:
			if. set Events[Key] = Ev

	Remove<public>(Key:agent)<transacts>:void=
		set Events = Events.WithRemoved(Key)
#id-03aee3ce-8d15-458d-b80e-30394e4e9048

#gen
#map_t1_event_t2
#agent
#float
#id-c1673247-b97b-436c-b121-0b5f69f55361
map_agent_event_float<public> := class():
	var Events:[agent]event(float) = map{}

	Signal<public>(Key:agent, Val:float):void=
		if(Ev := Events[Key]):
			Ev.Signal(Val)
		else. LError()

	Get<public>(Key:agent)<decides><transacts>:event(float)=
		if(Ev := Events[Key]):
			Ev
		else:
			FailError[]
			Err()

	Add<public>(Key:agent)<transacts>:event(float)=
		if(Ev2 := Events[Key]):
			LError()
			return Ev2
		Ev := event(float){}
		if. set Events[Key] = Ev
		Ev

	Add<public>(Key:agent, Ev:event(float))<transacts>:void=
		if(Ev2 := Events[Key]):
			LError()
		else:
			if. set Events[Key] = Ev

	Remove<public>(Key:agent)<transacts>:void=
		set Events = Events.WithRemoved(Key)
#id-c1673247-b97b-436c-b121-0b5f69f55361

#gen
#map_t1_event_t2
#agent
#logic
#id-a31978e5-3b16-4052-be5a-f5637ae85f90
map_agent_event_logic<public> := class():
	var Events:[agent]event(logic) = map{}

	Signal<public>(Key:agent, Val:logic):void=
		if(Ev := Events[Key]):
			Ev.Signal(Val)
		else. LError()

	Get<public>(Key:agent)<decides><transacts>:event(logic)=
		if(Ev := Events[Key]):
			Ev
		else:
			FailError[]
			Err()

	Add<public>(Key:agent)<transacts>:event(logic)=
		if(Ev2 := Events[Key]):
			LError()
			return Ev2
		Ev := event(logic){}
		if. set Events[Key] = Ev
		Ev

	Add<public>(Key:agent, Ev:event(logic))<transacts>:void=
		if(Ev2 := Events[Key]):
			LError()
		else:
			if. set Events[Key] = Ev

	Remove<public>(Key:agent)<transacts>:void=
		set Events = Events.WithRemoved(Key)
#id-a31978e5-3b16-4052-be5a-f5637ae85f90

#gen
#map_t1_event_t2
#agent
#message
#id-3d6bdd97-275e-45c3-b3eb-8c19a608cc32
map_agent_event_message<public> := class():
	var Events:[agent]event(message) = map{}

	Signal<public>(Key:agent, Val:message):void=
		if(Ev := Events[Key]):
			Ev.Signal(Val)
		else. LError()

	Get<public>(Key:agent)<decides><transacts>:event(message)=
		if(Ev := Events[Key]):
			Ev
		else:
			FailError[]
			Err()

	Add<public>(Key:agent)<transacts>:event(message)=
		if(Ev2 := Events[Key]):
			LError()
			return Ev2
		Ev := event(message){}
		if. set Events[Key] = Ev
		Ev

	Add<public>(Key:agent, Ev:event(message))<transacts>:void=
		if(Ev2 := Events[Key]):
			LError()
		else:
			if. set Events[Key] = Ev

	Remove<public>(Key:agent)<transacts>:void=
		set Events = Events.WithRemoved(Key)
#id-3d6bdd97-275e-45c3-b3eb-8c19a608cc32