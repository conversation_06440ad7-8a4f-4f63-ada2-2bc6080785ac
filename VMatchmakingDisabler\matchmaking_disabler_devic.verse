# using { /Fortnite.com/Devices }
# using { /Verse.org/Simulation }
# using { VGiga }

# matchmaking_disabler_devic<public> := class(creative_device){
#     @editable RoundSettings : round_settings_device = round_settings_device{}

#     Init<public>(DisableAfterMinutes:float):void={
# 		RoundSettings.EnableMatchmaking()
# 		spawn. DisableMatchmakingWithDelay(DisableAfterMinutes)
#     }

#     DisableMatchmakingWithDelay<public>(Delay:float)<suspends>:void={
#         Sleep(Delay * 60)
#         DisableMatchmaking()
#     }

#     DisableMatchmaking<public>():void={
#         RoundSettings.DisableMatchmaking()
#     }
# }