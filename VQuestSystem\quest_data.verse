using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }

active_quest_data<public> := class<unique>:
	Data<public> :quest_data
	var Progress:int = 0
	QuestFinishedEvent<public> :event() = event(){}
	Agent<public>:agent
	RequestedToShowOnUiEv<public> :event() = event(){}
	var FinishedFromSave:logic = false
	# Type:quest_type


quest_data<public> := class:
	var MaxProgress<public>:int
	var IsMaxProgressCumulative<public>:logic = true
	var ProgressIncreasePerEvent<public>:int = 1
	var Id<public>:string
	var NextQuestIds<public>:[]string = array{}
	var NextVisibleQuestIds<public>:[]string = array{}
	var Name<public>:string
	var ShortName<public>:?string = false
	var ArrowPosition<public>:?vector3 = false
	var ArrowPositionGetter<public>:?(agent->?vector3) = false
	var GoldReward<public>:int
	var MTracker<public>:?tracker_device = false
	var FirstQuest<public>:logic = false
	var ShowInUiInstantly<public>:logic = true

	# possible values:
	# kill creature
	# kill player
	# playtime # this is in minutes
	var TypeId<public>:string

	#this is set externally, from balance.questcompleters
	var CompleteEvent<public>:?event(agent) = false

