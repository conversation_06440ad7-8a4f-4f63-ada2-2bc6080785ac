using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }

spawner_devic_for_pool<public> := class<unique>(creative_device){
	SpawnEvent: event(tuple(creative_prop_asset, transform, event(?creative_prop))) = event(tuple(creative_prop_asset, transform, event(?creative_prop))) {}

	var MManager:?spawner_for_pool = false

	OnBegin<override>()<suspends>:void={
		# GigaLogger.SetBillboard(LogBillboard)
		# GigaLogger.LogToBillboard("SD: {GetCreativeObjectsWithTag(game_manager_tag{}).Length}")
		# if(GM := for(
		#     I:GetCreativeObjectsWithTag(game_manager_tag{}),
		#     Casted := game_manager[I]
		# ){
		#         Casted
		# }[0]){
		#     set GM.SpawnerDevices += array{Self}
		# }

		loop{
			SpawnEventData := SpawnEvent.Await()
			if(Manager := MManager?){
				Unique := Manager.SpawnItem(SpawnEventData(0), SpawnEventData(1))
				SpawnEventData(2).Signal(Unique)
			}else{LError()}
		}
	}

	Init(Logger:i_logger):spawner_for_pool={
		Manager := spawner_for_pool{Logger := Logger, D := Self}
		set MManager = option. Manager
		Manager
	}
}
