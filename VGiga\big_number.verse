using { /Verse.org/Simulation }

#################################
# We can represent very big numbers as a struct containing both a value and an exponent
#################################
big_number := struct <concrete>:
	@editable
	Value : float = 0.0
	@editable
	Exponent : int = 0

#################################
# Below are various functions to operate on big numbers
#################################

# Adds two big_numbers together
(NumberA : big_number).Add(NumberB : big_number):big_number=
	CommonExponent : int = Max(NumberA.Exponent, NumberB.Exponent)

	NewValue : float =  (NumberA.Value * Pow(10.0, (NumberA.Exponent - CommonExponent) * 1.0)) + 
						(NumberB.Value * Pow(10.0, (NumberB.Exponent - CommonExponent) * 1.0))

	big_number{Value := NewValue, Exponent := CommonExponent}.Normalize()

# Subtracts a big_number from another
(NumberA : big_number).Sub(NumberB : big_number):big_number=
	CommonExponent : int = Max(NumberA.Exponent, NumberB.Exponent)

	NewValue : float =  (NumberA.Value * Pow(10.0, (NumberA.Exponent - CommonExponent) * 1.0)) - 
						(NumberB.Value * Pow(10.0, (NumberB.Exponent - CommonExponent) * 1.0))

	big_number{Value := NewValue, Exponent := CommonExponent}.Normalize()

# Multiply two big_numbers together
(NumberA : big_number).Mul( NumberB : big_number):big_number=
	NewValue := NumberA.Value * NumberB.Value
	NewExponent := NumberA.Exponent + NumberB.Exponent

	big_number{Value := NewValue, Exponent := NewExponent}.Normalize()

# Multiply a big_number with a float
(NumberA : big_number).Mul(NumberB : float):big_number=
	NewValue := NumberA.Value * NumberB
	NewExponent := NumberA.Exponent

	big_number{Value := NewValue, Exponent := NewExponent}.Normalize()

# Tells you if one big_number is greater than another
(NumberA : big_number).GreaterThanOrEquals(NumberB : big_number):logic=
	if:
		NumberA.Exponent > NumberB.Exponent or (NumberA.Exponent = NumberB.Exponent and NumberA.Value >= NumberB.Value)
	then:
		return true

	return false

# Tells you how much it would cost to buy X items
#   Start is how many items you currently own, and count is how many you would like to purchase (used for price scaling)
#   CommonRatio refers to the scaling in price for subsequent purchases.  If set to 1.0, no price scaling occurs.  
BigNumber_CalculateCostRange(BaseCost : big_number, Start : int, Count : int, CommonRatio : float):big_number=
	term1 := Pow(CommonRatio, (Start + Count)*1.0)
	term2 := Pow(CommonRatio, Start * 1.0)

	TotalCost_Value := BaseCost.Value * (term1 - term2) / (CommonRatio - 1.0)
	TotalCost_Exponent := BaseCost.Exponent

	big_number{Value:=TotalCost_Value, Exponent:=TotalCost_Exponent}.Normalize()


# Tells you how many items you could afford to purchase
#   CommonRatio refers to the scaling in price for subsequent purchases.  If set to 1.0, no price scaling occurs.  
BigNumber_CalculateAffordableItems(CurrentMoney : big_number, BaseCost : big_number, AmountOwned : int, CommonRatio : float):int=
	var AffordableItems : int = 1
	loop:
		Affordable := CurrentMoney.GreaterThanOrEquals(BigNumber_CalculateCostRange(BaseCost, AmountOwned, AffordableItems, CommonRatio))
		if:
			not Affordable?
		then:
			break
		
		set AffordableItems += 1

	return AffordableItems - 1


# Formats a big_number into a string
(ValueIN : big_number).FormatDisplay()<transacts>:string=
	if (ValueIN.Value = 0):
		return "0"

	CorrectedNumber := ValueIN.Normalize().NormalizeToNearestNumberSet()
	Value := CorrectedNumber.Value

	if:
		CorrectedNumber.Exponent < 3
		ValueAsInt := Floor[Value]
	then:
		return "{ValueAsInt}"
	
	var ValueAsString : string = "{Value}"
	# LPrint("ValueAsString {ValueAsString}")
	# LPrint("Value {Value}")
	# LPrint("Value * 1000.0 {Value * 1000.0}")
	# if. LPrint("Mod[Value * 1000.0 {Mod[Floor[Value * 1000.0],1000]}")
	# ex value: 10.000000, has 6 zeros
	# 10.000000 * 10 = 10.00000
	# 10.000000 * 100 = 100.0000
	# 10.000000 * 1000 = 1000.000
	# 10.000000 * 10000 = 10000.00
	# 10.000000 * 100000 = 100000.0
	# 10.000000 * 1000000 = 1000000.
	# AmountOfTrailingZeros :int=
	# 	if(Mod[Floor[Value * 1000000.0],10] = 0):
	# 		if(Mod[Floor[Value * 100000.0],10] = 0):
	# 			if(Mod[Floor[Value * 10000.0],10] = 0):
	# 				if(Mod[Floor[Value * 1000.0],10] = 0):
	# 					if(Mod[Floor[Value * 100.0],10] = 0):
	# 						if(Mod[Floor[Value * 10.0],10] = 0):
	# 							6
	# 						else:
	# 							5
	# 					else:
	# 						4
	# 				else:
	# 					3
	# 			else:
	# 				2
	# 		else:
	# 			1
	# 	else:
	# 		0

	# if(AmountOfTrailingZeros = 6):

	TargetLength : int = if(Mod[Floor[Value * 10.0],10] = 0):
		if(Value < 10.0):
			1
		else if(Value >= 100.0):
			3
		else:
			2
	else if(CorrectedNumber.Value >= 100.0):
		3
	else:
		4


	# LPrint("TargetLength {TargetLength}")
	if(Val := ValueAsString.Remove[TargetLength, ValueAsString.Length]):
		set ValueAsString = Val

	
	if:
		SuffixIndex : int = Quotient[CorrectedNumber.Exponent, 3]
		set ValueAsString += NumberSuffixes[SuffixIndex]      

	ValueAsString


NumberSuffixes : []string = array{"-","K","M","B","T","Qa","Qi","Sx","Sp","Oc","No","De"}


# Takes a big_number, and converts it to ensure a single-digit whole-number component for the Value field
#   Ex)  14.2 * 10^2  will become 1.42 * 10^3
(Number : big_number).Normalize()<transacts>:big_number=
	var NewExponent : int = Number.Exponent
	var NewValue : float = Number.Value

	if (NewValue <= 0.0):
		return NewResult := big_number{}

	loop:
		if (NewValue < 10.0):
			break
		set NewValue *= 0.1
		set NewExponent += 1
	
	loop:
		if (NewValue >= 1.0):
			break
		set NewValue *= 10.0
		set NewExponent -= 1      

	Result := big_number:
		Value := NewValue
		Exponent := NewExponent


# Takes a big_number, and converts it to ensure a 1,2 or 3 digit whole-number component for the Value field.  This is used for string formatting.
#   Ex)  1.42K or 14.2K or 142K are all valid
#        1420k is not valid, and should instead be 1.42M
(Number : big_number).NormalizeToNearestNumberSet()<transacts>:big_number=
	var NewExponent : int = Number.Exponent
	var NewValue : float = Number.Value

	loop:
		if (Mod[NewExponent, 3] = 0):
			break

		set NewValue *= 10.0
		set NewExponent -= 1

	Result := big_number:
		Value := NewValue
		Exponent := NewExponent
