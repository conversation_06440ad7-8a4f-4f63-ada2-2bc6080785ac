list_VAR1<public> := class:
	var<private> Length<public>:int = 0

	var Arr:[]VAR1 = array{}
	var<private> ArrLength:int = 0

	GetAll<public>()<transacts>:[]VAR1=
		Arr

	Get<public>(I:int)<decides><transacts>:VAR1=
		Arr[I]
		
	Add<public>(Obj:VAR1)<transacts>:void=
		if(ArrLength > Length):
			if(set Arr[Length] = Obj):
				set Length += 1
			else:
				LError()
		else:
			set Arr += array. Obj
			set ArrLength += 1
			set Length += 1

	Clear<public>()<transacts>:void=
		set Arr = array{}
		set ArrLength = 0
		set Length = 0
		

	# RemoveElement<public>(I:int)<transacts>:void=
	# 	if(I < Count):
	# 		for(X := I..Count-2
	# 			Val := Arr[X+1]
	# 		):
	# 			if. set Arr[X] = Val
	# 		set Count -= 1
	# 	else:
	# 		LError()
		
list_VAR1_c<public>(Arr:[]VAR1):void=
	list_VAR1:
		Arr := Arr
		ArrLength := Arr.Length
		Length := Arr.Length
