using { /Fortnite.com/Devices }
using { /Fortnite.com/FortPlayerUtilities }
using { /Fortnite.com/Characters }
using { /Fortnite.com/AI }
using { /Verse.org/Simulation }
using { VGiga }
using { VCustomBillboard }
using { VNotifications }

marketing_investment_device<public> := class(auto_creative_device, i_init_async):
    @editable InvestButton:button_device = button_device{}
    @editable InvestmentBillboard:custom_billboard_devic = custom_billboard_devic{}
    @editable CustomerBillboard:custom_billboard_devic = custom_billboard_devic{}
    @editable InvestmentCost:int = 1000

    var Resources:?resources_manager = false
    var SaveSystem:?save_system = false
    var Notifications:?notifications_system = false
    var Loc:i_localization = empty_i_localization{}

    # Customer acquisition and revenue constants
    var BaseCustomerAcquisitionRate:float = 0.02 # customers per second per $1000 invested
    var CustomerRevenueRate:float = 0.05 # dollars per second per customer
    var DiminishingReturnsFactor:float = 0.9 # each investment becomes 90% as effective

    InitAsync<override>(Container:vcontainer)<suspends>:void=
        set Resources = Container.ResolveOp[resources_manager] or Err()
        set SaveSystem = Container.ResolveOp[save_system] or Err()
        set Notifications = Container.ResolveOp[notifications_system] or Err()
        set Loc = Container.Resolve_i_localization()

        spawn. HandleButtonActivations()
        spawn. UpdateCustomers()
        spawn. UpdateBillboards()

        # Test balance on startup
        TestBalance()



    HandleButtonActivations()<suspends>:void=
        loop:
            Agent := InvestButton.InteractedWithEvent.Await()
            TookGold := Resources.G().TryToTakeGold(Agent, InvestmentCost)
            if(TookGold?):
                # Add to marketing investment
                CurrentInvestment := SaveSystem.G().GetIntStat(Agent, int_stat_id.MarketingInvestment)
                NewInvestment := CurrentInvestment + InvestmentCost
                SaveSystem.G().SetIntStat(Agent, int_stat_id.MarketingInvestment, NewInvestment)

                # Show notification
                Notifications.G().ShowCenterNotification(Agent, "Marketing investment increased! Total: ${NewInvestment.ToShortNumberString()}".ToMessage(), 3.0)
            else:
                # Not enough gold
                Notifications.G().ShowCenterNotification(Agent, "Not enough gold for marketing investment!".ToMessage(), 2.0)

    UpdateCustomers()<suspends>:void=
        loop:
            Sleep(1.0) # Update every second

            for(Player -> Data : Resources.G().PlayerDataMap.DataMap):
                Agent := Data.Agent
                CurrentInvestment := SaveSystem.G().GetIntStat(Agent, int_stat_id.MarketingInvestment)
                CurrentCustomers := SaveSystem.G().GetIntStat(Agent, int_stat_id.CustomerCount)

                if(CurrentInvestment > 0):
                    # Calculate customer acquisition rate with diminishing returns
                    # Formula: rate = base_rate * investment * (diminishing_factor ^ (investment / 10000))
                    InvestmentFactor := CurrentInvestment / 1000.0
                    DiminishingFactor := Pow(DiminishingReturnsFactor, CurrentInvestment / 10000.0)
                    CustomerAcquisitionRate := BaseCustomerAcquisitionRate * InvestmentFactor * DiminishingFactor

                    # Add customers (fractional customers accumulate over time)
                    NewCustomers := CurrentCustomers + (Round[CustomerAcquisitionRate] or 0)
                    if(NewCustomers > CurrentCustomers):
                        SaveSystem.G().SetIntStat(Agent, int_stat_id.CustomerCount, NewCustomers)

    UpdateBillboards()<suspends>:void=
        loop:
            Sleep(2.0) # Update billboards every 2 seconds

            for(Player -> Data : Resources.G().PlayerDataMap.DataMap):
                Agent := Data.Agent
                CurrentInvestment := SaveSystem.G().GetIntStat(Agent, int_stat_id.MarketingInvestment)
                CurrentCustomers := SaveSystem.G().GetIntStat(Agent, int_stat_id.CustomerCount)

                # Update investment billboard (right side)
                InvestmentBillboard.SetText("{Loc.G(Agent, "Marketing Investment")}\n${CurrentInvestment.ToShortNumberString()}")

                # Update customer billboard (left side)
                CustomerBillboard.SetText("{Loc.G(Agent, "Customers")}\n{CurrentCustomers.ToShortNumberString()}")

    # Calculate expected revenue per second for balancing
    CalculateRevenuePerSecond<public>(Investment:int, Customers:int):float=
        Customers * CustomerRevenueRate

    # Calculate target customers for given investment and time
    CalculateTargetCustomers<public>(Investment:int, TimeInSeconds:float):int=
        if(Investment <= 0):
            return 0

        InvestmentFactor := Investment / 1000.0
        DiminishingFactor := Pow(DiminishingReturnsFactor, Investment / 10000.0)
        CustomerAcquisitionRate := BaseCustomerAcquisitionRate * InvestmentFactor * DiminishingFactor

        Round[CustomerAcquisitionRate * TimeInSeconds] or 0

    # Test balance calculations
    TestBalance<public>():void=
        # Test case 1: $100,000 investment + 30 minutes (1800 seconds) should give ~$50/s
        Investment100k := 100000
        TimeInSeconds := 1800.0 # 30 minutes

        Customers := CalculateTargetCustomers(Investment100k, TimeInSeconds)
        RevenuePerSecond := CalculateRevenuePerSecond(Investment100k, Customers)

        Print("Balance Test Results:")
        Print("Investment: ${Investment100k}")
        Print("Time: {TimeInSeconds} seconds (30 minutes)")
        Print("Expected customers: {Customers}")
        Print("Expected revenue per second: ${RevenuePerSecond}")
        Print("Target revenue per second: $50")

        # Test case 2: $1,000 investment should generate some revenue
        Investment1k := 1000
        Customers1k := CalculateTargetCustomers(Investment1k, 60.0) # 1 minute
        Revenue1k := CalculateRevenuePerSecond(Investment1k, Customers1k)

        Print("Small investment test:")
        Print("Investment: ${Investment1k}")
        Print("Customers after 1 minute: {Customers1k}")
        Print("Revenue per second: ${Revenue1k}")
