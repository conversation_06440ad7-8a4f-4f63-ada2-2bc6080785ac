using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga
using. VNotifications
using. VCoop

var SaveMap:weak_map(player, save_data) = map{}

save_data := class<final><persistable>:
    Ver:int = 1 # Version
    SId:int = 0 # SessionId

    T:int = 0 # Tokens
    G:int = 0 # Gold
    Po:int = 0 # Points
    PL:int = 0 # Playtime
    W:int = 0 # Wood
    H:[]string = array{} # House
    P:[]int = array{} # Pets levels
    R:int = 0 # Rebirths
    UM:[]string = array{} #UnlockedMissions
    GL:int = 0 # GoldGainLevel
    US:[]string = array{} #UnlockedSkills
    IS:[string]int = map{} #IntStats
    FS:[string]float = map{} #FloatStats
    LS:[string]logic = map{} #LogicStats

save_data_var<public> := class:
    var Version:int = 0 # Version
    var SessionId:int = 0 # SessionId

    Gold :observable_value_int= observable_value_int:
        Value := 0
    Wood :observable_value_int= observable_value_int:
        Value := 0
    Points :observable_value_int= observable_value_int:
        Value := 0
    var Playtime :int= 0

    var House:[]string = array{}
    var PetsLevels:[]int = array{}
    var Rebirths:int = 0
    var UnlockedMissions:[]string = array{}
    GoldGainLevel :observable_value_int= observable_value_int:
        Value := 0
    var UnlockedSkills:[]string = array{}
    var IntStats:[string]int = map{} #IntStats
    var FloatStats:[string]float = map{} #FloatStats
    var LogicStats:[string]logic = map{}

    ToPersistable(SaveGoldEnabled:logic)<transacts>:save_data=
        save_data:
            Ver := Version
            SId := SessionId
            G := SaveGoldEnabled? and Gold.Get() or 0
            Po := Points.Get()
            H := House
            R := Rebirths
            W := Wood.Get()
            P := PetsLevels
            UM := UnlockedMissions
            GL := GoldGainLevel.Get()
            US := UnlockedSkills
            IS := IntStats
            FS := FloatStats
            LS := LogicStats
            PL := Playtime

    Set(S:save_data, SaveGoldEnabled:logic):void=
        set IntStats = S.IS
        set Playtime = S.PL
        set FloatStats = S.FS
        set LogicStats = S.LS
        set Version = S.Ver
        set SessionId = S.SId
        if(SaveGoldEnabled?):
            Gold.Set(S.G)
        else:
            Gold.Set(0)

        Points.Set(S.Po)
        set House = S.H
        set Rebirths = S.R
        Wood.Set(S.W)
        set PetsLevels = S.P
        set UnlockedMissions = S.UM
        GoldGainLevel.Set(S.GL)
        set UnlockedSkills = S.US

int_stat_id<public> := enum:
    GoldGain,
    GoldCrit,
    GoldCritAmount,
    Kills,
    DailyRewardsQuestsCompleted,
    MarketingInvestment,
    CustomerCount

(Val:int_stat_id).ToString<public>()<transacts>:string=
    case (Val):
        int_stat_id.GoldGain => "GG"
        int_stat_id.GoldCrit => "GC"
        int_stat_id.GoldCritAmount => "GCA"
        int_stat_id.Kills => "KILL"
        int_stat_id.DailyRewardsQuestsCompleted => "DRQC"
        int_stat_id.MarketingInvestment => "MI"
        int_stat_id.CustomerCount => "CC"

(Val:string).ToIntStat<public>()<decides><transacts>:int_stat_id=
    case (Val):
        "GG" => int_stat_id.GoldGain
        "GC" => int_stat_id.GoldCrit
        "GCA" => int_stat_id.GoldCritAmount
        "KILL" => int_stat_id.Kills
        "DRQC" => int_stat_id.DailyRewardsQuestsCompleted
        "MI" => int_stat_id.MarketingInvestment
        "CC" => int_stat_id.CustomerCount

# CloneSave<constructor>(Old:save_data)<transacts> := save_data:
# 	Gold := Old.Gold


# player_stats_table := class<final><persistable>:
# 	Version:int = 0
# 	Value:int = 2

# 	Clone()<transacts>:void=
# 		player_stats_table:
# 			Version := Self.Version


save_system_balance<public> := class(class_interface):
    SaveEnabled<public> :logic
    SaveGoldEnabled<public> :logic
    MTimeToSaveInSec<public> :?float



save_system<public> := class(auto_creative_device, i_init, i_init_per_player_async):
    @editable MSaveCompletedMsgDevice:?hud_message_device = false

    var B :?save_system_balance= false
    var Notifs :?notifications_system= false
    var SaveMapVar:[player]save_data_var = map{}
    var AgentsInited:[agent]int = map{}

    var PlayerInitedEv:[agent]event() = map{}
    var Coop :?coop= false

    DeleteSave<public>(Agent:agent):void=
        if(Player := player[Agent]):
            NewSave := save_data_var:
                Rebirths := SaveMapVar[Player].Rebirths or 0
            if. set SaveMapVar[Player] = NewSave
            Save(Player, NewSave)

    WaitForSaveInitedForPlayer<public>(Agent:agent)<suspends>:void=
        if(AgentsInited[Agent]):
            return

        if(Ev := PlayerInitedEv[Agent]):
            Ev.Await()
        else:
            Ev := event(){}
            if. set PlayerInitedEv[Agent] = Ev
            Ev.Await()

    Init<override>(Container:vcontainer):void=
        set Coop = Container.ResolveOp[coop] or Err()
        set B = Container.ResolveOp[save_system_balance] or Err()
        set Notifs = Container.ResolveOp[notifications_system] or Err()

    InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
        if(Coop.G().CoopAdmin? <> Agent):
            return
        Print("save InitPlayerAsync"); Sleep(0.0)
        if(AgentsInited[Agent]):
            return

        InitedEv := if(Ev := PlayerInitedEv[Agent]):
            Ev
        else:
            Ev := event(){}
            if. set PlayerInitedEv[Agent] = Ev
            Ev

        var Data :save_data_var= save_data_var{}

        TimerUi := make_save_timer_generated()
        var MPlayer:?player = false

        VB := B.G()

        race:
            PlayerRemoved.Await()
            block:
                Player := Agent.WaitForPlayerActive()
                set MPlayer = option. Player
                if. set SaveMapVar[Player] = Data
                if(VB.SaveEnabled?):

                    if(var OldSave :save_data= SaveMap[Player]):
                        LPrint("Loading existing save")

                        # if(Ki := OldSave.IS["KILL"]):
                        # 	Print("OldSave Persistable kills saving: {Ki}")
                        # else:
                        # 	LPrint("OldSave Kills missing")
                        if(OldSave.Ver = 0):
                            set OldSave = save_data{}
                            Data.Set(OldSave, VB.SaveGoldEnabled)
                            Save(Player, Data)
                        else:
                            Data.Set(OldSave, VB.SaveGoldEnabled)
                    else:
                        LPrint("No save found, new player")
                        if. set SaveMap[Player] = save_data{}

                InitedEv.Signal()
                if. set AgentsInited[Agent] = 1

                if(not VB.SaveEnabled?):
                    Sleep(Inf)

                if(TimeToSaveInSec := VB.MTimeToSaveInSec?):
                    var CurTimeToSaveInSec :float= TimeToSaveInSec
                    Agent.AddToPlayerUi(TimerUi.SaveTimer)
                    loop:
                        Sleep(1.0)
                        set CurTimeToSaveInSec = CurTimeToSaveInSec - 1.0
                        TimerUi.TimerText.SetText(CurTimeToSaveInSec.TimeToStringMinsAndSeconds().ToMessage())
                        if(CurTimeToSaveInSec <= 0.0):
                            set CurTimeToSaveInSec = TimeToSaveInSec
                            Save(Player, Data)
                            if(SaveCompletedMsgDevice := MSaveCompletedMsgDevice?):
                                SaveCompletedMsgDevice.Show(Agent)
                            else:
                                Notifs.G().ShowTopNotification(Agent, ToMsg("SAVING GAME..."))

                            Sleep(2.0)
                else:
                    loop:
                        Sleep(10.0)
                        Save(Player, Data)



        Agent.RemoveFromPlayerUi(TimerUi.SaveTimer)
        set PlayerInitedEv = PlayerInitedEv.WithRemoved(Agent)
        set AgentsInited = AgentsInited.WithRemoved(Agent)
        if(Player := MPlayer?):
            set SaveMapVar = SaveMapVar.WithRemoved(Player)

    # TestPrint(Agent:agent):void=
    # 	if(Savee := GetPlayerSave[Agent]):
    # 		LPrint("Init save, house parts count: {Savee.House.Length}")
    # 		LPrint("Init save")

    OnRebirth<public>(Agent:agent):void=
        if(Data := SaveMapVar[Agent.GetPlayerActive[]]):
            set Data.House = array{}
            set Data.Rebirths += 1
            Data.Gold.Set(0)
            Data.GoldGainLevel.Set(0)
            Data.Wood.Set(0)
            set Data.UnlockedMissions = array{}
            set Data.IntStats = map{}
            set Data.FloatStats = map{}
            set Data.LogicStats = map{}
            set Data.Playtime = 0
            if(Player := player[Agent]):
                Save(Player, Data)
            else:
                LError()
            #TODO reset everything

    OnHousePartUnlocked<public>(Agent:agent, Id:string):void=
        if(Data := SaveMapVar[Agent.GetPlayerActive[]]):
            set Data.House += array{Id}

    GetPlayerSave<public>(Agent:agent)<decides><transacts>:save_data_var=
        # if(not Agent.GetPlayerActive[]):
        # 	LPrint("player not active")
        if(not SaveMapVar[Agent.GetPlayerActive[]]):
            LPrint("SaveMapVar not added")

        SaveMapVar[Agent.GetPlayerActive[]]

    GetHouseMap<public>(Agent:agent)<decides><transacts>:[]string=
        Player := Agent.GetPlayerActive[]
        SaveMapVar[Player].House

    Save<private>(Player:player, Data:save_data_var):void=
        if:
            Player.IsActive[]
            Persistable := Data.ToPersistable(B.G().SaveGoldEnabled)
            set SaveMap[Player] = Persistable
            # if(Ki := Persistable.IS["KILL"]):
            # 	Print("Persistable kills saving: {Ki}")
            # else:
            # 	LPrint("Kills missing")

        # TestPrint(Player)

    SetGold(Player:player, Value:int):void=
        if(Go := SaveMapVar[Player]):
            Go.Gold.Set(Value)

    AddGold(Player:player, Value:int):void=
        if(Go := SaveMapVar[Player]):
            Go.Gold.Add(Value)

    AddPetLevels<public>(Player:player, Zone:int, Levels:int):void=
        if(Go := SaveMapVar[Player]):
            for(X := 0..Zone):
                if(not Go.PetsLevels[X]):
                    set Go.PetsLevels += array{0}
                if(X = Zone,
                    CurLevel := Go.PetsLevels[X]
                ):
                    if. set Go.PetsLevels[X] = CurLevel + Levels

    AddWood(Player:player, Value:int):void=
        if(Go := SaveMapVar[Player]):
            Go.Wood.Add(Value)

    ClearPets<public>(Agent:agent)<decides><transacts>:void=
        P := player[Agent]
        set SaveMapVar[P].PetsLevels = array{}


    GetRebirths<public>(Agent:agent)<transacts>:int=
        if(P := SaveMapVar[Agent.GetPlayerActive[]]):
            P.Rebirths
        else:
            LError()
            0

    GetPets<public>(Agent:agent)<decides><transacts>:[]int=
        P := Agent.GetPlayerActive[]
        SaveMapVar[P].PetsLevels

    GetPoints<public>(Player:player)<transacts>:int=
        if(Go := SaveMapVar[Player].Points):
            return Go.Get()

        LError()
        0

    GetPlaytime<public>(Player:player)<transacts>:int=
        if(Go := SaveMapVar[Player]):
            return Go.Playtime

        0

    AddPlaytime<public>(Player:player, Value:int):void=
        if(Go := SaveMapVar[Player]):
            set Go.Playtime += Value

    AddPoints(Player:player, Value:int):void=
        if(Go := SaveMapVar[Player]):
            Go.Points.Add(Value)

    GetWood(Player:player)<transacts>:int=
        if(Go := SaveMapVar[Player].Wood):
            return Go.Get()

        LError()
        0

    GetGold<public>(Player:player)<transacts>:int=
        if(Go := SaveMapVar[Player].Gold):
            return Go.Get()

        LError()
        0

    GetIntStat<public>(Agent:agent, Id:int_stat_id)<transacts>:int=
        if(Go := SaveMapVar[player[Agent]].IntStats[Id.ToString()]):
            return Go
        0
    GetIntStat<public>(Agent:agent, Id:string)<transacts>:int=
        if(Go := SaveMapVar[player[Agent]].IntStats[Id]):
            return Go
        0

    SetIntStat<public>(Agent:agent, Id:int_stat_id, Val:int)<transacts>:void=
        if(PlayerData := SaveMapVar[player[Agent]]):
            if. set SaveMapVar[player[Agent]].IntStats[Id.ToString()] = Val

    SetIntStat<public>(Agent:agent, Id:string, Val:int)<transacts>:void=
        if(PlayerData := SaveMapVar[player[Agent]]):
            if. set SaveMapVar[player[Agent]].IntStats[Id] = Val

    GetFloatStat<public>(Agent:agent, Id:string)<decides><transacts>:float=
        SaveMapVar[player[Agent]].FloatStats[Id]

    SetFloatStat<public>(Agent:agent, Id:string, Val:float)<transacts>:void=
        if(PlayerData := SaveMapVar[player[Agent]]):
            if. set SaveMapVar[player[Agent]].FloatStats[Id] = Val

    GetLogicStat<public>(Agent:agent, Id:string, DefaultValue:logic)<transacts>:logic=
        SaveMapVar[player[Agent]].LogicStats[Id] or DefaultValue

    SetLogicStat<public>(Agent:agent, Id:string, Val:logic)<transacts>:void=
        if(PlayerData := SaveMapVar[player[Agent]]):
            if. set SaveMapVar[player[Agent]].LogicStats[Id] = Val

    IncreaseIntStat<public>(Agent:agent, Id:int_stat_id)<transacts>:void=
        IncreaseIntStat(Agent, Id.ToString())

    IncreaseIntStat<public>(Agent:agent, Id:string)<transacts>:void=
        if(PlayerData := SaveMapVar[player[Agent]]):
            Val := 1 + (PlayerData.IntStats[Id] or 0)
            if. set SaveMapVar[player[Agent]].IntStats[Id] = Val
            # if(Kills := SaveMapVar[player[Agent]].IntStats[Id]):
            # 	Print("Kills increased new value: {Kills}")
            # else:
            # 	Print("falseee failed")

        else:
            LError()

    GetMissionsUnlocked <public>(Agent:agent)<transacts>:[]string=
        if(Go := SaveMapVar[Agent.GetPlayerActive[]].UnlockedMissions):
            return Go

        if(not Agent.GetPlayerActive[]):
            LErrorPrint("GetMissionsUnlocked PLAYER NOT ACTIVE!!")
        else:
            LError()
        array{}
    AddMissionUnlocked <public>(Agent:agent, Mission:string)<transacts>:void=
        if(UN := SaveMapVar[Agent.GetPlayerActive[]].UnlockedMissions):
            if(not UN.Find[Mission]):
                if. set SaveMapVar[Agent.GetPlayerActive[]].UnlockedMissions = UN + array{Mission}
        else:
            LError()
    GetSkillsUnlocked <public>(Agent:agent)<transacts>:[]string=
        if(Go := SaveMapVar[Agent.GetPlayerActive[]].UnlockedSkills):
            return Go

        LError()
        array{}

    AddSkillUnlocked <public>(Agent:agent, Skill:string)<transacts>:void=
        if(UN := SaveMapVar[Agent.GetPlayerActive[]].UnlockedSkills):
            if. set SaveMapVar[Agent.GetPlayerActive[]].UnlockedSkills = UN + array{Skill}
        else:
            LError()

# var PlayerStatsMap:weak_map(player, player_stats_table) = map{}

# player_stats_table := class<computes><final><persistable>:
# 	Version:int = 0

# 	Clone()<transacts> :player_stats_table=
# 		Self

# player_stat := class<final><persistable>:
# 	CurrentValue:int = 0
# 	HighestValue:int = 0

# 	GetUpdated(NewValue:int)<transacts>:player_stat=
# 		player_stat:
# 			CurrentValue := NewValue
# 			HighestValue := if. NewValue > HighestValue then NewValue else HighestValue

# player_stats_managerr := class():

# 	GetPlayerStats(Agent:agent)<decides><transacts>:player_stats_table=
# 		Player := player[Agent]
# 		PlayerStatsMap[Player]

# 	InitializeAllPlayers(Players:[]player):void =
# 		for (Player : Players):
# 			InitializePlayer(Player)

# 	InitializePlayer(Player:player):void=
# 		if:
# 			not PlayerStatsMap[Player]
# 			set PlayerStatsMap[Player] = player_stats_table{}

# 	IncreaseWins(Agent:agent):void=
# 		if:
# 			PlayerStatsTable := GetPlayerStats[Agent]
# 			WinStat := PlayerStatsTable.Wins

# 			set PlayerStatsMap[player[Agent]] = player_stats_table:
# 				PlayerStatsTable.ClonePlayerStatsTable<constructor>()
# 				Wins := WinStat.GetUpdated(WinStat.CurrentValue+1)

# 	IncreaseLosses(Agent:agent):void=
# 		if:
# 			PlayerStatsTable := GetPlayerStats[Agent]
# 			LossesStat := PlayerStatsTable.Losses

# 			set PlayerStatsMap[player[Agent]] = player_stats_table:
# 				PlayerStatsTable.ClonePlayerStatsTable<constructor>()
# 				Losses := LossesStat.GetUpdated(LossesStat.CurrentValue+1)

# 	RecordScore(Agent:agent, Score:int):void=
# 		if:
# 			PlayerStatsTable := GetPlayerStats[Agent]
# 			ScoreStat := PlayerStatsTable.Score

# 			set PlayerStatsMap[player[Agent]] = player_stats_table:
# 				PlayerStatsTable.ClonePlayerStatsTable<constructor>()
# 				Score := ScoreStat.GetUpdated(Score)


# MakePlayerStatsTable<constructor>(OldTable:player_stats_table)<transacts> := player_stats_table:
# 	Version := OldTable.Version
# 	Score := OldTable.Score
# 	Wins := OldTable.Wins
# 	Losses := OldTable.Losses
#
# player_stat := class<final><persistable>:
# 	CurrentValue:int = 0
# 	HighestValue:int = 0

# 	SetHighestValue(NewValue:int)<transacts>:int=
# 		if (NewValue > HighestValue):
# 			NewValue
# 		else:
# 			HighestValue

# MakeUpdatedPlayerStat<constructor>(OldStats:player_stat, NewValue:int)<transacts> := player_stat:
# 	CurrentValue := NewValue
# 	HighestValue := OldStats.SetHighestValue(NewValue)

# stat_type := class<computes><unique><abstract>:
# 	DebugString():string

# StatType := module:

# 	score_stat<public> := class<computes><unique>(stat_type):
# 		DebugString<override>():string = "Score"

# 	win_stat<public> := class<computes><unique>(stat_type):
# 		DebugString<override>():string = "Win"

# 	loss_stat<public> := class<computes><unique>(stat_type):
# 		DebugString<override>():string = "Loss"

# 	Score<public>:score_stat = score_stat{}
# 	Win<public>:win_stat = win_stat{}
# 	Loss<public>:loss_stat = loss_stat{}

# player_stats_manager := class():

# 	GetPlayerStats(Agent:agent)<decides><transacts>:player_stats_table=
# 		var PlayerStats:player_stats_table = player_stats_table{}
# 		if:
# 			Player := player[Agent]
# 			PlayerStatsTable := PlayerStatsMap[Player]
# 			set PlayerStats = MakePlayerStatsTable(PlayerStatsTable)
# 		PlayerStats

# 	InitializeAllPlayers(Players:[]player):void =
# 		for (Player : Players):
# 			InitializePlayer(Player)

# 	InitializePlayer(Player:player):void=
# 		if:
# 			not PlayerStatsMap[Player]
# 			set PlayerStatsMap[Player] = player_stats_table{}

# 	RecordPlayerStat(Agent:agent, Stat:stat_type, ?Score:int = 0):void=
# 		if:
# 			Player := player[Agent]
# 			PlayerStatsTable := PlayerStatsMap[Player]
# 			if(Stat = StatType.Score):
# 				ScoreStat := PlayerStatsTable.Score

# 				set PlayerStatsMap[Player] = player_stats_table:
# 					MakePlayerStatsTable<constructor>(PlayerStatsTable)
# 					Score := MakeUpdatedPlayerStat(ScoreStat, Score)

# 			else if(Stat = StatType.Win):
# 				WinsStat := PlayerStatsTable.Wins

# 				set PlayerStatsMap[Player] = player_stats_table:
# 					MakePlayerStatsTable<constructor>(PlayerStatsTable)
# 					Wins := MakeUpdatedPlayerStat(WinsStat, WinsStat.CurrentValue+1)

# 			else if(Stat = StatType.Loss):
# 				LossesStat := PlayerStatsTable.Losses

# 				set PlayerStatsMap[Player] = player_stats_table:
# 					MakePlayerStatsTable<constructor>(PlayerStatsTable)
# 					Losses := MakeUpdatedPlayerStat(LossesStat, LossesStat.CurrentValue+1)