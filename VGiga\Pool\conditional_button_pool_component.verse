#gen
#pool_component
#conditional_button_device
#id-c7bc3a50-23e5-42ff-b68e-42ff405801d0
using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using.  VGiga


		
conditional_button_device_pool_component_c<public>(Area:area_interface, D:creative_device)<transacts>:conditional_button_device_pool_component=
	conditional_button_device_pool_component:
		Area := Area
		D := D
	.Init()

conditional_button_device_pool_component<public> := class:
	Area<public>:area_interface
	var FreeCreativeDevices : []conditional_button_device = array{}
	D<public>:creative_device

	Init()<transacts>:conditional_button_device_pool_component=
		set FreeCreativeDevices = for(Obj : D.GetDevicesInArea(conditional_button_device, Area)):
			Obj
		Self

	GetAllFree<public>():[]conditional_button_device=
		FreeCreativeDevices

	Rent<public>()<decides><transacts>:conditional_button_device=
		if(CreativeDevice := FreeCreativeDevices[0]
			Val := FreeCreativeDevices.RemoveElement[0]
			set FreeCreativeDevices = Val
		):
			return CreativeDevice
		else:
			FailError[]
			return conditional_button_device{}

	RentDisposable<public>()<decides><transacts>:pooled_editable_conditional_button_device=
		Device := Rent[]
		pooled_editable_conditional_button_device:
			Device := Device
			MyPool := Self

	Return<public>(CreativeDevice:conditional_button_device):void=
		if. CreativeDevice.TeleportTo[Area.GetCenterPoint(), rotation{}]
		set FreeCreativeDevices += array. CreativeDevice


pooled_editable_conditional_button_device<public> := class(i_disposable):
	MyPool<public>:conditional_button_device_pool_component
	Device<public>:conditional_button_device

	Dispose<override>():void=
		MyPool.Return(Device)

	
#id-c7bc3a50-23e5-42ff-b68e-42ff405801d0