
using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }

#gen
#map_t1_t2
#agent
#p_data2
#id-6de69f4a-b964-4f00-b204-a2bffc25f50e
map_agent_p_data2<public> := class:
	var DataMap:[agent]p_data2 = map{}
	var MDataSetEvent:?event(tuple(agent, p_data2)) = false

	Get<public>(Key:agent)<decides><transacts>:p_data2=
		DataMap[Key]

	GetErr<public>(Key:agent)<decides><transacts>:p_data2=
		if(Data := DataMap[Key]):
			Data
		else:
			FailError[]
			Err()

	GetMap<public>()<transacts>:[agent]p_data2=
		DataMap

	GetOrAwait<public>(Key:agent)<suspends>:p_data2=
		if(Data := DataMap[Key]):
			Data
		else:
			if(DataSetEvent := MDataSetEvent?):
				var Return:?p_data2 = false
				loop:
					Data := DataSetEvent.Await()
					if(Data(0) = Key):
						set Return = option. Data(1)
						break
				Return.G()
				# DataSetEvent.AwaitForData(Key)
			else:
				Ev := event(tuple(agent, p_data2)){}
				set MDataSetEvent = option. Ev
				# Ev.AwaitForData(Key)
				var Return:?p_data2 = false
				loop:
					Data := Ev.Await()
					if(Data(0) = Key):
						set Return = option. Data(1)
						break
				Return.G()
			

	Set<public>(Key:agent, Data:p_data2):void=
		if. set DataMap[Key] = Data
		if(DataSetEvent := MDataSetEvent?):
			DataSetEvent.Signal((Key, Data))

	Remove<public>(Key:agent)<transacts>:void=
		set DataMap = DataMap.WithRemoved(Key)
#id-6de69f4a-b964-4f00-b204-a2bffc25f50e


p_data2<public> := class(){
	
}