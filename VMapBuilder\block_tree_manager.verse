using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
using. VPropSpawner

block_tree_manager_spawner_data := struct:
	Tr<public>:transform
	TrunkAsset<public>:creative_prop_asset
	LeavesAsset<public>:creative_prop_asset
	PropSpawner<public>:prop_spawner
	
BlockTreeSpawner := module:
	SpawnRespawningTreeAsync<public>(Data:block_tree_manager_spawner_data)<suspends>:i_disposable=
		CancelEvent := event(){}
		Return := disposable_as_event_c(CancelEvent)
		Tree := Spawn(Data, CancelEvent)
		spawn. KeepRespawningTree(Data, Tree, CancelEvent)
		Return

	KeepRespawningTree(Data:block_tree_manager_spawner_data, TreeManager:block_tree_manager, CancelEvent:event())<suspends>:void=
		var MCurTree :?block_tree_manager= option. TreeManager

		loop:
			if(CurTree := MCurTree?):
				set MCurTree = race:
					block:
						# CurTree.DisposedEvent.Await() # this is the same, CancelEvent is always passed
						CancelEvent.Await()
						false
					block:
						CurTree.WantsToRespawn.Await()
						Manager := Spawn(Data, CancelEvent)
						option. Manager
			else:
				break



	Spawn(Data:block_tree_manager_spawner_data, DisposeTreeEvent:event())<suspends>:block_tree_manager=
		Tr := Data.Tr
		TrunkAsset := Data.TrunkAsset
		LeavesAsset := Data.LeavesAsset
		PropSpawner := Data.PropSpawner

		Pos := Tr.Translation
		Rotation := Tr.Rotation
		Scale := Tr.Scale


		TrunkHeight := GetRandomInt(4, 6)
		TrunkProps := for(Z := 0..TrunkHeight-1):
			PropSpawner.Spawn(TrunkAsset,
				transform:
					Translation := Pos + Vector3(0.0, 0.0, Z * BSizeF)
					Rotation := Rotation
					Scale := Scale
			)
		
		LeavesStartHeight := GetRandomInt(1, 2)
		LeavesAround := for(Z := LeavesStartHeight..TrunkHeight
			X := -1..1
			Y := -1..1
			(X,Y) <> (0,0)
		):
			PropSpawner.Spawn(LeavesAsset,
				transform:
					Translation := Pos + Vector3(X * BSizeF, Y * BSizeF, Z * BSizeF)
					Rotation := Rotation
					Scale := Scale
			)

		LeavesAtTop := for(Z := TrunkHeight..TrunkHeight+1):
			PropSpawner.Spawn(LeavesAsset,
				transform:
					Translation := Pos + Vector3(0.0, 0.0, Z * BSizeF)
					Rotation := Rotation
					Scale := Scale
			)

		Manager := block_tree_manager:
			DisposedEvent := DisposeTreeEvent
			LeavesProps := LeavesAround + LeavesAtTop
			TrunkProps := TrunkProps


		spawn. Manager.InitAsync()
		Manager


block_tree_manager := class(i_disposable):
	DisposedEvent:event() = event(){}
	WantsToRespawn:event() = event(){}
	
	TrunkProps:[]?creative_prop_unique
	LeavesProps:[]?creative_prop_unique

	InitAsync()<suspends>:void=
		if(BottomTrunkProp := TrunkProps[0]?):
			RandCheckDelay := GetRandomFloat(9.0,11.0)
			race:
				DisposedEvent.Await()

				# Respawn Tree
				loop:
					Sleep(RandCheckDelay)
					if(BottomTrunkProp.IsValid[])
					{}else:
						RemoveLeavesProps()
						Sleep(30.0)
						WantsToRespawn.Signal()
						break
							
			RemoveProps()
		else:
			LError()

	RemoveLeavesProps():void=
		for(MProp:LeavesProps
			Prop := MProp?
		):
			Prop.Dispose()

	RemoveProps():void=
		for(MProp:TrunkProps
			Prop := MProp?
		):
			Prop.Dispose()
		RemoveLeavesProps()
		
	
	Dispose<override>():void=
		DisposedEvent.Signal()
		
	