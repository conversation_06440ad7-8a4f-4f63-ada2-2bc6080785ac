using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }

map_VAR1_event_VAR2<public> := class():
	var Events:[VAR1]event(VAR2) = map{}

	Signal<public>(Key:VAR1, Val:VAR2):void=
		if(Ev := Events[Key]):
			Ev.Signal(Val)
		else. LError()

	Get<public>(Key:VAR1)<decides><transacts>:event(VAR2)=
		if(Ev := Events[Key]):
			Ev
		else:
			FailError[]
			Err()

	Add<public>(Key:VAR1)<transacts>:event(VAR2)=
		if(Ev2 := Events[Key]):
			LError()
			return Ev2
		Ev := event(VAR2){}
		if. set Events[Key] = Ev
		Ev

	Add<public>(Key:VAR1, Ev:event(VAR2))<transacts>:void=
		if(Ev2 := Events[Key]):
			LError()
		else:
			if. set Events[Key] = Ev

	Remove<public>(Key:VAR1)<transacts>:void=
		set Events = Events.WithRemoved(Key)