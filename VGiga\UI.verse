using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }

ui_window<public> := class<abstract>:
	ShowFor<public>(PlayerUI:player_ui)<suspends>:void

Anchors<public>(MinX:float, MaxX:float, MinY:float, MaxY:float)<transacts> : anchors=
	anchors{Minimum := vector2{X := MinX, Y := MinY}, Maximum := vector2{X := MaxX, Y := MaxY}}
	
AnchorsXY<public>(X:float, Y:float)<transacts> : anchors=
	anchors:
		Minimum := vector2{X := X, Y := Y}
		Maximum := vector2{X := X, Y := Y}
		
AnchorsPctSize<public>(X:float, XSize:float, Y:float, YSize:float)<transacts> : anchors=
	anchors:
		Minimum := vector2{X := X - XSize/2.0, Y := Y - YSize/2.0}
		Maximum := vector2{X := X + XSize/2.0, Y := Y + YSize/2.0}

Margin<public>(L:float, R:float, T:float, B:float)<transacts> : margin=
	margin{Left := L, Top := T, Right := R, Bottom := B}

Offsets<public>(Top:float, Left:float, Right:float, Bottom:float) : margin=
	margin{Top := Top, Left := Left, Right := Right, Bottom := Bottom}

Alignment<public>(X:float, Y:float) : vector2=
	vector2{X := X, Y := Y}

(Text:text_block).SetShadowOpacityRet<public>(InOpacity:type {_X:float where 0.000000 <= _X, _X <= 1.000000}):text_block=
	Text.SetShadowOpacity(InOpacity)
	Text

(Widget:widget).Collapse<public>():void=
	Widget.SetVisibility(widget_visibility.Collapsed)
(Widget:widget).Show<public>():void=
	Widget.SetVisibility(widget_visibility.Visible)
(Widget:widget).Hide<public>():void=
	Widget.SetVisibility(widget_visibility.Hidden)

text_block_c<public>(TextColor:color):text_block=
	T := text_block:
		DefaultShadowOffset := option. Vector2One
		# DefaultShadowColor := TextColor
		DefaultTextColor := TextColor
	T.SetShadowOpacity(1.0)
	T

canvas_c<public>(Slots:[]canvas_slot)<transacts>:canvas=
	canvas:
		Slots := Slots