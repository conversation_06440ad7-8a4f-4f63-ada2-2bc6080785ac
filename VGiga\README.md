# GigaVERSE

Library that adds what's missing in Verse and more...

## Setup
1. Clone/unzip this plugin to your UEFN project: `<ProjectName>/Plugins/<ProjectName>/Content/Giga`
2. Add `using { VGiga }` to your files

For ex. Math.verser file should be in `<ProjectName>/Plugins/<ProjectName>/Content/Giga/Math.verse`

## Features:

**Quick string to message for UI**
    Usage:
        MyMessage := "hello world".ToMessage()
        UIButton := button_loud{DefaultText := "hello world".ToMessage()}

**Float to Int operators**
    Usage:
        MyFloat := Float(MyInt)
        MyFloat := MyInt + MyFloat
        MyFloat := MyInt * MyFloat
        etc

**Array sort (quick sort)**
    Usage: 
        SortedArray := MyIntArray.Sort(IntComparer)
        SortedArray := MyFloatArray.Sort(FloatComparer)
        SortedArray := MyCustomArray.Sort(CustomComparer) - requires custom Comparer, see Array.verse
        SortedArray := MyFloatArray.SortDescending(IntComparer)
        etc

**Array reverse**
    Usage:
        ReversedArray := MyIntArray.Reverse()

## Contributions:
    1. Always add <transacts>, <varies> or <computes> to methods if possible.
    2. Don't use any one line keywords (`if then, for do, if ().` etc) for simplicity
    3. Skip `return` only if it's close to the method's end

## Wishlist for UEFN/Epic
1. Parametric types are bugged and sometimes can't be used outside of a file they are located. Seems to be related to compilation order that's looks unordered. It's a bit random and can start breaking after a UEFN restart.
2. Method with the same name and parameters count BUT different parameters types (or return types) shouldn't ambiguous  