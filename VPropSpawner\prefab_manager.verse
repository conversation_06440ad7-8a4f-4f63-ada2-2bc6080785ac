using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 

props_manager<public> := class:
	Definition<public>:prefab_definition_devic
	var<internal> Props<public>:[]creative_prop_unique = array{}
	var LastTr<private>:transform = transform{}

	var WatchingPropDestroyed<private>:logic= false
	var PropsValidCount<private>:int= 0
	PropDestroyedEv<private>:event(creative_prop_unique)= event(creative_prop_unique){}
	DisposedEv<public>:event() = event(){}
	var Disposed <public> :logic= false

	GetPropDestroyedEv<public>():event(creative_prop_unique)=
		if(not WatchingPropDestroyed?):
			set WatchingPropDestroyed = true
			for(Prop:Props):
				set PropsValidCount += 1
				spawn. WatchPropDisposedAsync(Prop)
		PropDestroyedEv
		
	WatchPropDisposedAsync<private>(Prop:creative_prop_unique)<suspends>:void=
		race:
			DisposedEv.Await()
			loop:
				if(not Prop.IsValid[]):
					set PropsValidCount -= 1
					PropDestroyedEv.Signal(Prop)
					if(PropsValidCount = 0):
						Dispose()
					break
				Sleep(0.1)
		
	TeleportTo<public>(Pos:vector3, Rotation:rotation)<decides><transacts>:void=
		# LPrint("TeleportTo")
		set LastTr = transform:
			Translation := Pos
			Rotation := Rotation
			Scale := Vector3One
		
		for(I->Prop:Props
			InitTr := Definition.PrefabsTransforms[I]
			Tr := InitTr.RotateBy(Rotation).AddTranslation(Pos)
			Prop.Prop.TeleportTo[Tr]
		):
			# LPrint("TeleportTo: {Tr.Translation}")
	
	MoveToOffset<public>(Offset:vector3, Time:float):void=
		for(I->Prop:Props
			InitTr := Prop.Prop.GetTransform()
			Tr := InitTr.AddTranslation(Offset)
		):
			spawn. Prop.Prop.MoveTo(Tr, Time)

	Dispose<public>():void=
		if(not Disposed?):
			set Disposed = true
			for(Prop:Props, Prop.IsValid[]):
				Prop.Dispose()
				
			set Props = array{}
			DisposedEv.Signal()

	Spawn(Transform:transform, Spawner:prop_spawner)<suspends>:props_manager=
		for(X := 0..Definition.PrefabsTransforms.Length-1
			Prefab := Definition.Prefabs[X]
			Tr := Definition.PrefabsTransforms[X]
		):
			# var SpawnTransform:transform = I.Prop.GetTransform()
			var SpawnTransform:transform = Tr
			# set SpawnTransform.Translation = -Definition.GetTransform().Translation + SpawnTransform.Translation
			set SpawnTransform.Translation = Transform.Rotation.RotateVector( SpawnTransform.Translation)
			set SpawnTransform.Rotation = SpawnTransform.Rotation.RotateBy(Transform.Rotation)
			set SpawnTransform.Scale = SpawnTransform.Scale * Transform.Scale

			set SpawnTransform.Translation =  SpawnTransform.Translation + Transform.Translation

			MSpawned := Spawner.Spawn(Prefab.Asset, SpawnTransform)
			if(Spawned := MSpawned?):
				set Props += array. Spawned

		Self
	