<#> Quick string to message for UI
	Usage:
		MyMessage := "hello world".ToMessage()
		MyButton := button_loud{DefaultText := "hello world".ToMessage()}
		MyLogic.ToString()

(Text:string).ToMessage<public>():message=
	if(Text.Length = 0):
		EmptyMessage
	else:
		ToMessage(Text)


ToMessage<localizes>(Text:string)<computes>:message = "{Text}"
ToMsg<public><localizes>(Val:int)<computes>:message = "{Val}"
ToMsg<public><localizes>(Text:string)<computes>:message = "{Text}"

(Logic:logic).ToString<public>()<computes>:string=
	if(Logic?):
		"true"
	else:
		"false"
(Text:string).Split<public>(Delimiter:char)<transacts>:[]string=
    if(Text.Length = 0):
        return array{}
        
    var Result:[]string = array{}
    var CurrentStart:int = 0
    var FoundSplit:logic = false
    
    # Handle each character
    var I:int = 0
    loop:
        if(I >= Text.Length):
            break
            
        if(C := Text[I]):
            if(C = Delimiter):
                set FoundSplit = true
                # Add segment (empty or not) before this delimiter
                if(CurrentStart <= I):
                    if(CurrentStart < I):
                        Slice := Text.Slice[CurrentStart, I] or ""
                        set Result += array{Slice}
                    else:
                        set Result += array{""}
                set CurrentStart = I + 1
            
        set I += 1
    
    # Handle final segment
    if(CurrentStart < Text.Length):
        Slice := Text.Slice[CurrentStart, Text.Length] or ""
        set Result += array{Slice}
    else if(CurrentStart = Text.Length):
        set Result += array{""}
    
    # Return original text if no splits found
    if(not FoundSplit? and Text.Length > 0):
        array{Text}
    else:
        Result
(Text:string).Contains<public>(TextToSearch:string)<decides><transacts>:void=
	if (Text = "" or TextToSearch = ""):
		false?

	TextToSearchLen := TextToSearch.Length
	TextLen := Text.Length

	var MatchIndex :int= 0
	var I:int = 0
	loop:
		if(C := Text[I]):
			if (C = TextToSearch[MatchIndex]):
				set MatchIndex += 1
				if (MatchIndex = TextToSearchLen):
					break
			else:
				set MatchIndex = 0
			
		set I += 1
		if(I >= TextLen):
			break

	I < TextLen

# BigNumber_FormatDisplay(ValueIN : big_number):string=

(Input:int).ToShortNumberString<public>()<transacts>:string=
	big_number{Value:= Input * 1.0, Exponent := 0}.FormatDisplay()
		
(Input:float).ToString<public>(NumDigitsAfterDecimal:int)<transacts>:string =
	if:
		Multiplier := Pow(10.0, NumDigitsAfterDecimal.F())
		RoundedValue := Round[Input * Multiplier] / Multiplier
		BeforeDecimal := Floor[RoundedValue]
		AfterDecimal := Abs(Round[(RoundedValue - BeforeDecimal) * Multiplier])
		var AfterDecimalString : string = ToString(AfterDecimal)

		#pad the number after the decimal with leading zeroes
		for (It := 0..(NumDigitsAfterDecimal - AfterDecimalString.Length - 1)):
			set AfterDecimalString = array{'0'} + AfterDecimalString
	then:
		if(AfterDecimalString.Length > 0): 
			"{BeforeDecimal}.{AfterDecimalString}" 
		else:
			"{BeforeDecimal}"
	else:
		ToString(Input)

(Time:int).TimeToStringMinsAndSeconds<public>()<transacts>:string=
	(Time * 1.0).TimeToStringMinsAndSeconds()
(Time:float).TimeToStringMinsAndSeconds<public>()<transacts>:string=
	if(Time < 0.0):
		LErrorPrint("Time smaller than 0") # not supported

	if(Seconds := Floor[Time]
		SecondsBelow60 := Mod[Seconds,60]
		Mins := Floor[Seconds/60.0]
		MinutesBelow60 := Mod[Mins,60]
	):
		# var MinutesBelow60Text : string = if(MinutesBelow60 < 10):
		# 	"0{MinutesBelow60}"
		# else:
		# 	"{MinutesBelow60}"
		
		var SecondsBelow60Text : string = if(SecondsBelow60 < 10):
			"0{SecondsBelow60}"
		else:
			"{SecondsBelow60}"

		return "{MinutesBelow60}:{SecondsBelow60Text}"
	else:
		"Error"

TimeToString<public>(Time:float, ?ShowUnits:logic = false, ?RemoveMilis:logic = false)<transacts>:string=
	if(Time < 0.0):
		LErrorPrint("Time smaller than 0") # not supported

	if(Seconds := Floor[Time]
		Milis := Floor[(Time - Seconds) * 1000.0]
		SecondsBelow60 := Mod[Seconds,60]
		Mins := Floor[Seconds/60.0]
		MinutesBelow60 := Mod[Mins,60]
		Hours := Floor[Mins/60.0]
	):
		var HoursText : string = "{Hours}"
		var MinutesBelow60Text : string = if(MinutesBelow60 < 10):
			"0{MinutesBelow60}"
		else:
			"{MinutesBelow60}"
		
		var SecondsBelow60Text : string = if(SecondsBelow60 < 10):
			"0{SecondsBelow60}"
		else:
			"{SecondsBelow60}"
		
		MilisText := if (Milis < 10):
			"00{Milis}"
		else if (Milis < 100):
			"0{Milis}"
		else:
			"{Milis}"
		

		if(ShowUnits?):
			set HoursText += "h"
			set MinutesBelow60Text += "m"
			set SecondsBelow60Text += "s"

		var TimeText : string = "0"
		if(Hours > 0):
			set TimeText = "{HoursText}:{MinutesBelow60Text}:{SecondsBelow60Text}"
		else if(Mins > 0):
			set TimeText = "{MinutesBelow60Text}:{SecondsBelow60Text}"
		else if(Seconds > 0):
			set TimeText = "{SecondsBelow60Text}"
		
		if(not RemoveMilis?):
			set TimeText += if(Seconds > 0):
				".{MilisText}"
			else:
				"{MilisText}"

		return TimeText
	else:
		"Error"

BRBillboard<public> :string= "                                                                        "