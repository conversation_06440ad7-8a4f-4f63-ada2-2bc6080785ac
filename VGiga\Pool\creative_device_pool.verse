# using {/Verse.org/Simulation}
# using {/Verse.org/Simulation/Tags}
# using {/Verse.org/Assets}
# using {/Verse.org/Verse}
# using {/Verse.org/Random}
# using {/Verse.org/Colors}
# using {/Verse.org/Colors/NamedColors}
# using {/Verse.org/Native}
# using {/Verse.org/Concurrency}
# using {/UnrealEngine.com/Temporary}
# using {/UnrealEngine.com/Temporary/UI}
# using {/UnrealEngine.com/Temporary/SpatialMath}
# using {/UnrealEngine.com/Temporary/Diagnostics}
# using {/UnrealEngine.com/Temporary/Curves}
# using {/Fortnite.com/UI}
# using {/Fortnite.com/Devices}
# using {/Fortnite.com/Devices/Animation}
# using {/Fortnite.com/Devices/CreativeAnimation}
# using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
# using {/Fortnite.com/Vehicles}
# using {/Fortnite.com/Teams}
# using {/Fortnite.com/Playspaces}
# using {/Fortnite.com/Game}
# using {/Fortnite.com/FortPlayerUtilities}
# using {/Fortnite.com/Characters}
# using {/Fortnite.com/AI}

# creative_device_pool<public> := class(){
#     var FreeCreativeDevices : []creative_object_interface = array{}
# 	Tag<public>:tag
    
#     Init<public>():creative_device_pool={
#         set FreeCreativeDevices = for(
#             Obj : GetCreativeObjectsWithTag(Tag)){
#                 Obj
#             }
#         Self
#     }

#     Rent<public>()<decides><transacts>:creative_object_interface={
#         if(CreativeDevice := FreeCreativeDevices[0]
#             set FreeCreativeDevices = FreeCreativeDevices.RemoveElement[0]){
#             return CreativeDevice
#         }else{
#         	FailError[]
#             return item_granter_device{}
# 		}
#     }

#     Return<public>(CreativeDevice:creative_object_interface):void={
#         set FreeCreativeDevices += array{CreativeDevice}
#     }
# }

# # trigger_device_pool<public> := class{
# # 	var Pool:creative_device_pool_t(trigger_device)
# # }

# # creative_device_pool_t<public>(t:type) := class(){
# #     CreativeDevices<public> : []t
    
# #     # Init<public>():creative_device_pool_t(t)={
# #     #     set FreeCreativeDevices = for(
# #     #         Obj : GetCreativeObjectsWithTag(Tag)){
# #     #             Obj
# #     #         }
# #     #     Self
# #     # }

# #     # Rent<public>()<decides><transacts>:creative_object_interface={
# #     #     if(CreativeDevice := FreeCreativeDevices[0]
# #     #         set FreeCreativeDevices = FreeCreativeDevices.RemoveElement[0]){
# #     #         return CreativeDevice
# #     #     }else{
# #     #     	FailError[]
# #     #         return item_granter_device{}
# # 	# 	}
# #     # }

# #     # Return<public>(CreativeDevice:creative_object_interface):void={
# #     #     set FreeCreativeDevices += array{CreativeDevice}
# #     # }
# # }

# # # creative_device_pool_t_c<public>(Tag:tag, Cast:type{_(:creative_object_interface)<decides><transacts>:t} where t:type):creative_device_pool_t(t)={
# # # 	return creative_device_pool_t(t){
# # # 		CreativeDevices := for(Obj : GetCreativeObjectsWithTag(Tag), Casted := Cast[Obj]){
# # # 			Casted
# # # 		}
# # # 		Tag := Tag
# # # 	}
# # # } 


# # creative_device_pool_t_c<public>(Cast:type{_(:creative_object_interface)<decides><transacts>:t}, Tag:tag where t:type):creative_device_pool_t(t)={
# # 	Array:[]t = for(Obj : GetCreativeObjectsWithTag(Tag), Casted := Cast[Obj]){
# # 		Casted
# # 	}
# # 	return creative_device_pool_t(t){
# # 		CreativeDevices := Array
# # 	}
# # } 