using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 

buyables<public> := class(auto_creative_device, i_init):
	BuildingBoughtEv<public>:event(agent) = event(agent){}
	HouseBoughtEv<public>:event(agent) = event(agent){}
	CosmeticBoughtEv<public>:event(agent) = event(agent){}

	Init<override>(Container:vcontainer):void=
		Buyable := Container.ResolveAllErr(buyable)
		
		for(B:Buyable):
			spawn. OnBought(B)

	OnBought(B:buyable)<suspends>:void=
		EvToInvoke := if(B.IsCosmetic[]):
			CosmeticBoughtEv
		else:
			if(B.Id.Contains["HOUSE"]):
				HouseBoughtEv
			else:
				BuildingBoughtEv
		loop:
			Agent := B.BoughtEv.Await()
			EvToInvoke.Signal(Agent)

		


	
	