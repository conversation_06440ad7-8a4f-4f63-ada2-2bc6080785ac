using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }

spawner_for_pool<public> := class<unique>(){
    var SpawnedList:[]creative_prop_unique = array{}
    var SpawnedCount:int = 0

    Logger<public>:i_logger
    D<public>:spawner_devic_for_pool

    DespawnAll<public>()<suspends>:void={
        for(Spawned:SpawnedList){
            Spawned.Prop.Dispose()
        }
        set SpawnedCount = 0
    }


    SpawnItem<public>(Asset:creative_prop_asset, Transform:transform):?creative_prop={
        Result := SpawnProp(Asset, Transform)
        MaybeProp := Result(0)
        Success := Result(1)
        # if{SpawnedCount > 40
        #     LPrint("SpawnedCount: {SpawnedCount}")
        # }
        # GigaLogger.LogToBillboard("SpawnItemPet")
        Logger.Log(SpawnPropResultToString(Success))
		# LPrint("SpawnedCount: {SpawnedCount}, Res:{SpawnPropResultToString(Success)}")
        if(Prop := MaybeProp?){
            return option. Prop
        }else{
            LPrint("SpawnedCount: {SpawnedCount}, Res:{SpawnPropResultToString(Success)}")
            LPrintStack()
            
            ## maybe dispose unique?
            # return (creative_prop_unique{Prop := creative_prop{}}, Result)
        }
        MaybeProp
    }


    IsSpawnerFull()<decides><transacts>:void={
        SpawnedCount > 400
    }
    
    Spawn<public>(Asset:creative_prop_asset, Transform:transform)<suspends>:?creative_prop={
        var Ret :?creative_prop = false
        # LPrintStack()
        OnSpawned:event(?creative_prop) := event(?creative_prop){}
        sync{
            block{
                set Ret = OnSpawned.Await()
            }
            block{
                D.SpawnEvent.Signal(Asset, Transform, OnSpawned)
                Sleep(-1.0)
            }
        }
        Ret
    }
}