using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI} 

area_group<public> := class<concrete>(area_interface){

	var Areas:[]area_interface = array{}
	
	IsTransformInside<override>(Tr:transform)<decides><transacts>:void={
		var Inside:logic = false
		var End:int = Areas.Length - 1
		var I:int = 0
		loop{
			if(A := Areas[I], A.IsTransformInside[Tr]){
				set Inside = true
				break
			}
			set I += 1
			if(I = End){
				break
			}
		}
		Inside?
	}

	IsInside<override>(Vec:vector3)<decides><transacts>:void={
		var Inside:logic = false
		var End:int = Areas.Length - 1
		var I:int = 0
		loop{
			if(A := Areas[I], A.IsInside[Vec]){
				set Inside = true
				break
			}
			set I += 1
			if(I = End){
				break
			}
		}
		Inside?
	}

	IsInsideLog<override>(Vec:vector3)<decides><transacts>:void=
		var Inside:logic = false
		var End:int = Areas.Length - 1
		var I:int = 0
		loop:
			if(A := Areas[I].IsInsideLog[Vec]):
				set Inside = true
				break
			set I += 1
			if(I = End):
				break
		Inside?
	
	GetCenterPoint<override>()<transacts>:vector3=
		GetRandomPointInside()

	GetRandomPointInside<override>()<transacts>:vector3=
		if(A := Areas.GetRandom[]):
			return A.GetRandomPointInside()
		LError()

		return vector3{}

	AwaitAgentInside<override>(Agent:agent, PlayerExited:event())<suspends>:void={
		AwaitAgentInsideRecursive(Agent, PlayerExited, 0)
	}

	AwaitAgentInsideRecursive(Agent:agent, PlayerExited:event(), Index:int)<suspends>:void={
		if(Area:= Areas[Index]){
			race{
				AwaitAgentInsideRecursive(Agent, PlayerExited, Index + 1)
				Area.AwaitAgentInside(Agent,PlayerExited)
			}
		}else{
			if (Areas.Length > 0):
				Sleep(Inf)
			false
		}
	}

	# (Awaitables:[]type{_(tuple(:agent, :event()))<suspends>:r} where i:type, r:type).RaceMethodsData<public>(InputGetters:[](data->i), GettersData:[]data where data:type)<suspends>:?r=
	# {
	# 	RaceAwaitableMethodsData(Awaitables, InputGetters, GettersData, 0)
	# }

	# RaceAwaitableMethodsData<internal>(Awaitables:[]type{_(:i)<suspends>:r}, InputGetters:[](data->i), GettersData:[]data, Index:int where i:type, r:type, data:type)<suspends>:?r={
	# 	if(Awaitable := Awaitables[Index]
	# 		InputGetter := InputGetters[Index]
	# 		GetterData := GettersData[Index]
	# 	){
	# 		race{
	# 			RaceAwaitableMethodsData(Awaitables, InputGetters, GettersData, Index + 1)
	# 			block{
	# 				# Result:r = Awaitable(InputGetter(GetterData))
	# 				# option. Result
	# 				Sleep(Inf)
	# 				false
	# 			}
	# 		}
	# 	}else{
	# 		if (Awaitables.Length > 0):
	# 			Sleep(Inf)
	# 		false
	# 	}
	# }
}