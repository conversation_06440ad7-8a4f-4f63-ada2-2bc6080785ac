using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga
using. VGiga.Pool
using. VCustomBillboard

hit_to_continue_manipulator_tag := class(tag):

hit_to_continue_system<public> := class(auto_creative_device, i_init):
	@editable PoolArea:?area_box_editable = false

	var HitBlocksPool:?hit_to_continue_block_devic_pool_component = false
	var BillboardSpawner:?custom_billboard_spawner = false
	
	Init<override>(Container:vcontainer):void=
		PoolArea.G().Init()
		set HitBlocksPool = option. hit_to_continue_block_devic_pool_component_c(PoolArea.G(), Self)

	WaitForHitsToContinue<public>(HitsAmount:int, HitBoxPosition:vector3, CancelEv:event())<suspends>:logic=
		if(Rented := HitBlocksPool.G().RentDisposable[]):
			Rented.Device.Manipulator.G().ShowProps()
			race:
				block:
					CancelEv.Await()
					return false
				block:
					if. Rented.Device.TeleportTo[HitBoxPosition.AddZ(-200.0), rotation{}]
					spawn. Rented.Device.MoveTo(HitBoxPosition, rotation{}, 1.0)
					for(X:=0..HitsAmount-1):
						Rented.Device.SetHitsAmount(HitsAmount - X)
						Rented.Device.Manipulator.G().DamagedEvent.Await()
						Rented.Device.Manipulator.G().RestoreHealth()
			Rented.Device.Manipulator.G().HideProps()
			Rented.Dispose()
		else:
			LError()
		
		return true

