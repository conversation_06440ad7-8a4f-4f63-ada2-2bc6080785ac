using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }

i_localization_map<public> := interface(class_interface):
	GetLocalizationMap<public>():[lang][string]string
	
i_localization_map_case<public> := interface:
	Translate<public>(Lang:lang, Text:string):string
	
# localization_map_case := class(class_interface, i_localization_map_case):
# 	Translate<override>(Lang:lang, Text:arr_char)<computes>:arr_char=
# 		case(Lang):
# 			lang.Pol => case(Text.Arr):
# 				 "You've completed the game in"=>"Ukończyłeś grę w:"
# 				 "Make a screenshot for the leaderboard! Do you want to reset time, shop and get +10% gain bonus?"=>"Zrób zrzut ekranu do tablicy wyników! Czy chcesz zresetować czas, sprzedać sklep i dostać +10% mnożnika bonusowego?"
# 				_ => ""
# 			_ => ""

localization<public> := class(auto_creative_device, i_init, i_localization):
	var ButtonsDevices<public>:[]localization_buttons_devic = array{}

	var PlayerToLang :[agent]lang= map{}
	var LocalizationMap<public>:[lang][string]string = map{}

	var LocalizationMapArr<public>:[lang][string]arr_char = map{}

	LanguageChangedEvent<public>:event(agent) = event(agent){}

	Init<override>(Container:vcontainer):void=
		set ButtonsDevices = GetDevices(localization_buttons_devic)
		Map := Container.ResolveErr(i_localization_map)
		set LocalizationMap = Map.GetLocalizationMap()

		for(Lang->Mapp:LocalizationMap):
			var LangMap:[string]arr_char = map{}
			for(Key->Val:Mapp):
				if. set LangMap[Key] = arr_char_c(Val)
			if. set LocalizationMapArr[Lang] = LangMap

		for(D:ButtonsDevices):
			spawn. RunButtons(D)

		Self

	RunButtons(D:localization_buttons_devic)<suspends>:void=
		sync:
			loop:
				Agent := D.ButtonEng.InteractedWithEvent.Await()
				SetLanguage(Agent, lang.Eng)
			loop:
				Agent := D.ButtonPol.InteractedWithEvent.Await()
				SetLanguage(Agent, lang.Pol)

	GetLanguageChangedEvent<override>():event(agent)=
		LanguageChangedEvent
	GetLanguage<override>(Agent:agent)<transacts>:lang=
		PlayerToLang[Agent] or lang.Eng

	SetLanguage<override>(Agent:agent, Lang:lang):void=
		if. set PlayerToLang[Agent] = Lang
		LanguageChangedEvent.Signal(Agent)


	G<override>(Agent:agent, Text:string):string=
		if(Lang := PlayerToLang[Agent]):
			if(Lang = lang.Eng):
				return Text
			else if(Translated := LocalizationMap[Lang][Text]):
				return Translated
			else:
				# Print("missing localization for: {ToString(Lang)} {Text}")
		Text

	GG<override>(Agent:agent, Text:string)<decides><transacts>:string=
		Lang := PlayerToLang[Agent]
		Lang <> lang.Eng
		if(Translated := LocalizationMap[Lang][Text]):
			Translated
		else:
			# Print("missing localization for: {Lang} {Text}")
			Fail[]
			Err()

	G<override>(Agent:agent, Text:arr_char):arr_char=
		if(Lang := PlayerToLang[Agent]):
			if(Lang = lang.Eng):
				return Text
			else if(Translated := LocalizationMapArr[Lang][Text.Arr]):
				return Translated
			else:
				# Print("missing localization for: {Lang} {Text}")
		Text

	GMsg<override>(Agent:agent, Text:arr_char):message=
		if(Lang := PlayerToLang[Agent]):
			if(Lang = lang.Eng):
				return ToMsg(Text)
			else if(Translated := LocalizationMapArr[Lang][Text.Arr]):
				return ToMsg(Translated)
			else:
				# Print("missing localization for: {Lang} {Text}")
		ToMsg(Text)
	GMsg<override>(Agent:agent, Text:string):message=
		if(Lang := PlayerToLang[Agent]):
			if(Lang = lang.Eng):
				return ToMsg(Text)
			else if(Translated := LocalizationMap[Lang][Text]):
				return ToMsg(Translated)
			else:
				# Print("missing localization for: {Lang} {Text}")
		ToMsg(Text)

	GG<override>(Agent:agent, Text:arr_char)<decides><transacts>:arr_char=
		Lang := PlayerToLang[Agent]
		Lang <> lang.Eng
		if(Translated := LocalizationMapArr[Lang][Text.Arr]):
			Translated
		else:
			# Print("missing localization for: {Lang} {Text.Arr}")
			Fail[]
			Err()

	IsEng<override>(Agent:agent)<decides><transacts>:void=
		if(Lang := PlayerToLang[Agent]):
			Lang = lang.Eng

	
