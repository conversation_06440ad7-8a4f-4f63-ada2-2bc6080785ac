using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }
<#
map_VAR1_VAR2 := class:
	var DataMap:[VAR1]VAR2 = map{}
	var MDataSetEvent:?event(tuple(VAR1, VAR2)) = false

	Get(Key:VAR1)<decides><transacts>:VAR2=
		DataMap[Key]

	GetErr(Key:VAR1)<decides><transacts>:VAR2=
		if(Data := DataMap[Key]):
			Data
		else:
			FailError[]
			Err()

	GetMap()<transacts>:[VAR1]VAR2=
		DataMap

	GetOrAwait<public>(Key:VAR1)<suspends>:VAR2=
		if(Data := DataMap[Key]):
			Data
		else:
			if(DataSetEvent := MDataSetEvent?):
				var Return:?VAR2 = false
				loop:
					Data := DataSetEvent.Await()
					if(Data(0) = Key):
						set Return = option. Data(1)
						break
				Return.G()
				# DataSetEvent.AwaitForData(Key)
			else:
				Ev := event(tuple(VAR1, VAR2)){}
				set MDataSetEvent = option. Ev
				var Return:?VAR2 = false
				# Ev.AwaitForData(Key)
				loop:
					Data := Ev.Await()
					if(Data(0) = Key):
						set Return = option. Data(1)
						break
				Return.G()
			

	Set(Key:VAR1, Data:VAR2):void=
		if. set DataMap[Key] = Data
		if(DataSetEvent := MDataSetEvent?):
			DataSetEvent.Signal((Key, Data))

	Remove(Key:VAR1)<transacts>:void=
		set DataMap = DataMap.WithRemoved(Key)
#>