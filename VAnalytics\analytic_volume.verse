using { /<EMAIL>/Pekao_Bank/VGiga }
using { /Fortnite.com/Devices }
using { /UnrealEngine.com/Temporary/Diagnostics }
using { /Verse.org/Simulation }

analytic_volume := class(auto_creative_device, i_on_rebirth):
	@editable Volume:volume_device = volume_device{}
	@editable MEnterAnalytics:?analytics_device = false
	@editable MExitAnalytics:?analytics_device = false
	@editable MInsideAnalytics:?analytics_device = false
	@editable MEnterFirstTimeAnalytics:?analytics_device = false

	var EnteredPlayers:[agent]logic = map{}

	OnBegin<override>()<suspends>:void=
		race:
			loop:
				Agent := Volume.AgentEntersEvent.Await()
				if (EnterAnalytics := MEnterAnalytics?):
					EnterAnalytics.Submit(Agent)
				if (EnterFirstTimeAnalytics := MEnterFirstTimeAnalytics?):
					if (not EnteredPlayers[Agent]?):
						EnterFirstTimeAnalytics.Submit(Agent)
						if. set EnteredPlayers[Agent] = true
				if (InsideAnalytics := MInsideAnalytics?):
					spawn { TrackAgentInside(Agent, InsideAnalytics) }
			loop:
				Agent := Volume.AgentExitsEvent.Await()
				if (ExitAnalytics := MExitAnalytics?):
					ExitAnalytics.Submit(Agent)

	TrackAgentInside(Agent:agent, InsideAnalytics:analytics_device)<suspends>:void=
		race:
			block:
				Volume.AgentExitsEvent.AwaitFor(Agent)
			block:
				loop:
					Sleep(60.0)
					InsideAnalytics.Submit(Agent)

	OnRebirth<override>(Agent:agent):void=
		set EnteredPlayers = EnteredPlayers.WithRemoved(Agent)





