using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }

(Transform:transform).RotateYawTowards<public>(Target:vector3)<decides><transacts>:transform={
	Dir := (-Transform.Translation + Target).MakeUnitVector[]
	Yaww := RadiansToDegrees(ArcTan(Dir.Y, Dir.X))
	# Pitchh := RadiansToDegrees(ArcTan(Dir.Z, 
	#     Sqrt((Dir.X * Dir.X) + (Dir.Y * Dir.Y))))
	Rotation := MakeRotationFromYawPitchRollDegrees(Yaww, 0.0,0.0)
	transform{
		Translation := Transform.Translation
		Rotation := Rotation
		Scale := Transform.Scale
	}
}

(Transform:transform).RotateYawPitchTowards<public>(Target:vector3)<decides><transacts>:transform={
	Dir := (-Transform.Translation + Target).MakeUnitVector[]
	Yaww := RadiansToDegrees(ArcTan(Dir.Y, Dir.X))
	Pitchh := RadiansToDegrees(ArcTan(Dir.Z, 
		Sqrt((Dir.X * Dir.X) + (Dir.Y * Dir.Y))))
	Rotation := MakeRotationFromYawPitchRollDegrees(Yaww, Pitchh,0.0)
	transform{
		Translation := Transform.Translation
		Rotation := Rotation
		Scale := Transform.Scale
	}
}

TransformOne<public>()<computes>:transform={
	transform{Translation := vector3{X := 1.0, Y := 1.0, Z := 1.0}, Rotation := IdentityRotation(), Scale := Vector3One}
}

(Tr:transform).RotateBy<public>(Rotation:rotation)<transacts>:transform=
	var T:transform = Tr
	set T.Translation = Rotation.RotateVector(T.Translation)
	set T.Rotation = T.Rotation.RotateBy(Rotation)
	return T

(Tr:transform).Yaw<public>(ValueDegrees:float)<transacts>:transform=
	Tr.RotateBy(MakeRotationFromYawPitchRollDegrees(ValueDegrees,0.0,0.0))

(Tr:transform).Yaw<public>(ValueDegrees:int)<transacts>:transform=
	Tr.RotateBy(MakeRotationFromYawPitchRollDegrees(ValueDegrees * 1.0,0.0,0.0))

(Tr:transform).YawRotation<public>(ValueDegrees:int)<transacts>:transform=
	transform{Translation := Tr.Translation, Rotation := Tr.Rotation.Yaw(ValueDegrees), Scale := Tr.Scale}

(Tr:transform).YawRotation<public>(ValueDegrees:float)<transacts>:transform=
	transform{Translation := Tr.Translation, Rotation := Tr.Rotation.Yaw(ValueDegrees), Scale := Tr.Scale}

(Transform:transform).WithTranslation<public>(Value:vector3)<transacts>:transform=
	var Tr:transform = Transform
	set Tr.Translation = Value
	Tr

(Tr:transform).WithTranslationZ<public>(Z:float)<transacts>:transform=
	transform:
		Translation := vector3:
			X := Tr.Translation.X
			Y := Tr.Translation.Y
			Z := Z
		Rotation := Tr.Rotation
		Scale := Tr.Scale

(Tr:transform).AddTranslationZ<public>(Z:float)<transacts>:transform=
	transform:
		Translation := vector3:
			X := Tr.Translation.X
			Y := Tr.Translation.Y
			Z := Tr.Translation.Z + Z
		Rotation := Tr.Rotation
		Scale := Tr.Scale

(Tr:transform).WithTranslationZAndScale<public>(Z:float, Scale:float)<transacts>:transform=
	transform:
		Translation := vector3:
			X := Tr.Translation.X
			Y := Tr.Translation.Y
			Z := Z
		Rotation := Tr.Rotation
		Scale := Tr.Scale * Scale

(Transform:transform).WithRotation<public>(Value:rotation)<transacts>:transform=
	var Tr:transform = Transform
	set Tr.Rotation = Value
	Tr

(Transform:transform).WithScale<public>(Value:vector3)<transacts>:transform=
	var Tr:transform = Transform
	set Tr.Scale = Value
	Tr


(Transform:transform).AddTranslation<public>(Value:vector3)<transacts>:transform=
	var Tr:transform = Transform
	set Tr.Translation = Tr.Translation + Value
	Tr


(Transform:transform).AddRotation<public>(Value:rotation)<transacts>:transform=
	var Tr:transform = Transform
	set Tr.Rotation = Tr.Rotation.RotateBy(Value)
	Tr

(Transform:transform).AddScale<public>(Value:vector3)<transacts>:transform=
	var Tr:transform = Transform
	set Tr.Scale = Tr.Scale + Value
	Tr


(Tr:transform).SnapToGrid<public>(Step:float)<transacts>:transform=
	Pos := Tr.Translation

	if(X := Round[Pos.X / Step] * Step
		Y := Round[Pos.Y / Step] * Step
		Z := Round[Pos.Z / Step] * Step
	):
		transform:
			Translation := Vector3(X, Y, Z)
			Rotation := Tr.Rotation
			Scale := Tr.Scale
	else:
		LError()
		Tr

(Pos:vector3).SnapToGrid<public>(Step:float)<transacts>:vector3=
	if(X := Round[Pos.X / Step] * Step
		Y := Round[Pos.Y / Step] * Step
		Z := Round[Pos.Z / Step] * Step
	):
		Vector3(X, Y, Z)
	else:
		LError()
		Pos

(Tr:transform).Snap96<public>()<transacts>:transform=
	Tr.SnapToGrid(96.0)

(Tr:vector3).Snap96<public>()<transacts>:vector3=
	Tr.SnapToGrid(96.0)

(X:float).SnapToGrid<public>(Step:float)<transacts>:float=
	if(X2 := Round[X / Step] * Step):
		X2
	else:
		LError()
		X



