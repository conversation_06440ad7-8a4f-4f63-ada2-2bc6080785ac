using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }

# (Task:task(t)).WithTimeout<public>(Time:float where t:type)<suspends>:?t={
#     race{
#         Sleep(Time)
#         block{
#             Ret := Task.Await()
#             return option. Ret
#         }
#     }
#     false
# }

(Task:type{_(Input:t)<suspends>:t2}).WithTimeout<public>(Input2:t, Time:float where t:type, t2:type)<suspends>:?t2={
    race{
        Sleep(Time)
        block{
            Ret := Task(Input2)
            return option. Ret
        }
    }
    false
}


# (Awaitables:[]type{_(:i)<suspends>:r} where i:type, r:type).RaceMethodsData<public>(Input:i)<suspends>:?r={
#     # RaceAwaitableMethodsData(Awaitables, Input, 0)
# 	false
# }

# (Awaitables:[]type{_()<suspends>:r} where r:type).RaceMethods<public>()<suspends>:?r={
#     RaceMethods(Awaitables, 0)
# }
# (Awaitables:[]type{_()<suspends>:int}).RaceMethods<public>()<suspends>:?int={
#     RaceMethods(Awaitables, 0)
# }

# RaceMethods<internal>(Awaitables:[]type{_()<suspends>:int}, Index:int)<suspends>:?int={
#     if(Awaitable := Awaitables[Index]
# 	){
#         race{
#             RaceMethods(Awaitables, Index + 1)
#             block{
#                 Result:= Awaitable()
#                 option. Result
# 			}
# 		}
# 	}else{
#         if (Awaitables.Length > 0):
#             Sleep(Inf)
#         false
# 	}
# }

# RaceAwaitableMethodsData<internal>(Awaitables:[]type{_(:i)<suspends>:r}, Input:i, Index:int where i:type, r:type)<suspends>:?r={
#     if(Awaitable := Awaitables[Index]
# 	){
#         race{
#             RaceAwaitableMethodsData(Awaitables, Input, Index + 1)
#             block{
#                 Result:r = Awaitable(Input)
#                 option. Result
# 			}
# 		}
# 	}else{
#         if (Awaitables.Length > 0):
#             Sleep(Inf)
#         false
# 	}
# }

# (Awaitables:[]awaitable(t) where t:type).Race<public>()<suspends>:?t=
#     RaceAwaitables(Awaitables, 0)

# RaceAwaitables<internal>(Awaitables:[]awaitable(t), Index:int where t:type)<suspends>:?t=
#     if (Awaitable := Awaitables[Index]):
#         race:
#             RaceAwaitables(Awaitables, Index + 1)
#             block:
#                 Result:t = Awaitable.Await()
#                 option{Result}
#     else:
#         if (Awaitables.Length > 0):
#             Sleep(Inf)
#         false

# (Awaitables:[]awaitable(t) where t:type).Sync<public>()<suspends>:[]t=
#     SyncAwaitables(Awaitables, 0)

# SyncAwaitables<internal>(Awaitables:[]awaitable(t), Index:int where t:type)<suspends>:[]t=
#     if (Awaitable := Awaitables[Index]):
#         Result:tuple(t, []t) = sync:
#             Awaitable.Await()
#             SyncAwaitables(Awaitables, Index + 1)
#         array{Result(0)} + Result(1)
#     else:
#         array{}