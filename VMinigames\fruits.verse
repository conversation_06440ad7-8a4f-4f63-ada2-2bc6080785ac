using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
using. VResourcesSystem
using { /UnrealEngine.com/Assets }
using. VAudio
fruits := class(auto_creative_device, i_init_async):

    @editable RespawnMinRandomTime:float = 10.0
    @editable RespawnMaxRandomTime:float = 30.0

    var Resources :?resources_manager= false
    var Audio:?audio = false
    var Rewards :?gm_balance_rewards= false
    
    InitAsync<override>(Container:vcontainer)<suspends>:void=
        set Rewards = Container.ResolveOp[gm_balance_rewards] or Err()		
        set Resources = Container.ResolveOp[resources_manager] or Err()
        set Audio = Container.ResolveOp[audio] or Err()
        Fruits := tag_fruit{}.GetAll(Self)
        for(Fruit:Fruits):
            spawn. RunFruit(Fruit)
            

    RunFruit(Fruit:button_device)<suspends>:void=
        InitScale := Fruit.GetTransform().Scale
        loop:
            Agent := Fruit.InteractedWithEvent.Await()
            Resources.G().GiveGoldWithMultiplier(Agent, Rewards.G().FruitTimeSec, Rewards.G().FruitRatio, ?ShowNotification := true)
            Resources.G().GivePoints(Agent, 1)
            CancelFx := SpawnParticleSystem(Modele.Piggy.Blow_up_Piggy, Fruit.GetTransform().Translation)
            if. Fruit.TeleportTo[Fruit.GetTransform().WithScale(Vector3One * 0.0001)]
            Audio.G().GotCash.Play(Agent)
            Fruit.Disable()
            Rand := GetRandomFloat(RespawnMinRandomTime, RespawnMaxRandomTime)
            Sleep(Rand)
            if. Fruit.TeleportTo[Fruit.GetTransform().WithScale(InitScale)]
            CancelFx.Cancel()
            Fruit.Enable()
        