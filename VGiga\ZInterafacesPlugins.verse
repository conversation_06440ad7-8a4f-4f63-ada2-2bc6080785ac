using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 

i_localization<public> := interface(class_interface):
	G<public>(Agent:agent, Text:string):string
	GG<public>(Agent:agent, Text:string)<decides><transacts>:string
	G<public>(Agent:agent, Text:arr_char):arr_char
	GG<public>(Agent:agent, Text:arr_char)<decides><transacts>:arr_char
	GetLanguageChangedEvent<public>():event(agent)
	SetLanguage<public>(Agent:agent, Lang:lang):void
	GetLanguage<public>(Agent:agent)<transacts>:lang
	IsEng<public>(Agent:agent)<decides><transacts>:void
	GMsg<public>(Agent:agent, Text:arr_char):message
	GMsg<public>(Agent:agent, Text:string):message

empty_i_localization<public> := class(i_localization):
	LanguageChangedEvent<public>:event(agent) = event(agent){}
	G<override>(Agent:agent, Text:string):string=
		Text
	GG<override>(Agent:agent, Text:string)<decides><transacts>:string=
		Text
	G<override>(Agent:agent, Text:arr_char):arr_char=
		Text
	GG<override>(Agent:agent, Text:arr_char)<decides><transacts>:arr_char=
		Text
	SetLanguage<override>(Agent:agent, TeLangxt:lang):void={}

	GetLanguageChangedEvent<override>():event(agent)=
		LanguageChangedEvent
	GetLanguage<override>(Agent:agent)<transacts>:lang=
		lang.Eng
	IsEng<override>(Agent:agent)<decides><transacts>:void={}
	GMsg<override>(Agent:agent, Text:arr_char):message=ToMsg(Text)
	GMsg<override>(Agent:agent, Text:string):message=ToMsg(Text)
#gen
#plugin_t1
#i_localization
#id-2b67e9b2-4e7c-49f4-8264-c8234f75abd2
(Container:vcontainer).Resolve_i_localization<public>()<transacts>:i_localization=
	if(Resolved := Container.ResolveNoPrint[i_localization]):
		Resolved
	else:
		LPrint("i_localization missing, using empty")
		empty_i_localization{}	
#id-2b67e9b2-4e7c-49f4-8264-c8234f75abd2


lang<public> := enum:
	Eng
	Pol

ToString<public>(Lang:lang)<computes><reads>:string=
	if(Lang = lang.Pol):
		"pol"
	else:
		"eng"