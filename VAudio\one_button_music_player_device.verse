
using { /Fortnite.com/Devices }
using { /Verse.org/Simulation }
using { /UnrealEngine.com/Temporary/Diagnostics }
using { /Verse.org/Simulation/Tags }
AudioButton := class(tag){}

# See https://dev.epicgames.com/documentation/en-us/uefn/create-your-own-device-in-verse for how to create a verse device.

# A Verse-authored creative device that can be placed in a level
one_button_music_player_device := class(creative_device):
    var ChangeMusicButtons : []button_device = array{}
    @editable Radio1 : audio_player_device = audio_player_device{}
    @editable Radio2 : audio_player_device = audio_player_device{}
    @editable Radio3 : audio_player_device = audio_player_device{}
    var CurrentRadio : int = 0


    # Runs when the device is started in a running game
    OnBegin<override>()<suspends>:void=
        Radio1.Play()
        Radio2.Play()
        Radio3.Play()
        AudioButtonsArray := FindCreativeObjectsWithTag(AudioButton{})
                for (Obj : AudioButtonsArray):
                    if (Button := button_device[Obj]):
                        set ChangeMusicButtons = ChangeMusicButtons + array{Button}
                        Button.InteractedWithEvent.Subscribe(ChageRadioState)
    
    ChageRadioState(Agent:agent):void= 
        set CurrentRadio += 1 
        if (CurrentRadio = 6):
            set CurrentRadio = 0 
        if (CurrentRadio=0 or CurrentRadio=2 or CurrentRadio=4):
            UnregisterFromEveryRadio(Agent)
        if (CurrentRadio=1):
            Radio1.Register(Agent)
            for (Button : ChangeMusicButtons):
                Button.SetInteractionText(StringToMessage("Turn Off Radio"))
        if (CurrentRadio=3):
            Radio2.Register(Agent)
            for (Button : ChangeMusicButtons):
                Button.SetInteractionText(StringToMessage("Turn Off Radio"))
        if (CurrentRadio=5):
            Radio3.Register(Agent)
            for (Button : ChangeMusicButtons):
                Button.SetInteractionText(StringToMessage("Turn Off Radio"))

    StringToMessage<localizes>(value:string)<computes> : message = "{value}"

    UnregisterFromEveryRadio(Agent:agent):void=
        Radio1.Unregister(Agent)
        Radio2.Unregister(Agent)
        Radio3.Unregister(Agent)
        for (Button : ChangeMusicButtons):
            Button.SetInteractionText(StringToMessage("Turn On Radio"))


        