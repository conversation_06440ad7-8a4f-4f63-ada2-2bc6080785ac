using { /Fortnite.com/Devices }
using { /Verse.org/Simulation }
using { /UnrealEngine.com/Temporary/Diagnostics }
using { /Verse.org/Simulation/Tags }
using { /Verse.org/Random }
using { /Verse.org/Assets }
using { VResourcesSystem}

StickerAccolade := class(tag){}

presents_to_place := class<concrete>():
    @editable PresentObject : creative_prop = creative_prop{}
    @editable Button : button_device = button_device{}
    var Resources_InGift : resources_manager = resources_manager{}
    var Accolade : accolades_device = accolades_device{}

    var IsStickerUsed : logic = false
    var TimeToReset : float = 300.0
    var Index : int = -1
    var prezentOtwartyMesh : mesh = Meshes.prezentotwarty
    var prezentZamknietyMesh : mesh = Meshes.prezentzamkniety

    Init():void=
        Button.InteractedWithEvent.Subscribe(OnInteract)
        Print("Init Gift")

    OnInteract(Agent : agent):void=
        Print("Player Interacted")
        if(IsStickerUsed = false):
            Print("Player Open Present")
            PresentObject.SetMesh(prezentOtwartyMesh)
            Resources_InGift.GiveGold(Agent, 1000)
            Accolade.Award(Agent)
            Button.Disable()
            set IsStickerUsed = true
            spawn{Cooldown()}
    
    Cooldown()<suspends>:void= 
        Sleep(TimeToReset)
        Button.Enable()
        set IsStickerUsed = false
        PresentObject.SetMesh(prezentZamknietyMesh)
    




        
# See https://dev.epicgames.com/documentation/en-us/uefn/create-your-own-device-in-verse for how to create a verse device.

# A Verse-authored creative device that can be placed in a level
placesticker_system_device := class(creative_device):
    @editable Stickers : []presents_to_place = array{}
    @editable Nagroda : accolades_device = accolades_device{}
    @editable Resources : resources_manager = resources_manager{}


    # Runs when the device is started in a running game
    OnBegin<override>()<suspends>:void=  
        for(Item : Stickers):
            Item.Init()
            set Item.Accolade = Nagroda
            set Item.Resources_InGift = Resources
        
