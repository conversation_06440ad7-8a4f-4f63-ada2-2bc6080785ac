using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }

p_data := class:
    QuestsUi:quests_list_generated
	Agent<public>:agent
	AddQuest(Data:quest_data):active_quest_data=
		ActiveQuest := active_quest_data:
			Agent := Agent
			Data := Data

		if. set ActiveQuests[Data.Id] = ActiveQuest
		ActiveQuest

	RemoveQuestAndAddAsCompleted(Quest:active_quest_data):void=
		set ActiveQuests = ActiveQuests.WithRemoved(Quest.Data.Id)
		if. set CompletedQuests[Quest.Data.Id] = true

	FindActiveQuest(Id:string)<decides><transacts>:active_quest_data=
		ActiveQuests[Id]

	var ActiveQuests:[string]active_quest_data = map{}
	var CompletedQuests:[string]logic = map{}