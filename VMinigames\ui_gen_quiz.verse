using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/UI
using. /Fortnite.com/UI
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
quiz_ui_generated := class:
	Image_68:texture_block
	Overlay_400:overlay
	Image_214ColorBlock:color_block
	QuizUi:canvas

make_quiz_ui_generated():quiz_ui_generated=

	Image_68 :texture_block= texture_block:
		DefaultImage := VMinigames.T_QuizBg
		DefaultDesiredSize := vector2:
			X := 2048.000000
			Y := 1024.000000
	Overlay_400 :overlay= overlay:
		Slots := array:
			overlay_slot:
				HorizontalAlignment := horizontal_alignment.Fill
				VerticalAlignment := vertical_alignment.Fill
				Widget := Image_68
	Image_214ColorBlock :color_block= color_block:
		DefaultColor := color:
			R := 0.000000
			G := 0.000000
			B := 0.000000
		DefaultOpacity := 0.534000
		DefaultDesiredSize := vector2:
			X := 32.000000
			Y := 32.000000
	QuizUi :canvas= canvas:
		Slots := array:
			canvas_slot:
				Alignment := vector2:
					X := 0.500000
					Y := 0.500000
				Offsets := margin:
					Right := 0.000000
					Bottom := 0.000000
				Anchors := anchors:
					Maximum := vector2:
						X := 1.000000
						Y := 1.000000
				SizeToContent := false
				Widget := Image_214ColorBlock
			canvas_slot:
				Alignment := vector2:
					X := 0.500000
					Y := 0.500000
				Offsets := margin:
					Right := 2048.000000
					Bottom := 1024.000000
				Anchors := anchors:
					Minimum := vector2:
						X := 0.500000
						Y := 0.500000
					Maximum := vector2:
						X := 0.500000
						Y := 0.500000
				ZOrder := 1
				SizeToContent := false
				Widget := Overlay_400


	quiz_ui_generated:
		Image_68 := Image_68
		Overlay_400 := Overlay_400
		Image_214ColorBlock := Image_214ColorBlock
		QuizUi := QuizUi
