using { /Verse.org/Random }
using { /Fortnite.com/Game }


# (Array:string).FindBetweenStr(StartId:int, EndId:int, Char:char):?int=
# 	var I:int = StartId
# 	loop:
# 		if(Element := Array[I]
# 			Element = Char
# 		):
# 			return option. I
            
# 		set I += 1
# 		if(I >= Array.Length):
# 			break

# 	return false

(Array:[]t where t:subtype(comparable)).ArrayContains<public>(Val:t)<decides><transacts>:void=
    Array.Find[Val]
    
(Array:[]t where t:subtype(comparable)).FindBetween(StartId:int, EndId:int, Char:t)<decides><transacts>:int=
    var I:int = StartId
    loop:
        if(Element := Array[I]
            Element = Char
        ):
            break
            
        set I += 1
        if(I >= EndId):
            break

    if(I >= EndId):
        Fail[]
        Err()
    else:
        I
(Array:[]t where t:subtype(comparable)).FindBetweenLast(StartId:int, EndId:int, Char:t)<decides><transacts>:int=
    var I:int = EndId
    loop:
        if(Element := Array[I]
            Element = Char
        ):
            break
            
        set I -= 1
        if(I < 0):
            break

    if(I < 0):
        Fail[]
        Err()
    else:
        I
# (Input:[]t where t:type).MaxBy<public>(Comparer: type{_(:t, :t)<transacts> : int})<transacts><decides> : t = 
# 	var Biggest :t= Input[0]
# 	for(X := 1..Input.Length
# 		Next := Input[X]
# 		Comparer(Biggest, Next) < 0
# 	):
# 		set Biggest = Next
# 	Biggest

# (Input:[]t where t:type).MinBy<public>(Comparer: type{_(:t, :t)<transacts> : int})<transacts><decides> : t = 
# 	var Smallest :t= Input[0]
# 	for(X := 1..Input.Length
# 		Next := Input[X]
# 		Comparer(Smallest, Next) > 0
# 	):
# 		set Smallest = Next

# 	Smallest

# (Items:[]t).Sort<public>(IsAscending:logic, Comparer:type{_(:t, :t)<transacts>:int} where t:type)<transacts>:[]t = 
#     if (Items.Length > 1, Pivot := Items[Floor(Items.Length/2)]):
#         Left := for(Item : Items, Comparer(Item, Pivot) < 0) do Item 
#         Middle := for(Item : Items, Comparer(Item, Pivot) = 0) do Item
#         Right := for(Item : Items, Comparer(Item, Pivot) > 0) do Item
#         if(IsAscending?):
#             Left.Sort(IsAscending, Comparer) + Middle + Right.Sort(IsAscending, Comparer)
#         else: 
#             Right.Sort(IsAscending, Comparer) + Middle + Left.Sort(IsAscending, Comparer)
#     else:    
#         Items 
 
# CompareInts(A : int, B : int)<transacts>: int = 
#     if(A < B) then -1
#     else if(A > B) then 1
#     else 0
<#> Array sort
    UUsage:::::
        SortedArray := MyIntArray.Sort(IntComparer)
        SortedArray := MyFloatArray.Sort(FloatComparer)
        SortedArray := MyCustomArray.Sort(CustomComparer) - requires custom Comparer
        SortedArray := MyFloatArray.SortDescending(FloatComparer)
        etc
    Based on code by MattRix https://dev.epicgames.com/community/snippets/l0X/fortnite-array-sort-function
# (Items:[]t).SortInternal(Ascending:logic, Comparer:type{_(:t, :t)<transacts>:int} where t:type)<transacts>:[]t = 
#     if (Items.Length > 1, Pivot := Items[Floor(Items.Length/2)]):
#         Left := for(Item : Items, Comparer(Item, Pivot) = -1):
#             Item 
#         Middle := for(Item : Items, Comparer(Item, Pivot) = 0):
#             Item
#         Right := for(Item : Items, Comparer(Item, Pivot) = 1):
#             Item
#         if(Ascending?):
#             Left.SortInternal(Ascending, Comparer) + Middle + Right.SortInternal(Ascending, Comparer)
#         else: 
#             Right.SortInternal(Ascending, Comparer) + Middle + Left.SortInternal(Ascending, Comparer)
#     else:    
#         Items 


# SafeSort(Its:[]t):[]t = {
#     var Items :[]t= for(Its:Element). Element
#     var Sorted :[]t= array{}
#     N := Items.Length
#     Biggest:t
#     for(i := 0..n,j := 0..n-i-1) {
#         if(Comparer(Items[j], Items[j+1]) = 1
#             Temp:t = Items[j]
#             set Items[j] = Items[j+1]
#             set Items[j+1] = Temp){
#         }
#     }
#     for(i := 0..n-1,j := 0..n-i-1) {
#         if(Comparer(Items[j], Items[j+1]) = 1
#             Temp:t = Items[j]
#             set Items[j] = Items[j+1]
#             set Items[j+1] = Temp){
#         }
#     }
#     return Items
# }
# CRASHES PROJECTS!
(Its:[]t).SortInternalAsc(Comparer:type{_(:t, :t)<transacts>:int} where t:type)<transacts>:[]t=
    var Items :[]t= Its
    n := Items.Length - 1
    for(i := 0..n, j := 0..n-i):
        if:
            Comparer(Items[j], Items[j+1]) = 1
            Temp:t = Items[j]
            Val := Items[j+1]
            set Items[j] = Val
            set Items[j+1] = Temp
            
    return Items

(Its:[]t).SortInternalDesc(Comparer:type{_(:t, :t)<transacts>:int} where t:type)<transacts>:[]t=
    var Items :[]t= Its
    n := Items.Length - 1
    for(i := 0..n, j := 0..n-i):
        if:
            Comparer(Items[j], Items[j+1]) = -1
            Temp:t = Items[j]
            Val := Items[j+1]
            set Items[j] = Val
            set Items[j+1] = Temp
            
    return Items

(Items:[]t).SortAsc<public>(Comparer:type{_(:t, :t)<transacts>:int} where t:type)<transacts>:[]t = 
    Items.SortInternalAsc(Comparer)

(Items:[]t).SortDesc<public>(Comparer:type{_(:t, :t)<transacts>:int} where t:type)<transacts>:[]t = 
    Items.SortInternalDesc(Comparer)

(Items:[]t  where t:subtype(positional)).SortOnX<public>()<transacts>:[]t={
    Items.SortInternalDesc(PositionalXComparer)
}
PositionalXComparer<public>(APos:positional, BPos:positional)<transacts>:int={
    A := APos.GetTransform().Translation.X
    B := BPos.GetTransform().Translation.X
    if(A < B){
        -1
    }
    else if(A > B){
        1
    }else{
        0
    }
}
# SortTeleportDevices(arr2:[]teleport_manager):[]teleport_manager = {
# }
    # bubbleSort(arr2:[]teleport_manager):[]teleport_manager = {
    #     var arr: []teleport_manager = arr2
    #     n := arr.Length
    #     for(i := 0..n-1) {
    #         for(j := 0..n-i-1) {
    #             if(arr[j].GetTransform().Translation.X > arr[j+1].GetTransform().Translation.X
    #                 var tempArr:teleport_manager = arr[j]
    #                 set arr[j] = arr[j+1]
    #                 set arr[j+1] = tempArr) {
    #                 # arr[j], set arr[j+1] = arr[j+1], arr[j]
    #             }
    #         }
    #     }
    #     return arr
    # }

IntComparer<public>(A:int, B:int)<computes>:int={
    if(A < B){
        -1
    }
    else if(A > B){
        1
    }else{
        0
    }
}

FloatComparer<public>(A:float, B:float)<computes>:int={
    if(A < B):
        return -1
    else if(A > B):
        return 1
    0
}

# crashes uefn
(Arr:[]t where t:type).FirstData<public>(Check:type{_(:t,:t2)<decides><transacts>:void}, Data:t2 where t2:type)<decides><transacts>:t=
    var Ret:?t = false
    N := Arr.Length
    var X :int= 0 
    loop:
        if(Item := Arr[X], Check[Item, Data]):
            set Ret = option. Item
            break

        set X += 1
        if(X >= N):
            break
            
    if(Item := Ret?):
        Item
    else:
        Fail[]
        Err()

(Arr:[]t where t:type).FirstDataId<public>(Check:type{_(:t,:t2)<decides><transacts>:void}, Data:t2 where t2:type)<decides><transacts>:int=
    var Ret:?int = false
    N := Arr.Length
    var X :int= 0 
    loop:
        if(Item := Arr[X], Check[Item, Data]):
            set Ret = option. X
            break

        set X += 1
        if(X >= N):
            break
            
    if(Item := Ret?):
        Item
    else:
        Fail[]
        Err()
        
# (Arr:[]t where t:type).FirstDataError<public>(Check:type{_(:t,:t2)<decides><transacts>:void}, Data:t2 where t2:type)<decides><transacts>:t={
# 	if(Item := Arr.FirstData[Check, Data]){
# 		Item
# 	}else{
# 		FailError[]
# 		Err()
# 	}
# }
# (Generator:generator(t) where t:type).FirstA()<decides><transacts>:t=
# 	(for(I:Generator):
# 		I
# 	)[0]

# (Arr:[]t where t:type).FirstA()<decides><transacts>:t=
# 	Arr[0]
(Arr:[]t where t:type).ArrayFirst<public>(Check:type{_(:t)<decides><transacts>:void})<decides><transacts>:t=
    var Ret:?t = false
    N := Arr.Length
    var X :int= 0 
    loop:
        if(Item := Arr[X], Check[Item]):
            set Ret = option. Item
            break
        
        set X += 1
        if(X >= N):
            break
        
    
    if(Item := Ret?):
        Item
    else:
        Fail[]
        Err()

(Arr:[]t where t:type).ArrayFirst1<public>(Check:type{_(:t, :t1)<decides><transacts>:void}, T1:t1 where t1:type)<decides><transacts>:t=
    var Ret:?t = false
    N := Arr.Length
    var X :int= 0 
    loop:
        if(Item := Arr[X], Check[Item, T1]):
            set Ret = option. Item
            break
        
        set X += 1
        if(X >= N):
            break
        
    
    if(Item := Ret?):
        Item
    else:
        Fail[]
        Err()

(Arr:[]t where t:type).ArrayFirstError<public>(Check:type{_(:t)<decides><transacts>:void})<decides><transacts>:t=
    if(Item := Arr.ArrayFirst[Check]):
        Item
    else:
        FailError[]
        Err()
        
# (Arr:[]t where t:type).For<public>(Method:type{_(:t):t2} where t2:type):[]t2=
# 	for(A:Arr):
# 		Method(A)

# (Arr:[]t where t:type).ForDecides<public>(Method:type{_(:t)<decides><transacts>:t2} where t2:type):[]t2=
# 	for(A:Arr
# 		B := Method[A]
# 	):
# 		B
        

# (Arr:[]t where t:type).Where<public>(Check:type{_(:t)<decides><transacts>:void})<decides><transacts>:[]t=
# 	for(Item:Arr, Check[Item]):
# 		Item


# (Input:[]t where t:type).ToMap(v:type, :tuple()=>v where v : subtype(unique))<computes>:[]t=
#     for (I := (Input.Length-1)..0, Item := Input[I]):
#         Item

<#> Array reverse
    Usage:
        ReversedArray := MyIntArray.Reverse()
(Input:[]t where t:type).Reverse<public>()<transacts>:[]t=
    var New:[]t = array{}
    for(El:Input):
        set New = array{El} + New
    New

(Array:[]t where t:type).GetRandom<public>()<decides><transacts>:t=
    if(Array.Length < 1):
        Fail[]
    
    Array[GetRandomInt(0, Array.Length-1)]

(Array:[]t where t:type).GetRandomId<public>()<decides><transacts>:int=
    if(Array.Length < 1):
        Fail[]
    
    GetRandomInt(0, Array.Length-1)

(Array:[]t where t:type).GetRandomExcluding<public>(ToExclude:int)<decides><transacts>:tuple(t, int)=
    if(Array.Length < 1):
        Fail[]

    Rand := GetRandomIntExcludingInt(0, Array.Length-1, ToExclude)
    (Array[Rand], Rand)

(Array:[]t where t:type).GetRandomExcluding2Arrays<public>(Array2:[]t, ToExclude:int)<decides><transacts>:tuple(t, int)=
    if(Array.Length < 1, Array2.Length < 1):
        Fail[]
    
    Maxx := Array.Length + Array2.Length
    Rand := GetRandomIntExcludingInt(0, Maxx-1, ToExclude)
    if(Rand < Array.Length):
        (Array[Rand], Rand)
    else:
        (Array2[Array.Length - Rand], Rand)
    

(Array:[]t where t:type).GetRandomNoError<public>()<decides><transacts>:t=
    Array.Length > 0
    Array[GetRandomInt(0, Array.Length-1)]

(Array:[]t where t:type).GetRandomIndex<public>()<transacts>:int=
    GetRandomInt(0, Array.Length-1)

RemoveKeyFromMap(ExampleMap:[t]t2, ElementToRemove:t where t:subtype(comparable), t2:type)<transacts>:[t]t2=
    var NewMap:[t]t2 = map{}
    for (Key -> Value : ExampleMap, Key <> ElementToRemove):
        set NewMap = ConcatenateMaps(NewMap, map{Key => Value})
    return NewMap

(Map:[t]t2 where t:subtype(comparable), t2:type).FindKeyIndex<public>(Key:t)<decides><transacts>:int=
    var Id:int = 0
    var IdFound:int = -1
    for (K -> Value : Map):
        if(K = Key):
            set IdFound = Id
        set Id += 1

    if(IdFound >= 0):
        IdFound
    else:
        Fail[]
        0

(Map:[t]t2 where t:subtype(comparable), t2:type).GetAtIndex<public>(IdToFind:int)<decides><transacts>:tuple(t,t2)=
    var Id:int = 0
    var Found:?tuple(t,t2) = false
    for (K -> Value : Map):
        if(Id = IdToFind):
            set Found = option. (K,Value)
        set Id += 1

    if(F := Found?):
        F
    else:
        Fail[]
        Err()