using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga
using. VResourcesSystem
	 
tokens := class(auto_creative_device, i_init):

	var Resources:?resources_manager = false

	Init<override>(Container:vcontainer):void={}
		# set Resources = Container.ResolveErrOp(resources_manager)
		# Collectibles := token_tag{}.GetAll(Self)

		# for(C:Collectibles):
		# 	spawn. OnCollected(C.CollectedEvent)

	OnCollected(CollectedEv:listenable(agent))<suspends>:void={}
		# loop:
		# 	Agent := CollectedEv.Await()
		# 	Resources.G().IncreaseTokens(Agent)
		
			
	