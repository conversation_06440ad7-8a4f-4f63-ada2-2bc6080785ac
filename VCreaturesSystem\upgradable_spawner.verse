using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
using. VMapBuilder
using. VPropSpawner
using. VResourcesSystem
using. VNotifications
using. VGoldGranter

upgradable_spawner_spawn_map_tag_vertical := class(tag_comparable):
	GetComparable<override>()<computes>:comparable=
		"upgradable_spawnersmtv"

upgradable_spawner_devic<public> := class(creative_device):
	@editable Spawners:[]creature_spawner_device = array{}
	# @editable UpgradableMapBuilders:[]spawn_map_devic = array{} 
	
	Init<public>(Container:player_events_manager_devic,
		B:upgradable_spawner_balance,
		MPropSpawnerForMapBuilder:?prop_spawner,
		Area:area_interface
	)<suspends>:upgradable_spawner=
		ResourcesManager := Container.ResolveErr(resources_manager)
		if(
			Notifs := Container.Resolve[notifications_system]
			# GrantFromKills := Container.Resolve[grant_from_kills]
		):
			Tagg := upgradable_spawner_spawn_map_tag_vertical{}
			UpgradableMapBuilders := GetDevicesInAreaWithTag(spawn_map_devic, Area, Tagg).Sortspawn_map_devic(false, Compare_spawn_map_devic_Z)

			SpawnersInitPos := for(S:Spawners):
				S.GetTransform().Translation

			UpgradableMapBuildersInst :[]spawn_map= if(PropSpawner := MPropSpawnerForMapBuilder?):
				for(MapBuilder:UpgradableMapBuilders):
					MapBuilder.Init(Container, PropSpawner, false)
			else:
				LPrint("Prop spawner missing for Upgradable Spawner. Possibly missing.")
				array{}
					
			Manager := upgradable_spawner:
				D := Self
				B := B
				SpawnersInitPos := SpawnersInitPos
				UpgradableMapBuilders := UpgradableMapBuildersInst
				ResourcesManager := ResourcesManager
				Notifs := Notifs
				# GrantFromKills := GrantFromKills
			.Init()
			Container.Register(Manager)
			Manager
		else:
			LError()
			Err()

#gen
#sort_t1
#spawn_map_devic
#id-72fa23fe-8f40-4d3e-8862-9cb1c7b3d7f0
(Items:[]spawn_map_devic).Sortspawn_map_devic<public>(IsAscending:logic, Comparer:type{_(:spawn_map_devic, :spawn_map_devic)<transacts>:int})<transacts>:[]spawn_map_devic = 
    if (Items.Length > 1, Pivot := Items[Floor(Items.Length/2)]):
        Left := for(Item : Items, Comparer(Item, Pivot) < 0) do Item 
        Middle := for(Item : Items, Comparer(Item, Pivot) = 0) do Item
        Right := for(Item : Items, Comparer(Item, Pivot) > 0) do Item
        if(IsAscending?):
            Left.Sortspawn_map_devic(IsAscending, Comparer) + Middle + Right.Sortspawn_map_devic(IsAscending, Comparer)
        else: 
            Right.Sortspawn_map_devic(IsAscending, Comparer) + Middle + Left.Sortspawn_map_devic(IsAscending, Comparer)
    else:    
        Items 
 
# CompareInts(A : int, B : int)<transacts>: int = 
#     if(A < B) then -1
#     else if(A > B) then 1
#     else 0
#id-72fa23fe-8f40-4d3e-8862-9cb1c7b3d7f0
Compare_spawn_map_devic_Z(AD : spawn_map_devic, BD : spawn_map_devic)<transacts>: int =
	A := AD.GetTransform().Translation.Z
	B := BD.GetTransform().Translation.Z
    if(A < B). -1
    else if(A > B). 1
    else 0
	
upgradable_spawner_balance<public> := class(class_interface):
	var KeepFirstLevelEnabled<public>:logic = true
	var GoldPerKillArr<public>:[]int = array{}
	
upgradable_spawner<public> := class<internal>(class_interface):
	UpgradableMapBuilders<public>:[]spawn_map
	var<private> CurLevel<public>:int = 0

	D:upgradable_spawner_devic
	B:upgradable_spawner_balance
	Notifs:notifications_system
	SpawnersInitPos:[]vector3 = array{}
	# var PlayerDataMap : map_agent_p_data = map_agent_p_data{}
	var<private> Owner:?agent = false
	ResourcesManager:resources_manager

	UpgradedEvent<public>:event(agent)= event(agent){}
	MapRespawnedEvent<public>:event()= event(){}

	SpawnerUpgradedMessage<localizes>(PrevLevel:int, Level:int):message = "Creature Spawner Upgraded: {PrevLevel} -> {Level} lvl"
	SpawnerUpgradedMessage2<localizes>(Prev:int, Cur:int):message = "Per Kill: {Prev}💸 -> {Cur}💸"

	Init():upgradable_spawner=
		Reset()
		for(I->S:D.Spawners):
			GoldPerKill := (option. B.GoldPerKillArr[I]).VOrErr(0)
			spawn. OnCreatureEliminatedAsync(S, GoldPerKill)
		
		Self
	
	SetOwner<public>(Agent:agent):void=
		set Owner = option. Agent
		# if (GoldPer := B.GoldPerKillArr[CurLevel]):
		# 	GrantFromKills.SetGoldPerPlayerKill(Agent, GoldPer * 5)
		
	OnCreatureEliminatedAsync(Spawner:creature_spawner_device, GoldPerKill:int)<suspends>:void=
		loop:
			Result := Spawner.EliminatedEvent.Await()
			if(Killer := Result.Source?):
				ResourcesManager.GiveGoldText(Killer, GoldPerKill, "Mob Eliminated")
	
	Upgrade<public>(Agent:agent):void=
		if(Agent = Owner?):
			if(CurLevel + 1 < D.Spawners.Length):
				Notifs.ShowTopNotification(Agent, SpawnerUpgradedMessage(CurLevel, CurLevel + 1))

				if (GoldPrev := B.GoldPerKillArr[CurLevel]
					GoldPer := B.GoldPerKillArr[CurLevel + 1]
				):
					# GrantFromKills.SetGoldPerPlayerKill(Agent, GoldPer * 5)
					Notifs.ShowTopNotification(Agent, SpawnerUpgradedMessage2(GoldPrev, GoldPer))
				
				HideSpawner(CurLevel)
				ShowSpawner(CurLevel + 1)
		else:
			LPrint("Wrong agent trying to upgrade")


	Reset<public>():void=
		set Owner = false
		Start := if(B.KeepFirstLevelEnabled?): 
			ShowSpawner(0)
			1
		else:
			0

		for(X := Start..D.Spawners.Length-1):
			HideSpawner(X)
			
	
	ShowSpawner(X:int):void=
		if(Spawner := D.Spawners[X]
			Pos := SpawnersInitPos[X]
		):
			Print("Showing Spawner")
			Spawner.Enable()
			if. Spawner.TeleportTo[Pos, rotation{}]
			set CurLevel = X
		else:
			LError()
			
		if(MapBuilder := UpgradableMapBuilders[X]):
			spawn. RespawnAllMap(MapBuilder)


	RespawnAllMap(MapBuilder:spawn_map)<suspends>:void=
		MapBuilder.RespawnAll()
		MapRespawnedEvent.Signal()
	
	HideSpawner(X:int):void=
		if(Spawner := D.Spawners[X]
			HidePos := SpawnersInitPos[X].WithZ(-9999.0)
		):
			Spawner.Disable()
			Spawner.EliminateCreatures()
			if. Spawner.TeleportTo[HidePos, rotation{}]

		
		if(MapBuilder := UpgradableMapBuilders[X]):
			MapBuilder.DespawnAll()
		

	
	# RunForOwner(Agent:agent, PlayerRemoved:event())<suspends>:void=
	# 	race:
	# 		PlayerRemoved.Await()
	# 		block:

	
	# InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
	# 	PlayerDataMap.Set(Agent, p_data{})
	# 	PlayerRemoved.Await()
	# 	PlayerDataMap.Remove(Agent)
	