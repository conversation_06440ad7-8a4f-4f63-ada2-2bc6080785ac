using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using.  VGiga

cinematic_sequence_device_pool_editable<public> := class<concrete>:
	@editable var FreeCreativeDevices : []cinematic_sequence_device = array{}

	GetAllFree<public>():[]cinematic_sequence_device=
		FreeCreativeDevices

	Rent<public>()<decides><transacts>:cinematic_sequence_device=
		if(CreativeDevice := FreeCreativeDevices[0]
			Val := FreeCreativeDevices.RemoveElement[0]
			set FreeCreativeDevices = Val
		):
			return CreativeDevice
		else:
			FailError[]
			return cinematic_sequence_device{}

	RentDisposable<public>()<decides><transacts>:pooled_editable_cinematic_sequence_device=
		Device := Rent[]
		pooled_editable_cinematic_sequence_device:
			Device := Device
			MyPool := Self

	Return<public>(CreativeDevice:cinematic_sequence_device):void=
		set FreeCreativeDevices += array. CreativeDevice


pooled_editable_cinematic_sequence_device<public> := class(i_disposable):
	MyPool<public>:cinematic_sequence_device_pool_editable
	Device<public>:cinematic_sequence_device

	Dispose<override>():void=
		MyPool.Return(Device)