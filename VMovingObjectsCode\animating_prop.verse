using { /Fortnite.com/Devices }

using { /Fortnite.com/Devices/CreativeAnimation }

using { /Verse.org/Simulation }

using { /UnrealEngine.com/Temporary/SpatialMath }

 

RotationRateTip<localizes>:message := "The time it takes to make one AdditionalRotation in seconds."

UseEasePerKeyframeTip<localizes>:message := "Whether this prop should use the MoveEaseType for each keyframe. <PERSON>als<PERSON> will use the Linear ease type on each frame."

 

# A prop that translates, rotates, and scales to a destination using animation.

animating_prop<public> := class<concrete>(movable_prop):

 

    # The additional rotation to apply to the RootProp per keyframe.

    @editable {ToolTip := AdditionalRotationTip}

    AdditionalRotation:rotation = rotation{}

 

    # The time it takes to make one AdditionalRotation in seconds.

    @editable {ToolTip := RotationRateTip}

    var RotationRate:float = 1.0

 

    # Whether this prop should use the MoveEaseType per each frame of animation.

    # Setting this to false will use the linear MoveEaseType on each frame.

    @editable {ToolTip := UseEasePerKeyframeTip}

    UseEasePerKeyframe:logic = true

 

    # The Creative prop target for the RootProp to move toward. The rootprop will also copy

    # the scale of each MoveTarget.

    @editable {ToolTip := MoveTargetsTip}

    var MoveTargets:[]creative_prop = array{}

 

    # The transform the prop is currently targeting.

    var TargetTransform:transform = transform{}

 

    # Move and rotate the RootProp toward the MoveTarget, or MoveTransform if one is set.

    Move<override>()<suspends>:void=

        # If there are no targets to move to, this prop will rotate in place.

        if:

            MoveTargets.Length = 0

        then:

            set TargetTransform = RootProp.GetTransform()

 

            # Build and play the animation.

            BuildAndPlayAnimation()

        else:

            # Move to each target in the MoveTargets array.

            for:

                MoveTarget:MoveTargets

            do:

                # Set the TargetTransform to the MoveTarget if the

                # MoveTarget is set. Otherwise set it to the MoveTransform.

                if:

                    MoveTarget.IsValid[]

                then:

                    set TargetTransform = MoveTarget.GetTransform()

 

                    # Build and play the animation.

                    BuildAndPlayAnimation()

 

    # Builds an animation from an array of keyframes, then calls MoveToEase()

    # to animate the prop.

    BuildAndPlayAnimation()<suspends>:void=

 

        var Keyframes:[]keyframe_delta = array{}

 

        # Build the animation, using the RootProp as the target transform.

        set Keyframes = BuildMovingAnimationKeyframes(MoveDuration, RotationRate, AdditionalRotation, RootProp.GetTransform(), TargetTransform, MoveEaseType, UseEasePerKeyframe)

 

        # Set the animation mode to OneShot.

        var AnimationMode:animation_mode := animation_mode.OneShot

 

        # Play the animation by calling MoveToEase(), passing in the KeyFrames array.

        RootProp.MoveToEase(Keyframes, AnimationMode)