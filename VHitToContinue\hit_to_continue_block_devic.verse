using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
using. VCustomBillboard

hit_to_continue_block_devic<public> := class(creative_device):
	@editable Manipulator:?prop_manipulator_device = false
	@editable HitBillboard:?custom_billboard_devic = false

	OnBegin<override>()<suspends>:void=
		Manipulator.G()

	SetHitsAmount(Amount:int):void=
		if. Bill := HitBillboard.V[] then Bill.SetText("{Amount}")
#gen
#pool_component
#hit_to_continue_block_devic
#id-1ecd746b-c4ba-42b6-bbde-8889a4e94a1f
hit_to_continue_block_devic_pool_component_c<public>(Area:area_interface, D:creative_device)<transacts>:hit_to_continue_block_devic_pool_component=
	hit_to_continue_block_devic_pool_component:
		Area := Area
		D := D
	.Init()

hit_to_continue_block_devic_pool_component<public> := class:
	Area<public>:area_interface
	var FreeCreativeDevices : []hit_to_continue_block_devic = array{}
	D<public>:creative_device

	Init()<transacts>:hit_to_continue_block_devic_pool_component=
		set FreeCreativeDevices = for(Obj : D.GetDevicesInArea(hit_to_continue_block_devic, Area)):
			Obj
		Self

	GetAllFree<public>():[]hit_to_continue_block_devic=
		FreeCreativeDevices

	Rent<public>()<decides><transacts>:hit_to_continue_block_devic=
		if(CreativeDevice := FreeCreativeDevices[0]
			Val := FreeCreativeDevices.RemoveElement[0]
			set FreeCreativeDevices = Val
		):
			return CreativeDevice
		else:
			FailError[]
			return hit_to_continue_block_devic{}

	RentDisposable<public>()<decides><transacts>:pooled_editable_hit_to_continue_block_devic=
		Device := Rent[]
		pooled_editable_hit_to_continue_block_devic:
			Device := Device
			MyPool := Self

	Return<public>(CreativeDevice:hit_to_continue_block_devic):void=
		if. CreativeDevice.TeleportTo[Area.GetCenterPoint(), rotation{}]
		set FreeCreativeDevices += array. CreativeDevice


pooled_editable_hit_to_continue_block_devic<public> := class(i_disposable):
	MyPool<public>:hit_to_continue_block_devic_pool_component
	Device<public>:hit_to_continue_block_devic

	Dispose<override>():void=
		MyPool.Return(Device)

	
#id-1ecd746b-c4ba-42b6-bbde-8889a4e94a1f