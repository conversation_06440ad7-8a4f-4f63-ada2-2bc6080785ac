using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga
using. VGiga.Pool
using. VPopup
using. VResourcesSystem

quiz := class(auto_creative_device, i_init, i_init_per_player_async):
	@editable QuizPopupQuestionsPool:?popup_dialog_device_pool_editable = false
	@editable QuizPopupQuestionsOneBtnPool:?popup_dialog_device_pool_editable = false

	AgentToPopup:map_agent_popup_dialog_device = map_agent_popup_dialog_device{}
	AgentToPopupOneBtn:map_agent_popup_dialog_device = map_agent_popup_dialog_device{}

	@editable QuizPopupQuestionsGoodAns:?popup_dialog_device = false
	@editable QuizPopupQuestionsBadAns:?popup_dialog_device = false
	
	OpenQuizEv:event(tuple(agent,quiz_type)) = event(tuple(agent,quiz_type)){}
	var Popup:?popup = false
	var Loc:i_localization = empty_i_localization{}

	var Resources:?resources_manager = false

	QuizDatas :quiz_datas= quiz_datas{}

	Init<override>(Container:vcontainer):void=
		QuizPopupQuestionsGoodAns.G().SetButtonText("Ok".ToMessage(), 2)
		QuizPopupQuestionsBadAns.G().SetButtonText("Ok".ToMessage(), 2)
		set Loc = Container.Resolve_i_localization()
		set Popup = Container.ResolveOp[popup] or Err()
		set Resources = Container.ResolveOp[resources_manager] or Err()

		for(Trigger:tag_open_knowledge_quiz{}.GetAll(Self)):
			spawn. OnOpenQuizTrigger(Trigger, quiz_type.Cybersecurity)
		for(Trigger:tag_open_math_quiz{}.GetAll(Self)):
			spawn. OnOpenQuizTrigger(Trigger, quiz_type.Math)
		for(Trigger:tag_open_banking_quiz{}.GetAll(Self)):
			spawn. OnOpenQuizTrigger(Trigger, quiz_type.Banking)
		for(Trigger:tag_open_christmas_quiz{}.GetAll(Self)):
			spawn. OnOpenQuizTrigger(Trigger, quiz_type.Christmas)

	OnOpenQuizTrigger(Trigger:trigger_device, Type:quiz_type)<suspends>:void=
		loop:
			Agent := Trigger.TriggeredEvent.Await()
			if(Ag := Agent?):
				OpenQuizEv.Signal(Ag, Type)

	InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
		Print("quiz InitPlayerAsync"); Sleep(0.0)
		QuizVerseBg := make_quiz_ui_generated()
		# QuizPopup := QuizPopupQuestions.G()
		if(QuizPopup := QuizPopupQuestionsPool.G().RentDisposable[]
			QuizOneButton := QuizPopupQuestionsOneBtnPool.G().RentDisposable[]
		):
			# QuizOneButton := QuizPopupQuestionsOneBtn.G()
			race:
				PlayerRemoved.Await()
				loop:
					var QuizType:quiz_type = quiz_type.Math
					loop:
						OpenQuizData := OpenQuizEv.Await()
						if(Agent = OpenQuizData(0)):
							set QuizType = OpenQuizData(1)
							break
					
					PopRes := Popup.G().ShowOkCancel(Agent, "Do you want to start the knowledge quiz?")
					if(PopRes?):
						# QuizBg.Show(Agent)

						# Agent.AddToPlayerUi(QuizVerseBg.QuizUi, true, 3)
						KnowledgeQuizDatasRandomized := Shuffle of if(Loc.IsEng[Agent]):
							case (QuizType):
								quiz_type.Math => QuizDatas.MathDatas
								quiz_type.Cybersecurity => QuizDatas.CybersecurityDatas
								quiz_type.Banking => QuizDatas.BankingDatas
								quiz_type.Christmas => QuizDatas.ChristmasDatas
						else:
							case (QuizType):
								quiz_type.Math => QuizDatas.MathDatasPll
								quiz_type.Cybersecurity => QuizDatas.CybersecurityDatasPl
								quiz_type.Banking => QuizDatas.BankingDatasPl
								quiz_type.Christmas => QuizDatas.ChristmasDatasPl

						var CorrectCounter:int=0
						QuestionsAmount:= Min(5, KnowledgeQuizDatasRandomized.Length)
						

						for(X:= 0..QuestionsAmount-1):
							QuizData := KnowledgeQuizDatasRandomized[X] or Err()

							# QuizPopup.Device.SetDescriptionText(Loc.GMsg(Agent, QuizData.Question))
							# QuizPopup.Device.SetButtonText(Loc.GMsg(Agent, QuizData.Answer1), 0)
							# QuizPopup.Device.SetButtonText(Loc.GMsg(Agent, QuizData.Answer2), 1)
							# QuizPopup.Device.SetButtonText(Loc.GMsg(Agent, QuizData.Answer3), 2)
							LPrint(QuizData.Question)
							QuizPopup.Device.SetDescriptionText(QuizData.Question.ToMessage())
							QuizPopup.Device.SetButtonText(QuizData.Answer1.ToMessage(), 0)
							QuizPopup.Device.SetButtonText(QuizData.Answer2.ToMessage(), 1)
							QuizPopup.Device.SetButtonText(QuizData.Answer3.ToMessage(), 2)
							Sleep(0.0)
							QuizPopup.Device.Show(Agent)

							# QuizUi.QuestionText.SetTextColor(NamedColors.White)
							# QuizUi.QuestionText.SetText(Loc.GMsg(Agent, QuizData.Question))

							# QuizUi.ButtonAnswer1.SetText(EmptyMessage)
							# QuizUi.UEFN_TextBlock_C_245.SetText(Loc.GMsg(Agent, QuizData.Answer1))

							# QuizUi.ButtonAnswer2.SetText(Loc.GMsg(Agent, QuizData.Answer2))
							# QuizUi.ButtonAnswer3.SetText(Loc.GMsg(Agent, QuizData.Answer3))
							# QuizUi.OverlayBtn1.Show()
							# QuizUi.OverlayBtn2.Show()
							# QuizUi.OverlayBtn3.Show()

							# AnswerId := race:
							# 	QuizPopup.AwaitForRespondingButton(Agent)
								#todo add timer with wrong answer after
							AnswerId := QuizPopup.Device.AwaitForRespondingButton(Agent)
							QuizPopup.Device.Hide(Agent)
							
							# QuizUi.OverlayBtn1.Hide()
							# QuizUi.OverlayBtn2.Hide()
							# QuizUi.OverlayBtn3.Hide()

							
							
							PopupAns := if(AnswerId = QuizData.CorrectAnswerId):
								QuizPopupQuestionsGoodAns.G().SetDescriptionText(Loc.GMsg(Agent, "GOOD ANSWER,\nCONGRATS!"))
								# QuizUi.QuestionText.SetText(Loc.GMsg(Agent, "GOOD ANSWER, CONGRATS!"))
								# QuizUi.QuestionText.SetTextColor(NamedColors.SpringGreen)
								set CorrectCounter += 1
								QuizPopupQuestionsGoodAns.G()
							else:
								QuizPopupQuestionsBadAns.G().SetDescriptionText(Loc.GMsg(Agent, "BAD ANSWER :("))
								# QuizUi.QuestionText.SetText(Loc.GMsg(Agent, "BAD ANSWER :("))
								# QuizUi.QuestionText.SetTextColor(NamedColors.Red)
								QuizPopupQuestionsBadAns.G()
							
							# Sleep(0.0)
							PopupAns.Show(Agent)
							race:
								Sleep(3.0)
								PopupAns.AwaitForRespondingButtonOrExit(Agent, PlayerRemoved)
							PopupAns.Hide(Agent)
							# Sleep(0.0)

						# QuizUi.QuestionText.SetTextColor(NamedColors.White)

						TimeToFinishInSec:float= 5.0 * QuestionsAmount
						TimeToFinishInSecWithCorrectMulti:float= TimeToFinishInSec * (CorrectCounter * 1.0 / QuestionsAmount) 
						WillGetGoldAmount := Resources.G().CalculateGoldWithMultiplierMinSec(Agent, TimeToFinishInSecWithCorrectMulti, 0.20, 20)
						QuizOneButton.Device.SetDescriptionText(GetReward(Agent, CorrectCounter, QuestionsAmount, WillGetGoldAmount))
						# QuizUi.QuestionText.SetText(GetReward(Agent, CorrectCounter, QuestionsAmount, WillGetGoldAmount))
						# QuizUi.OverlayBtn1.Hide()
						# QuizUi.OverlayBtn3.Hide()
						# QuizUi.OverlayBtn2.Show()
						# QuizUi.ButtonAnswer2.SetText("Ok".ToMessage())
						QuizOneButton.Device.SetButtonText("Ok".ToMessage(), 2)
						Sleep(0.0)
						QuizOneButton.Device.Show(Agent)
						race:
							Sleep(5.0)
							AnswerId := QuizOneButton.Device.AwaitForRespondingButtonOrExit(Agent, PlayerRemoved)
							# QuizUi.ButtonAnswer2.OnClick().Await()
						QuizOneButton.Device.Hide(Agent)
						Sleep(0.0)
						Resources.G().GiveGoldText(Agent, WillGetGoldAmount, "Quiz")

					# Agent.RemoveFromPlayerUi(QuizVerseBg.QuizUi)
					# QuizBg.Hide(Agent)
					QuizPopup.Device.Hide(Agent)
					QuizOneButton.Device.Hide(Agent)
		
							
		# Agent.RemoveFromPlayerUi(QuizVerseBg.QuizUi)
		# QuizBg.Hide(Agent)

			QuizPopup.Dispose()
			QuizOneButton.Dispose()
		else:
			LError()

	GetReward(Agent:agent, CorrectCounter:int, QuestionsAmount:int, WillGetGoldAmount:int):message= 
		if(Loc.IsEng[Agent]):
			ToMsg("Correct answers: {CorrectCounter}/{QuestionsAmount}\nGold reward: ${WillGetGoldAmount}")
		else:
			ToMsg("Dobre odpowiedzi: {CorrectCounter}/{QuestionsAmount}\nNagroda: ${WillGetGoldAmount}")

quiz_data := class:
	CorrectAnswerId:int
	Question:string
	Answer1:string
	Answer2:string
	Answer3:string					
			
		
		# 		OpenEv.Await
		# 		QuizUi := make
		# 		Len := KnowledgeQuizDatas.Length
		# 		QuizData := KnowledgeQuizDatas[GetRandomInt(0, Len -1)] or Err()
		# 		QuizUi.DescText.SetText(QuizData.Question)
		# 		QuizUi.AnswerButton1.SetText(QuizData.Answer1)
		# 		QuizUi.AnswerButton2.SetText(QuizData.Answer2)
		# 		QuizUi.AnswerButton3.SetText(QuizData.Answer3)
		# 		AnswerId := race:
		# 			block:
		# 			buttons...
		# 		if(AnswerId = QuizData.CorrectAnswerId):


	
		

		
		
quiz_type := enum:
	Math
	Cybersecurity
	Banking
	Christmas
	