using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }
using { VGiga.Pool }
using. VCoop
using. VResourcesSystem

money_as_gold_devic<public> := class(auto_creative_device, i_init_async, i_init_per_player_async):
    @editable ConditionalButton:conditional_button_device = conditional_button_device{}
    @editable GoldGrantersPool:item_granter_device_pool = item_granter_device_pool{}
    
    var PlayerDataMap : p_data_map = p_data_map{}
    var GoldGrantTickRate : float = 1.0
    var PlayerEvents :?player_events_manager_devic= false
    var Coop :?coop= false
    var Save :?save_system= false
    var ResourcesBalance :?resources_manager_balance= false

    # var ConditionalGoldButtons
    
    # WaitForAnyConditionalButton(Id:int, Agent:agent)<suspends>:race_track=
    #     if(Tracks.Length = 0):
    #         LError()
    #         Sleep(Inf)

    #     if(Track := Tracks[Id]):
    #         race:
    #             block:
    #                 Track.StartChannel.G().ReceivedTransmitEvent.AwaitForOption(Agent)
    #                 Track
    #             WaitForAnyTrack(Id + 1, Agent)
    #     else:
    #         Sleep(Inf)
    #         Err()
    OnButtonActivated(Spender:agent, Button:conditional_button_device):void=
        spawn. OnButtonActivatedAsync(Spender, Button)

    OnButtonActivatedAsync(Spender:agent, Button:conditional_button_device)<suspends>:void=
        GoldSpent := Button.GetItemCountRequired(0)
        for(Agent->Data:PlayerEvents.G().RegisteredPlayers
            Agent <> Spender
        ):
            TryToTakeGold(Agent, GoldSpent, ?ForceSingle := true)

    InitAsync<override>(Container:vcontainer)<suspends>:void=
        set	ResourcesBalance = Container.ResolveOp[resources_manager_balance] or Err()
        set Save = Container.ResolveOp[save_system] or Err()
        set Coop = Container.ResolveOp[coop] or Err()
        set PlayerEvents = Container.ResolveOp[player_events_manager_devic] or Err()

        if(not ResourcesBalance.G().MoneyAsGold?):
            return
            
        for(Button:tag_conditional_button_gold{}.GetAll(Self)):
            Button.ActivatedEvent.SubscribeAnything(OnButtonActivated, Button)
 
        var GoldAddActions :[agent]int = map{}
        var GoldReceivedFromAddActions :[agent]int = map{}

        Sleep(1.0)
        loop:
            set GoldAddActions = map{}
            for(Agent -> Data : PlayerDataMap.DataMap
                Agent.GetFortCharacterActive[]
            ):
                GoldReceived := GoldReceivedFromAddActions[Agent] or 0
                set Data.GoldAmountLastFrame = GetGold(Agent) + GoldReceived
            set GoldReceivedFromAddActions = map{}      
            Sleep(0.0)
            for(Agent -> Data : PlayerDataMap.DataMap
            ):
                GoldThisFrame := GetGold(Agent)
                if(GoldThisFrame > -1):
                    GoldEarned := GoldThisFrame - Data.GoldAmountLastFrame
                    WasGoldEarned := GoldEarned > 0 and true or false
                    if(WasGoldEarned?):
                        if. set GoldAddActions[Agent] = GoldEarned
 
            for(AgentEarner -> GoldEarned:GoldAddActions
                AgentReceiver -> Data : PlayerEvents.G().RegisteredPlayers
                AgentEarner <> AgentReceiver
            ):
                AddGold(AgentReceiver, GoldEarned)
                if(OldGold := GoldReceivedFromAddActions[AgentReceiver]):
                    if. set GoldReceivedFromAddActions[AgentReceiver] = OldGold + GoldEarned
                else:
                    if. set GoldReceivedFromAddActions[AgentReceiver] = GoldEarned

                        
    
    InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
        Print("gold_granter InitPlayerAsync"); Sleep(1.0)
        if(not ResourcesBalance.G().MoneyAsGold?):
            return
        if(Player := player[Agent]):
            var Data :p_data= p_data{}
            if(Admin := Coop.G().CoopAdmin?):
                if(Admin = Agent):
                    Save.G().WaitForSaveInitedForPlayer(Agent)
                    SavedGold := Save.G().GetGold(Player)
                    AddGold(Agent, SavedGold)
                    set Data.GoldAmountLastFrame = SavedGold
                else:
                    Sleep(1.0)
                    GoldToSync := GetGold(Admin)
                    AddGold(Agent, GoldToSync)
                    set Data.GoldAmountLastFrame = GoldToSync
            else:
                Save.G().WaitForSaveInitedForPlayer(Agent)
                SavedGold := Save.G().GetGold(Player)
                AddGold(Agent, SavedGold)
                set Data.GoldAmountLastFrame = SavedGold
            Sleep(0.0)
            PlayerDataMap.Set(Agent, Data)
            # race:
                PlayerRemoved.Await()
                # loop:
                #     set Data.GoldAmountLastFrame = GetGold(Agent)
                #     Sleep(0.0)
                #     GoldThisFrame := GetGold(Agent)
                #     WasGoldSpent := GoldThisFrame < Data.GoldAmountLastFrame and true or false
                #     if(WasGoldSpent?):


            PlayerDataMap.Remove(Agent)


    # AddGoldToGrant<public>(Agent:agent, Amount:int):void=
    #     if(PlayerData := PlayerDataMap.Get[Agent], Amount > 0):
    #         set PlayerData.GoldToGrant += Amount
    #         PlayerDataMap.Set(Agent, PlayerData)


    TryToTakeGold<public>(Agent:agent, Amount:int, ?ForceSingle :logic= false)<suspends>:logic=
        CurGold := GetGold(Agent)
        if(CurGold < Amount):
            return false

        if(not ForceSingle?, Coop.G().IsCoop[]):
            for(Agent2->Data:PlayerEvents.G().RegisteredPlayers
                Agent2 <> Agent
            ):
                TryToTakeGold(Agent2, Amount, ?ForceSingle := true)
                

        ConditionalButton.SetItemCountRequired(0, Amount)
        return race:
            block:
                ConditionalButton.ActivatedEvent.Await()
                true
            block:
                ConditionalButton.NotEnoughItemsEvent.Await()
                false
            block:
                ConditionalButton.Activate(Agent)
                Sleep(3.0) #timeout
                false

    GetGold<public>(Agent:agent):int=
        ConditionalButton.GetItemCount(Agent, 0)

    AddGold<public>(Agent:agent, Amount:int):void=
        if(Amount < 0):
            LErrorPrint("Error! Trying to give negative gold!!!")
        var CurrentAmount : int = Amount
        
        if(GoldGranter := GoldGrantersPool.Rent[]):
            Powers := array{6,5,4,3,2,1,0}
            for(Power : Powers
                Divider := Round[Pow(10.0, Power * 1.0)]
                Iterations := Floor(CurrentAmount / Divider)
                Iterations > 0
                CrAmt := Mod[Amount, Divider]
            ):
                set CurrentAmount = CrAmt
                GoldGranter.SetNextItem(Power)
                for(I:=1..Iterations):
                    GoldGranter.GrantItem(Agent)

            GoldGrantersPool.Return(GoldGranter)

p_data := class<internal>:
    var GoldAmountLastFrame : int = 0
    # var GoldToGrant : int = 0

p_data_map := class:
    var DataMap : [agent]p_data = map{}

    Get<public>(Player:agent)<decides><transacts>:p_data=
        if(Data := DataMap[Player]):
            Data
        else:
            FailError[]
            Err()

    Set<public>(Player:agent, Data:p_data):void=
        if. set DataMap[Player] = Data

    Remove<public>(Player:agent):void=
        set DataMap = DataMap.WithRemoved(Player)