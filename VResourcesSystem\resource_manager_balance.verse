using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
resources_manager_balance<public> := class(class_interface):
	var FansEnabled<public> :logic= true	
	var MoneyAsGold<public> :logic= true		
	var EnableWoodResource<public> :logic= true	
	var UiShowGoldPerSecond<public> :logic= true	
	var UiShowPets<public> :logic= true	
	var UiShowWood<public> :logic= true	
	var UiShowRebirths<public> :logic= true	
	var UiShowName<public> :logic= true	
	var UiShowVerificationCode<public> :logic= true	
	var UiShowTokens<public> :logic= true
	var HideOnlyTokensOverlay<public> :logic= false
	var HideOnlyGoldOverlay<public> :logic= false		