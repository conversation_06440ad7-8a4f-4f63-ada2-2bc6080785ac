
using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga
using. VResourcesSystem
leaderboard := class(auto_creative_device, i_init_per_player_async, i_init):

	@editable MOpenLandingPageButton:?button_device = false
	@editable MAddPointButton:?button_device = false
	@editable MAdd100PointsButton:?button_device = false
	
	var Save :?save_system= false

	var Resources :?resources_manager= false

	Init<override>(Container:vcontainer):void=
		set Resources = Container.ResolveOp[resources_manager] or Err()
		set Save = Container.ResolveOp[save_system] or Err()
	
	InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
		Print("leaderboard InitPlayerAsync"); Sleep(0.0)
		var MLandingUi :?leaderboard_landing_page_generated= false
		race:
			PlayerRemoved.Await()
			loop:
				if(AddPointButton := MAddPointButton?):
					AddPointButton.InteractedWithEvent.AwaitFor(Agent)
					Resources.G().GivePoints(Agent, 1)
				else:
					Sleep(Inf)
			loop:
				if(Add100PointsButton := MAdd100PointsButton?):
					Add100PointsButton.InteractedWithEvent.AwaitFor(Agent)
					Resources.G().GivePoints(Agent, 100)
				else:
					Sleep(Inf)
			block:
				if(OpenLandingPageButton := MOpenLandingPageButton?):
					loop:
						OpenLandingPageButton.InteractedWithEvent.AwaitFor(Agent)
						Player := Agent.WaitForPlayerActive()
						LandingUi := make_leaderboard_landing_page_generated()
						set MLandingUi = option. LandingUi

						Playtime := Save.G().GetPlaytime(Player)
						Points := Save.G().GetPoints(Player)
						LandingUi.TextPoints.SetText("Points: {Points}".ToMessage())

						Code := HashIntWithTimeInFront(Points, 6819, Playtime)
						LandingUi.TextVerification.SetText("Verification Code:\n{Code}".ToMessage())

						Agent.AddToPlayerUi(LandingUi.LeaderboardLandingPage, true, 5)
						LandingUi.OkButton.OnClick().Await()
						Agent.RemoveFromPlayerUi(LandingUi.LeaderboardLandingPage)
						set MLandingUi = false
				else:
					Sleep(Inf)

		if(LandingUi := MLandingUi?):
			Agent.RemoveFromPlayerUi(LandingUi.LeaderboardLandingPage)

		
	
	