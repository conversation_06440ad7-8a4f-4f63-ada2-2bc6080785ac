using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}

class_interface<public> := interface{}
# i_register_many<public> := interface{}

i_init<public> := interface(class_interface):
	Init<public>(Container:vcontainer):void
<#>
	Init<override>(Container:vcontainer):void=

i_init_async<public> := interface(class_interface):
	InitAsync<public>(Container:vcontainer)<suspends>:void
<#>
	InitAsync<override>(Container:vcontainer)<suspends>:void=

i_init_per_player<public> := interface(class_interface):
	InitPlayer<public>(Agent:agent):void
<#>
	InitPlayer<override>(Agent:agent):void=

i_init_per_player_async<public> := interface(class_interface):
	InitPlayerAsync<public>(Agent:agent, PlayerRemoved:event())<suspends>:void
<#>
	InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=

i_init_everytime_per_player<public> := interface(class_interface):
	InitEverytimePlayer<public>(Agent:agent):void
<#>
	InitEverytimePlayer<override>(Agent:agent):void=

i_init_everytime_per_player_async<public> := interface(class_interface):
	InitEverytimePlayerAsync<public>(Agent:agent, PlayerRemoved:event())<suspends>:void
<#>
	InitEverytimePlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=

i_removable_per_player<public> := interface(class_interface):
	RemovePlayer<public>(Agent:agent):void
<#>
	RemovePlayer<override>(Agent:agent):void=

devic_tag := class(tag){}

# GetCreativeObjectsWithTagCast<public>(Tag:tag, Cast:type{_(:creative_object_interface)<transacts><decides>:t} where t:subtype(creative_object_interface)):[]t=
# 	Devices := GetCreativeObjectsWithTag(Tag)
# 	for(Device:Devices, Casted := Cast[Device]):
# 		Casted
	
auto_creative_device <public> := class(creative_device, class_interface):
	

# i_test<public>(t:type) := interface{
#     Cast<public>(D:creative_device)<transacts><decides>:t
# }

var DevicesProvider:weak_map(session, devices_provider) = map{}

(D:creative_device).GetDevicesInArea<public>(cast_type:castable_subtype(creative_object_interface), Area:area_interface)<transacts>:[]cast_type=
	Devices := D.GetDevices(cast_type)
	# var Counter:int = 0
	Arr := for(Device:Devices, Area.IsInside[Device.GetTransform().Translation]):
		# LPrint("{Counter}")
		# LPrint("is inside")
		Device
	# LPrint("DevicesInArea: {Arr.Length}")

	return Arr

(D:creative_device).GetDevicesInAreaLog<public>(cast_type:castable_subtype(creative_object_interface), Area:area_interface)<transacts>:[]cast_type=
	Devices := D.GetDevices(cast_type)
	LPrint("Devices: {Devices.Length}")
	Arr := for(Device:Devices, Area.IsInsideLog[Device.GetTransform().Translation]):
		Device
	LPrint("DevicesInArea: {Arr.Length}")

	return Arr

# GetDevicesWithNormalTag<public>(Cast:type{_(:creative_object_interface)<transacts><decides>:t}, Tag:tag where t:subtype(creative_object_interface))<transacts>:[]t=
# 	Devices := GetCreativeObjectsWithTag(Tag)
# 	for(Device:Devices,
# 		Casted := Cast[Device]
# 	):
# 		Casted


(D:creative_device).GetDevices<public>(cast_type:castable_subtype(creative_object_interface))<transacts>:[]cast_type=
	Devices := D.GetProvider().Devices
	
	for(Device:Devices,
		Casted := cast_type[Device]
	):
		Casted


(D:creative_device).GetDevicesLog<public>(cast_type:castable_subtype(creative_object_interface))<transacts>:[]cast_type=
	Devices := D.GetDevices(cast_type)
	LPrint("Devices Found: {Devices.Length}")
	Devices
		
(D:creative_device).GetDevicesWithTag<public>(cast_type:castable_subtype(creative_object_interface), Tag:tag_comparable)<transacts>:[]cast_type=
	Devices := D.GetProvider().GetDevicesWithTag(Tag)
	
	for(Device:Devices,
		Casted := cast_type[Device]
	):
		Casted

# GetDevicesWithTagLog<public>(Cast:type{_(:creative_object_interface)<transacts><decides>:t}, Tag:tag_comparable where t:type)<transacts>:[]t=
# 	Devices := GetProvider().GetDevicesWithTag(Tag)
	
# 	for(Device:Devices,
# 		Casted := Cast[Device]
# 	):
# 		Casted

(D:creative_device).GetProvider()<transacts>:devices_provider=
	Session := GetSession()
	if(Provider := DevicesProvider[Session]):
		Provider
	else:
		Provider := devices_provider:
			Devices := D.FindCreativeObjectsWithTag(devic_tag{})
			RandomDevice := D
		if. set DevicesProvider[Session] = Provider
		Provider

(D:creative_device).GetDevicesWithTagCreativeObject<internal>(cast_type:castable_subtype(creative_object_interface), Tag:tag_comparable)<transacts>:[]cast_type=
	Devices := D.GetProvider().GetDevicesWithTag(Tag)
	
	for(Device:Devices,
		Casted := cast_type[Device]
	):
		Casted

# (D:creative_device).GetDevicesInAreaWithTagSimple<public>(Cast:type{_(:creative_object_interface)<transacts><decides>:t}, Area:area_interface, Tag:tag where t:subtype(creative_object_interface))<transacts>:[]t=
# 	Devices := D.FindCreativeObjectsWithTag(Tag)
# 	Arr := for(Device:Devices
# 		Casted := Cast[Device] 
# 		Area.IsInside[Device.GetTransform().Translation]
# 	):
# 		# LPrint("{Counter}")
# 		# LPrint("is inside")
# 		Device
# 	# LPrint("DevicesInArea: {Arr.Length}")

# 	return Arr

(D:creative_device).GetDevicesInAreaWithTag<public>(cast_type:castable_subtype(creative_object_interface), Area:area_interface, Tag:tag_comparable)<transacts>:[]cast_type=
	Devices := D.GetDevicesWithTagCreativeObject(cast_type, Tag)
	Arr := for(Device:Devices

		Area.IsInside[Device.GetTransform().Translation]
	):
		# LPrint("{Counter}")
		# LPrint("is inside")
		Device
	# LPrint("DevicesInArea: {Arr.Length}")

	return Arr

# ToPositionalError(T:t where t:subtype(class_interface))<decides><transacts>:positional=
# 	if(Retrun := positional[T]):
# 		Return
# 	else:
# 		LErrorPrint("NOT POSITIONAL")
# 		Fail[]
# 		Err()
	

(D:creative_device).etDevicesInAreaWithTagLog<public>(cast_type:castable_subtype(creative_object_interface), Area:area_interface, Tag:tag_comparable)<transacts>:[]cast_type=
	Devices := D.GetDevicesWithTagCreativeObject(cast_type, Tag)
	LPrint("Devices with tag before area check: {Devices.Length}")
	Arr := for(Device:Devices, Area.IsInside[Device.GetTransform().Translation]):
		# LPrint("{Counter}")
		# LPrint("is inside")
		Device
	LPrint("DevicesInArea: {Arr.Length}")
	if(Arr.Length < 15):
		for(Device:Devices, Area.IsInsideLog[Device.GetTransform().Translation]):

	return Arr

(D:creative_device).GetDevice<public>(cast_type:castable_subtype(creative_object_interface))<decides><transacts>:cast_type=
	Devices := D.GetDevices(cast_type)
	if(Devices.Length > 1):
		LErrorPrint("Error, too many devices")
	else if(Devices.Length = 0):
		LErrorPrint("Error, no devices")
	
	Devices[0]


(D:creative_device).GetDeviceLog<public>(cast_type:castable_subtype(creative_object_interface))<decides><transacts>:cast_type=
	LPrint("GetDevice11")
	Devices := D.GetDevices(cast_type)
	LPrint("GetDevice22")
	if(Devices.Length > 1):
		LErrorPrint("Error, too many devices")
	else if(Devices.Length = 0):
		LErrorPrint("Error, no devices")
	
	Devices[0]

(D:creative_device).GetDeviceInArea<public>(cast_type:castable_subtype(creative_object_interface), Area:area_interface)<decides><transacts>:cast_type=
	D.GetDevicesInArea(cast_type, Area)[0]

(D:creative_device).GetDeviceInAreaLog<public>(cast_type:castable_subtype(creative_object_interface), Area:area_interface)<decides><transacts>:cast_type=
	D.GetDevicesInAreaLog(cast_type, Area)[0]


# GetDevices2<public>(Cast:i_test(t) where t:subtype(creative_device)):[]t={
#     Session := GetSession()
#     Devices := if(Provider := DevicesProvider[Session]){
#         Provider.Devices
#     }else{
#         Devices2 := devic_tag_GetAll()
#         if. set DevicesProvider[Session] = devices_provider{
#             Devices := Devices2
#         }
#         Devices2
#     }
#     return for(Device:Devices,
#         Casted := Cast.Cast[Device]
#     ){
#         Casted
#     }
# }

devices_provider := class:
	Devices<public>:generator(creative_object_interface)
	RandomDevice<public>:creative_device
	var DevicesWithTagMap<public>:[comparable]generator(creative_object_interface) = map{}

	GetDevicesWithTag(Tag:tag_comparable)<transacts>:generator(creative_object_interface)=
		if(DevicesTag := DevicesWithTagMap[Tag.GetComparable()]):
			DevicesTag
		else:
			DevicesTag := RandomDevice.FindCreativeObjectsWithTag(Tag) # this works fine! child class is used
			if. set DevicesWithTagMap[Tag.GetComparable()] = DevicesTag
			DevicesTag
# makes push verse changes not working
# tag_castable<public>(t:type) := class(tag):
# 	Cast<public>(D:creative_object_interface)<decides><transacts>:t=
# 		Err()
		
# 	GetAll<public>(D:creative_device)<transacts>:[]t=
# 		Devices := D.FindCreativeObjectsWithTag(Self)
# 		for(Device:Devices,
# 			Casted := Cast[Device]
# 		):
# 			Casted
# 	GetAllLog<public>(D:creative_device)<transacts>:[]t=
# 		Devices := D.FindCreativeObjectsWithTag(Self)
# 		LPrint("Devices.Length {Devices.GetLength()}")
		
# 		for(Device:Devices,
# 			Casted := Cast[Device]
# 		):
# 			Casted

(Generator:generator(t) where t:type).GetLength()<transacts>:int=
	var Len:int= 0
	for(El:Generator):
		set Len += 1
	Len


resetable<public> := interface(class_interface):
	Reset<public>():void

i_on_rebirth<public> := interface(class_interface):
    OnRebirth<public>(Agent:agent):void

# race_track_tag := class(tag){}

# race_track_comparable_tag := class(tag_comparable):
# 	GetComparable<override>()<computes>:comparable=
# 		"race_track"
# GetDevicesWithNormalTag<public>(Cast:type{_(:creative_object_interface)<transacts><decides>:t}, Tag:tag where t:subtype(creative_object_interface))<transacts>:[]t=
# 	Devices := GetCreativeObjectsWithTag(Tag)
# 	for(Device:Devices,
# 		Casted := Cast[Device]
# 	):
# 		Casted
tag_comparable<public> := class(tag):
	GetComparable<public>()<computes>:comparable=Err()

i_disposable<public> := interface:
	Dispose<public>():void

disposable_as_event_c<public>(DisposedEvent:event())<transacts>:disposable_as_event=
	disposable_as_event:
		DisposedEvent := DisposedEvent

disposable_as_event<public> := class(i_disposable):
	DisposedEvent<public>:event()

	Dispose<override>():void=
		DisposedEvent.Signal()
