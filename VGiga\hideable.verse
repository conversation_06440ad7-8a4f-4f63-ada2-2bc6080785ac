using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 

(Obj:creative_object).InitPropHideable<public>()<transacts>:hideable_prop=
	InitTr := if(invalidatable[Obj]):
		invalidatable[Obj].IsValid[] and Obj.GetTransform() or transform{}
	else:
		Obj.GetTransform()
		
	hideable_prop:
		Obj := Obj
		InitTr := InitTr
	
hideable_prop<public> := class<internal>:
	InitTr:transform
	Obj:creative_object

	Show<public>()<transacts>:void=
		if. Obj.TeleportTo[InitTr]

	Hide<public>()<transacts>:void=
		if. Obj.TeleportTo[InitTr.WithTranslationZ(-900.0)]
		
	