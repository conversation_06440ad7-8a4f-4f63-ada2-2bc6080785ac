using. /Fortnite.com/Devices
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/SpatialMath
using. /Verse.org/Simulation
using. /Verse.org/SpatialMath



move_object<public> := class(creative_device):
	@editable StartTransformProp : creative_prop = creative_prop{}
	@editable EndTransformProp : creative_prop = creative_prop{}
	@editable PropToMove : creative_prop = creative_prop{}
	# @editable MStartChannel : channel_device = channel_device{}
	# @editable ResetChannel : channel_device = channel_device{}
	@editable MoveDuration  : float = 1.0

	StartEv:event() := event(){}
	ResetEv:event() := event(){}

	OnBegin<override>()<suspends>:void=
		StartTr := StartTransformProp.GetTransform()
		EndTr := EndTransformProp.GetTransform()
		loop:
			race:
				block:
					StartEv.Await()
					if. PropToMove.TeleportTo[StartTr]
					PropToMove.MoveTo(EndTr, MoveDuration)
				block:
					ResetEv.Await()
					if. PropToMove.TeleportTo[StartTr]

	Start<public>():void=
		StartEv.Signal()

	Reset<public>():void=
		ResetEv.Signal()
		





