using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 

(Popup:popup_dialog_device).ShowAwaitForButtonOrExit<public>(Agent:agent, PlayerRemoved:event())<suspends>:?int=
	Popup.Show(Agent)
	race:
		PlayerRemoved.Await()
		Popup.DismissedEvent.AwaitFor(Agent)
		Popup.TimeOutEvent.AwaitFor(Agent)
		loop:
			Id := Popup.AwaitForRespondingButton(Agent)
			return option. Id
	return false

(Popup:popup_dialog_device).ShowAwaitForButtonOrExitAndHide<public>(Agent:agent, PlayerRemoved:event())<suspends>:?int=
	Popup.Show(Agent)
	race:
		PlayerRemoved.Await()
		Popup.DismissedEvent.AwaitFor(Agent)
		Popup.TimeOutEvent.AwaitFor(Agent)
		loop:
			Id := Popup.AwaitForRespondingButton(Agent)
			Popup.Hide(Agent)
			return option. Id
	Popup.Hide(Agent)
	return false

(Popup:popup_dialog_device).ShowAwaitForButtonOrExitAndHideWithTimout<public>(Agent:agent, PlayerRemoved:event(), Timeout:float)<suspends>:?int=
	Popup.Show(Agent)
	race:
		PlayerRemoved.Await()
		Popup.DismissedEvent.AwaitFor(Agent)
		Popup.TimeOutEvent.AwaitFor(Agent)
		Sleep(Timeout)
		loop:
			Id := Popup.AwaitForRespondingButton(Agent)
			Popup.Hide(Agent)
			return option. Id
	Popup.Hide(Agent)
	return false

(Popup:popup_dialog_device).AwaitForRespondingButtonOrExit<public>(Agent:agent, PlayerRemoved:event())<suspends>:?int=
	race:
		PlayerRemoved.Await()
		Popup.DismissedEvent.AwaitFor(Agent)
		Popup.TimeOutEvent.AwaitFor(Agent)
		loop:
			Id := Popup.AwaitForRespondingButton(Agent)
			return option. Id
	return false

(Popup:popup_dialog_device).AwaitForRespondingButton<public>(Agent:agent)<suspends>:int=
	loop:
		AnyAgentWithButtonId := Popup.RespondingButtonEvent.Await()
		if(Agent = AnyAgentWithButtonId(0)):
			return AnyAgentWithButtonId(1)


(Popup:popup_dialog_device).ShowLocalized<public>(Agent:agent, L:i_localization)<suspends>:void=
	L.GetSetLocalizedText(Agent, Popup.GetDescriptionText, Popup.SetDescriptionText)
	L.GetSetLocalizedText(Agent, Popup.GetTitleText, Popup.SetTitleText)
	for(X := 0..5):
		L.GetSetLocalizedTextIndex(Agent, Popup.GetButtonText, SetButtonTextFix, X, Popup)
	Popup.Show(Agent)

SetButtonTextFix(Text:message, Index:int, Popup:popup_dialog_device):void=
	Popup.SetButtonText(Text, Index)

(L:i_localization).GetSetLocalizedText(Agent:agent, Get:type{_()<transacts>:string}, Set:type{_(:message):void}):void=
	LPrint("Get() {Get()}")
	
	if(LocalizedText := L.GG[Agent, Get()]):
		Print("LocalizedText : {LocalizedText}")
		Set("LocalizedText ".ToMessage())

(L:i_localization).GetSetLocalizedTextIndex(Agent:agent, Get:type{_(:int)<transacts>:string}, Set:type{_(:message, :int, :popup_dialog_device):void}, Index:int, Popup:popup_dialog_device):void=
	LPrint("Get() {Get(Index)}")
	if(LocalizedText := L.GG[Agent, Get(Index)]):
		Print("LocalizedText : {LocalizedText}")
		Set("LocalizedText sdf".ToMessage(), Index, Popup)
