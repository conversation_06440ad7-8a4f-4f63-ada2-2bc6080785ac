using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga
using. VResourcesSystem
using. VCoop
using. VHouseSystem
using. VNotifications

conditional_button_for_gold := class(auto_creative_device, i_init_async):
    @editable UnlockIdForName:string = ""
    @editable Button:conditional_button_device = conditional_button_device{}
    @editable GoldAmount:int = 0
    @editable GoldPerSecAmount:int = 0
    @editable DurationInSec:float = 0.0
    @editable ShowNotification:logic = true
    @editable MCinematicSequence:?cinematic_sequence_device = false
    @editable MTimer:?timer_device = false
    
    var Resources:?resources_manager = false
    var Coop:?coop = false
    var MHousesBalance:?houses_balance = false
    var Notifications:?notifications_system = false
    var PlayerEvents:?player_events_manager_devic = false
    var Loc:i_localization = empty_i_localization{}

    StoppedMakingMoneyMessage<localizes>(Name:string, AmountPerSec:int):message = "{Name} zatrzymał się -${AmountPerSec}/s"
    
    StoppedMakingMoneyNoNameMessage<localizes>(AmountPerSec:int):message = "Maszyna zatrzymała się -${AmountPerSec}/s"
    
    InitAsync<override>(Container:vcontainer)<suspends>:void=
        set Loc = Container.Resolve_i_localization()
        set Resources = Container.ResolveOp[resources_manager] or Err()
        set Coop = Container.ResolveOp[coop] or Err()
        set Notifications = Container.ResolveOp[notifications_system] or Err()
        set PlayerEvents = Container.ResolveOp[player_events_manager_devic] or Err()
        set MHousesBalance = Container.ResolveOp[houses_balance] or block:
            LPrint("houses_balance not found for conditional_button_for_gold")
            false
        spawn. HandleButtonActivations()
    
    HandleButtonActivations()<suspends>:void=
        loop:
            if(Timer := MTimer?):
                Timer.SetMaxDuration(DurationInSec)
                Timer.Reset()

            Agent :agent= Button.ActivatedEvent.Await()
            Button.Disable()
            if (GoldAmount > 0):
                Resources.G().GiveGold(Agent, GoldAmount, ?ShowNotification := ShowNotification)
            if (GoldPerSecAmount > 0):
                Resources.G().IncreaseGoldPerSec(Agent, GoldPerSecAmount)
                if(Timer := MTimer?):
                    Timer.Start()

                if(CinematicSequence := MCinematicSequence?):
                    if(Coop.G().IsCoop[]):
                        CinematicSequence.Play()
                    else:
                        CinematicSequence.Play(Agent)
                Sleep(DurationInSec)
                if(CinematicSequence := MCinematicSequence?):
                    if(Coop.G().IsCoop[]):
                        CinematicSequence.Stop()
                    else:
                        CinematicSequence.Stop(Agent)
                if(Timer := MTimer?):
                    Timer.Complete()
                Resources.G().DecreaseGoldPerSec(Agent, GoldPerSecAmount)
                
                # Show notification to all registered players
                for(Player -> Data : PlayerEvents.G().GetRegisteredPlayers()):
                    MUnlockName :?string= if(
                        Balance := MHousesBalance?, 
                        Preset := Balance.Presets.Get[UnlockIdForName]
                    ):
                        option. Preset.Name
                    else:
                        false
                    if(UnlockName := MUnlockName?):
                        Translated := Loc.G(Player, UnlockName)
                        Notifications.G().ShowBigBottomNotification(Player, StoppedMakingMoneyMessage(Translated,GoldPerSecAmount), 5.0)
                    else:
                        Notifications.G().ShowBigBottomNotification(Player, StoppedMakingMoneyNoNameMessage(GoldPerSecAmount), 5.0)
                
            Button.Enable()

