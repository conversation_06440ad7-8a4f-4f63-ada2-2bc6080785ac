using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}

	

timer_periodic<public> := class(){
    var TimeStarted<public>:float = 0.0
    var Elapsed :float= 0.0
    TimeForPeriod<public>:float

    StopPrint<public>():void={
        Stop()
        Print("Elapsed: {Elapsed}")
    }

    Stop<public>():void={
        EndTime := GetSimulationElapsedTime()
        set Elapsed = EndTime - TimeStarted
    }

    Start<public>()<transacts>:timer_periodic={
        set TimeStarted = GetSimulationElapsedTime()
        Self
    }

    GetElapsed<public>()<transacts>:float={
        GetSimulationElapsedTime() - TimeStarted
    }
    PeriodEndedAndStartNew<public>()<decides><transacts>:void={
        GetElapsed() > TimeForPeriod
        Start()
    }
}

timer_simple<public> := class(){
    var TimeStarted<public>:float = 0.0
    var Elapsed :float= 0.0

    StopPrint<public>():void={
        Stop()
        Print("Elapsed: {Elapsed}")
    }

    Stop<public>():void={
        EndTime := GetSimulationElapsedTime()
        set Elapsed = EndTime - TimeStarted
    }

    Start<public>():void={
        set TimeStarted = GetSimulationElapsedTime()
    }

    GetElapsed<public>():float={
        GetSimulationElapsedTime() - TimeStarted
    }
}

timer_bench<public> := class(){
    Name<public>:string
    var TimeStarted<public>:float
    var StartsCount<public>:int
    var Elapsed :float= 0.0
    var ElapsedHistory :[]float= array{}

    StopPrint<public>():void={
        Stop()
        Print("Bench {Name}, elapsed: {Elapsed}")
    }

    Stop<public>():void={
        EndTime := GetSimulationElapsedTime()
        set Elapsed = EndTime - TimeStarted
        set ElapsedHistory += array{Elapsed}
    }

    StartNew<public>():void={
        set TimeStarted = GetSimulationElapsedTime()
        set StartsCount += 1
    }

    PrintAverage<public>():void={
        var Avg :float = 0.0
        for(EElapsed:ElapsedHistory){
            set Avg += EElapsed
        }
        set Avg /= StartsCount * 1.0
        Print("Bench {Name}, avg: {Avg}")
    }
}

StartTimerBench<public>(Name:string):timer_bench={
    timer_bench{
        Name := Name
        TimeStarted := GetSimulationElapsedTime()
        StartsCount := 1
    }
}

StartTimer<public>():timer_simple={
    timer_simple{
        TimeStarted := GetSimulationElapsedTime()
    }
}


StartTimerPeriodic<public>(Period:float):timer_periodic={
    timer_periodic{
        TimeStarted := GetSimulationElapsedTime()
        TimeForPeriod := Period
    }
}