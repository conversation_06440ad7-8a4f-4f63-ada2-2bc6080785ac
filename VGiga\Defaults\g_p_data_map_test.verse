#map_agent
#p_data_test
using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }
using { VGiga.Defaults }


map_agent_p_data_test<public> := class{
	var DataMap<public> : [agent]p_data_test = map{}
	DataCreatedEvent:event(tuple(agent, p_data_test)) = event(tuple(agent, p_data_test)){}

	Get<public>(Player:agent)<decides><transacts>:p_data_test={
		if(Data := DataMap[Player]){
			Data
		}else{
			FailError[]
			Err()
		}
	}

	GetOrAwait<public>(Player:agent)<suspends>:p_data_test={
		if(Data := DataMap[Player]){
			Data
		}else{
			var Return:?p_data_test = false
			loop:
				Data := DataCreatedEvent.Await()
				if(Data(0) = Player):
					set Return = option. Data(1)
					break
			Return.G()
			# DataCreatedEvent.AwaitForData(Player)
		}
	}


	Set<public>(Player:agent, Data:p_data_test):void={
		if. set DataMap[Player] = Data
		DataCreatedEvent.Signal((Player, Data))
	}

	Remove<public>(Player:agent)<transacts>:void={
		set DataMap = DataMap.WithRemoved(Player)
	}
}
