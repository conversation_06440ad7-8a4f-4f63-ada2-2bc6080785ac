using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 

money_to_gold_exchange_button := class(auto_creative_device, i_init_async):
    @editable Button:?button_device = false
    @editable MoneyCost:int = 1000
    @editable GoldGranter:?item_granter_device = false

    var Resources:?resources_manager = false

    InitAsync<override>(Container:vcontainer)<suspends>:void=
        set Resources = Container.ResolveOp[resources_manager] or Err()
        spawn. HandleButtonActivations()

    HandleButtonActivations()<suspends>:void=
        loop:
            Agent := Button.G().InteractedWithEvent.Await()
            Result := Resources.G().TryToTakeGold(Agent, MoneyCost)
            if(Result?):
                GoldGranter.G().GrantItem(Agent)