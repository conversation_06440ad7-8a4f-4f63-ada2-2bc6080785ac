using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI 

area_box_editable<public> := class<concrete>(area_interface):
	@editable Prop<public>:creative_prop = creative_prop{}
	var Area<public>:area_box = area_box{}

	Init<public>()<transacts>:void=
		set Area = area_box_c(Prop)
	
	IsTransformInside<override>(Tr:transform)<decides><transacts>:void=
		Area.IsTransformInside[Tr]
	
	IsInside<override>(Vec:vector3)<decides><transacts>:void=
		Area.IsInside[Vec]

	IsInsideLog<override>(Vec:vector3)<decides><transacts>:void=
		Area.IsInsideLog[Vec]

	GetRandomPointInside<override>()<transacts>:vector3=
		Area.GetRandomPointInside()

	AwaitAgentInside<override>(Agent:agent, PlayerExited:event())<suspends>:void=
		Area.AwaitAgentInside(Agent,PlayerExited)

	GetCenterPoint<override>()<transacts>:vector3=
		Area.CenterPoint

