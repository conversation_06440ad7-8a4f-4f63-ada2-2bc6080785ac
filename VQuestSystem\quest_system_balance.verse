using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }

quest_system_balance<public> := class:
	var AssignFirstQuestAtStart<public>:logic = true
	var Quests<public>:[]quest_data = array{}
	var QuestCompleters<public>:[string]i_quest_completer = map{}

	AddCompleter<public>(Completer:i_quest_completer):void=
		if. set QuestCompleters[Completer.GetTypeId()] = Completer

	AddArrowGetter<public>(Id:string, ArrowPositionGetter:(agent->?vector3)):void=
		if(Quest := Quests.FirstData[CheckQuestTypeId, Id]
			# Quest := Quests[QuestNum]
		):
			set Quest.ArrowPositionGetter = option. ArrowPositionGetter

	CheckQuestTypeId(Quest:quest_data, TypeId:string)<decides><transacts>:void=
		Quest.TypeId = TypeId
		
	AddCompleter<public>(TypeId:string, Event:event(agent)):void=
		AddCompleter(simple_quest_completer:
			TypeId := TypeId
			Event := Event
		)