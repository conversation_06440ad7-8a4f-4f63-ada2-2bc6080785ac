<mxfile host="65bd71144e">
    <diagram id="p9XrnY3BwZCtLVpv6ykM" name="Page-1">
        <mxGraphModel dx="1812" dy="976" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="2" value="AmountInvestedToReturn (save)&lt;div&gt;AppTransactions (save)&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
                    <mxGeometry x="-10" y="570" width="200" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="3" target="4">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="spends 1000" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="260" y="490" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="4" target="8">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="amount invested to return 1000 * 0.04" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="260" y="610" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="6" target="8">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="App transactions = money given * 25" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="150" y="830" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="8" target="10">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="money given = amount invested * 0.1 every min or 0.025 every 15sec" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="260" y="710" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="amount invested to return -= money given" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="420" y="610" width="120" height="60" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>