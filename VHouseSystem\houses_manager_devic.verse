using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga } 
using { VGiga.Pool }
using { VResourcesSystem }
using { VLocalization }
using{VArrowSystem}
using. VCreaturesSystem
using. VPropSpawner
using. VNotifications
using. VChestsSystem
using. VCustomBillboard
using. VAudio
using. VCinematic
using. VCoop

houses_manager<public> := class<internal>(auto_creative_device, i_init, i_init_per_player_async):
    @editable HighlightPropAssetMoney:creative_prop_asset = DefaultCreativePropAsset
    @editable HighlightPropAssetFans:creative_prop_asset = DefaultCreativePropAsset
    @editable PopupEndTime:?popup_dialog_device = false

    @editable TeleportToHouseOnChannelTransmit:channel_device = channel_device{}
    @editable MStartCheckpointAfterRebirth:?player_checkpoint_device = false
    @editable MRebirthCinematic:?cinematic_player = false

    @editable MPlayer_Init_New_And_Rebirth:?analytics_device = false
    @editable UnlocksAnalytics:[]string_w_analytics_device = array{}
    var UnlocksAnalyticsMap:[string]analytics_device = map{}
    
    # @editable FOR_BUTTONS_USE_VERSE_tag_teleport_to_house_button:string = ""
    # @editable FOR_COLLECTIBLE_USE_VERSE_tag_teleport_to_house_button:string = ""
    # @editable TeleportToHouseVolumes:[]volume_device = array{}

    PlayerClaimedHouseEvent:event(house_manager) = event(house_manager){}

    UnlockableUnlockedEvent<public> : event(unlocked_unlocked_event_data) = event(unlocked_unlocked_event_data){}
    UnlockableUnlockedEventAgent : event(agent) =  event(agent){}
    HouseClaimedEvent<public>:event(agent) = event(agent){}
    HouseResetEvent<public>:event(agent) = event(agent){}
    AllUnlocksBoughtEv<public>:event(agent) = event(agent){}

    var HouseManagers<public>:[]house_manager = array{}
    var Loc:i_localization = empty_i_localization{}
    var Balance:?houses_balance = false
    var SaveSystem:?save_system = false
    var BalanceSpawners:?upgradable_spawner_balance = false
    var ArrowSystem:?arrow_system = false
    var Audio:?audio = false

    var Resources :?resources_manager= false
    var PlayerEvents :?player_events_manager_devic= false

    var ShowEndTimeTriggersEvents:[]listenable(?agent) = array{}
    var Coop :?coop= false

    OnTeleportToHouseOnChannelTransmit()<suspends>:void=
        loop:
            MAgent := TeleportToHouseOnChannelTransmit.ReceivedTransmitEvent.Await()
            if(Agent := MAgent?):
                TeleportAgentToHouse(Agent)

    Init<override>(C:vcontainer):void=
        set Coop = C.ResolveOp[coop] or Err()
        for(Device:UnlocksAnalytics):
            if. set UnlocksAnalyticsMap[Device.String] = Device.Analytics
            
        set PlayerEvents = C.ResolveOp[player_events_manager_devic] or Err()
        spawn. OnTeleportToHouseOnChannelTransmit()
        # for(Volume:TeleportToHouseVolumes):
        # 	Volume.AgentEntersEvent.Subscribe1(TeleportAgentToHouse)
        # for(Button:tag_teleport_to_house_button{}.GetAll(Self)):
        # 	Button.InteractedWithEvent.Subscribe1(TeleportAgentToHouse)
            

        set ShowEndTimeTriggersEvents = for(Trigger:tag_open_end_time_trigger{}.GetAll(Self)):
            Trigger.TriggeredEvent

        # LPrint("ShowEndTimeTriggersEvents.Length {ShowEndTimeTriggersEvents.Length}")
        

        PopupEndTime.G()

        # LPrint("1 {1}")
        set Audio = C.ResolveOp[audio] or Err()
        set Resources = C.ResolveOp[resources_manager] or Err()
        
        set BalanceSpawners = C.ResolveErrOp(upgradable_spawner_balance)
        # LPrint("s")
        set SaveSystem = C.ResolveErrOp(save_system)
        # LPrint("sdsd")
        set Balance = C.ResolveErrOp(houses_balance)
        # LPrint("sdd")
        set Loc = C.Resolve_i_localization()
        # LPrint("sds")
        ResourceManager := C.ResolveErrOp(resources_manager)
        # LPrint("sdg")
        TriggersPool := C.ResolveErrOp(trigger_device_pool)
        # LPrint("sdf")
        set ArrowSystem = C.ResolveErrOp(arrow_system)
        # LPrint("sdfff")

        Notifs := C.ResolveErr(notifications_system)
        # LPrint("sdffafsdff")
        CustomBillboardSpawner := C.ResolveErr(custom_billboard_spawner)
        # LPrint("asdff")
        var AmountOfUnlockablesToFinish :int= 0
        for(Name->U:Balance.G().Presets.Map
            for(Data:U.AdditionalData
                (Data = unlockable_additional_data.HideOnly or Data = unlockable_additional_data.ShowUpdatePopup)
            ):
                true
            .Length = 0
        ):
            # LPrint("ToUnlock: {Name}")
            set AmountOfUnlockablesToFinish += 1
        
        
        
        set HouseManagers = for(House:GetDevices(house_manager_devic)):
            # MUpgradableSpawner :?upgradable_spawner= if(SpawnerDevice := GetDeviceInArea[upgradable_spawner_devic, House.Area]
            # ):
            # 	Spawner := SpawnerDevice.Init(C, BalanceSpawners.G(), PropSpawner, House.Area)
            # 	Sleep(0.0)
            # 	option. Spawner
            # else:
            # 	false
            # if(Spawner := MUpgradableSpawner?):
            # 	for(Builder:Spawner.UpgradableMapBuilders):
            # 		Builder.BeforeMapRespawnForAgentInAreaEvent.Subscribe1(TeleportAgentToHouse)
                
            # # var MapUnlockIdToResourceIdWithLevel:[string]tuple(int,int) = map{}
            # # var MapSaveResourceIdToUnlockableWithLevel:[int][]tuple(string,int) = map{}

            # # var SaveResourceId_To_Unlockables:[int][]unlockable_id_options = map{}
            # # for(Id->Unlock:Balance.Presets.Map
            # # 	ResourceId := Unlock.SaveResourceId
            # # 	ResourceId > 0
            # # 	set SaveResourceId_To_Unlockables[ResourceId] = (option. SaveResourceId_To_Unlockables[ResourceId]).VOr(array{}) + array{Unlock}
            # # ){}
            # # for(Unlockables:SaveResourceId_To_Unlockables):
            # # 	Unlockables.Sort(true, CompareUnlocks)
            
            # SpawnedChestManagers := for(ChestManager: GetDevicesInArea(spawned_chests_manager_devic, House.Area)
            # 	ChestInited := ChestManager.Init[C]
            # ):
            # 	ChestInited
            # spawn. RespawnChestsOnMapRespawn(MUpgradableSpawner, SpawnedChestManagers)
            # LPrint("INITED CHESTTTT")
            HouseManager := House.Init(house_manager_args_c(C, Balance.G(), HighlightPropAssetMoney, HighlightPropAssetFans, ResourceManager.G(), Loc, TriggersPool.G(), CheckIfPlayerCanAcquire, ArrowSystem.G(),  AmountOfUnlockablesToFinish, Notifs, CustomBillboardSpawner, SaveSystem.G(), MRebirthCinematic))
            HouseManager.UnlockableUnlockedEvent.Subscribe1(OnUnlockableUnlocked)
            HouseManager.HouseClaimedEvent.Subscribe1(OnHouseClaimedEvent)
            spawn. WatchHouseResetEvent(HouseManager)
            spawn. OnAllUnlocksBoughtEv(HouseManager)
            HouseManager
        for(HM:HouseManagers):
            HM.UnlockableUnlockedEvent.Subscribe1(OnUnlockableUnlockedEvent)
            HM.AgentRebirthEvent.Subscribe1(OnAgentRebirthEvent)
        Self

    GetPhonePositionForAgent<public>(Agent:agent):?vector3=
        if(House := GetAgentHouse[Agent]):
            option. House.PcTriggerInitTr.Translation
        else:
            false
        

    WatchHouseResetEvent(HouseManager:house_manager)<suspends>:void=
        loop:
            HouseManager.HouseResetEvent.Await()
            OnHouseResetEvent(HouseManager)

    OnAllUnlocksBoughtEv(HouseManager:house_manager)<suspends>:void=
        loop:
            HouseManager.AllUnlocksBoughtEv.Await()
            if(Owner := HouseManager.MOwner?):
                AllUnlocksBoughtEv.Signal(Owner)
    RespawnChestsOnMapRespawn(MUpgradableSpawner:?upgradable_spawner, SpawnedChestManagers:[]spawned_chests_manager)<suspends>:void=
        if(UpgradableSpawner := MUpgradableSpawner?):
            loop:
                UpgradableSpawner.MapRespawnedEvent.Await()
                for(ChestManager:SpawnedChestManagers):
                    ChestManager.Respawn()

    TeleportAgentToHouse <public>(Agent:agent):void=
        if(AdminCoop := Coop.G().CoopAdmin?):
            if(H := GetAgentHouse[AdminCoop]):
                H.D.HouseTeleport.Teleport(Agent)
        else:
            if(H := GetAgentHouse[Agent]):
                H.D.HouseTeleport.Teleport(Agent)

    TeleportAgentToHouseEntryMinigame<public>(Agent:agent):void=
        spawn. TeleportAgentToHouseEntryMinigameAsync(Agent)
        
    TeleportAgentToHouseEntryMinigameAsync(Agent:agent)<suspends>:void=
        if(H := GetAgentHouse[GetAdminOrAgent(Agent)]):
            if(PlayerEvents.G().Container.Resolve[cinematic_players_config].SkipCinematics?):
                H.D.HouseTeleport.Teleport(Agent)
            else:
                # H.D.HouseTeleport.Teleport(Agent) #fix for bank loading from far away
                # Sleep(0.0)
                H.D.HouseTeleportEntryMinigame.Teleport(Agent)
    
    GetAdminOrAgent(Agent:agent)<transacts>:agent=
        if(AdminCoop := Coop.G().CoopAdmin?):
            AdminCoop
        else:
            Agent

    GetAgentHouse<public>(Agent:agent)<decides><transacts>:house_manager=
        HouseManagers.FirstDataHouseAgent[IsPlayerOwningHouse, GetAdminOrAgent(Agent)]
        # HouseManagers.FirstData[IsPlayerOwningHouse, Agent]

    IsPlayerOwningHouse(House:house_manager, Agent:agent)<decides><transacts>:void=
        GetAdminOrAgent(Agent) = House.MOwner?

    OnAgentRebirthEvent(Agent:agent):void=
        if(StartCheckpointAfterRebirth := MStartCheckpointAfterRebirth?):
            StartCheckpointAfterRebirth.Register(Agent)

            if(Char := Agent.GetFortCharacterActive[]):
                Char.Damage(500.0)

            
        
        # for(Remover:D.SaveUnlocksRemovers):
        # 	Remover.Remove(Agent)

    OnUnlockableUnlockedEvent(Data:unlocked_unlocked_event_data):void=
        if(Data.Unlockable.GoldCost > 0
            not Data.WasLoaded?
        ):
            SaveSystem.G().OnHousePartUnlocked(Data.Agent, Data.UnlockableId)
            if(Analytics := UnlocksAnalyticsMap[Data.UnlockableId]):
                Analytics.Submit(Data.Agent)
        
    GetUpgradHouseCompleter():event(agent)=
        UnlockableUnlockedEventAgent
    
    CheckIfPlayerCanAcquire(Agent:agent)<transacts>:logic=
        for(Manager:HouseManagers
            Owner := Manager.MOwner?
            Owner = Agent
        ):
            return false
        
        return true

    ClaimFreeHouse<public>(Agent:agent)<suspends>:void=
        LPrint("ClaimFreeHouse")
        Sleep(0.1)

        if(CheckIfPlayerCanAcquire(Agent)?):
            if(FreeHouse := HouseManagers.ArrayFirstError[IsHouseFree]):
                # Sleep(1.0)
                # LPrint("ClaimHouse")
                # Sleep(1.0)
                FreeHouse.D.ClaimTrigger.Trigger(Agent)
                # Sleep(1.0)
                # LPrint("afterClaimHouse")
                # Sleep(1.0)
                Sleep(0.4)
                # if(Char := Agent.GetFortCharacterActive[]):
                # # 	# Char.SetShield(0.0)
                # # 	# Char.SetHealth(100.0)
                # # 	# Char.Damage(200.0)
                # TeleportAgentToHouseEntryMinigame(Agent)
                # spawn. ShowEntryPopupAfterLanding(Agent)
            
    # ShowEntryPopupAfterLanding(Agent:agent)<suspends>:void=
    # 	Sleep(1.0)
    # 	# race:
    # 	# 	PlayerEvents.G().PlayerRemovedOncePerGame
    # 	# 	loop:
    # 	# 		if(Char := Agent.GetFortCharacterActive[], Char.IsOnGround[]):

    # 	# 			break

        
    InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
        Print("houses_manages InitPlayerAsync"); Sleep(0.0)
        if(SaveSystem.G().GetHouseMap[Agent].Length > 0):
            spawn. ClaimFreeHouse(Agent)
        else:
            if(Balance.G().ShowArrowToCaptureHouseAtPlayerStart?):
                for(House:HouseManagers):
                    spawn. ShowArrowToHouse(House, Agent, PlayerRemoved)
            if(Player_Init_No_Save := MPlayer_Init_New_And_Rebirth?):
                Player_Init_No_Save.Submit(Agent)
            else:
                LPrint("MPlayer_Init_New_And_Rebirth analytics device missing")

        #todo time of completion - remove for new version or keep it saved?
        race:
            PlayerRemoved.Await()
            block:
                # HouseClaimedEvent.AwaitFor(Agent)
                # Sleep(2.0)
                StartTime := GetSimulationElapsedTime()
                AllUnlocksBoughtEv.AwaitFor(Agent)
                # Sleep(2.0)
                EndTime := GetSimulationElapsedTime()
                TimeTook := EndTime - StartTime
                TimeTookString := TimeToString(TimeTook, ?RemoveMilis := false)
                loop:
                    ShowEndTimeTriggersEvents.AwaitFor(option. Agent)
                    # LPrint("ShowEndTimeTriggersEvents")
                    PopupEndTime.G().SetDescriptionText("{Loc.G(Agent, "Whole bank unlocked in")}: {TimeTookString}\n{Loc.G(Agent, "Try even faster and become the king of finance!")}".ToMessage())
                    PopupEndTime.G().SetButtonText("Ok".ToMessage(), 2)
                    PopupEndTime.G().ShowAwaitForButtonOrExitAndHideWithTimout(Agent, PlayerRemoved, 10.0)
            
        # race{
        #     PlayerRemoved.Await()
        #     block{
        #         HouseManager := PlayerClaimedHouseEvent.Await()
        #     }
        # }

    ShowArrowToHouse(House:house_manager, Agent:agent, PlayerRemoved:event())<suspends>:void=
        var MArrow :?i_disposable = false
        race:
            PlayerRemoved.Await()
            loop:
                if(Arrow := MArrow?):
                    Arrow.Dispose()
                    set MArrow = false
                if(IsHouseFree[House]):
                    set MArrow = ArrowSystem.G().ShowArrow(Agent, House.ClaimTriggerInitTr.Translation)
                    House.HouseClaimedEvent.Await()
                else:
                    House.HouseUnclaimedEvent.Await()
                Sleep(0.0)
            HouseClaimedEvent.AwaitFor(Agent)

        if(Arrow := MArrow?):
            Arrow.Dispose()
        
        

    IsHouseFree(House:house_manager)<decides><transacts>:void=
        not House.MOwner?

    OnUnlockableUnlocked(Data:unlocked_unlocked_event_data):void=
        UnlockableUnlockedEvent.Signal(Data)
        Audio.G().Upgrade.Play(Data.Agent)
        UnlockableUnlockedEventAgent.Signal(Data.Agent)
        if(not Data.WasLoaded?):
            # Resources.G().GivePoints(Data.Agent, 10)

    OnHouseClaimedEvent(Agent:agent):void=
        HouseClaimedEvent.Signal(Agent)
        Resources.G().ShowUi(Agent, false)
        spawn. OnHouseClaimedEventAsync(Agent)

    OnHouseClaimedEventAsync(Agent:agent)<suspends>:void=
        Sleep(0.4)
        for(H:HouseManagers):
            H.ReRegisterOwnerReference()

    OnHouseResetEvent(HouseMananger:house_manager):void=
        if(Owner := HouseMananger.MOwner?):
            HouseResetEvent.Signal(Owner)
#gen
#FirstData_tarr_tdata_postfix
#house_unlockable_manager
#string
#UnlockableString
#id-9bb36b7a-7652-4c87-84c6-599d85785f05
(Arr:[]house_unlockable_manager).FirstDataUnlockableString(Check:type{_(:house_unlockable_manager,:string)<decides><transacts>:void}, Data:string)<decides><transacts>:house_unlockable_manager=
	var Ret:?house_unlockable_manager = false
	N := Arr.Length
	var X :int= 0 
	loop:
		if(Item := Arr[X], Check[Item, Data]):
			set Ret = option. Item
			break
			
		set X += 1
		if(X >= N):
			break
			
	if(Item := Ret?):
		Item
	else:
		Fail[]
		Err()
#id-9bb36b7a-7652-4c87-84c6-599d85785f05

#gen
#FirstData_tarr_tdata_postfix
#house_manager
#agent
#HouseAgent
#id-a04ed3f1-c711-4623-a9b6-618e4ea43704
(Arr:[]house_manager).FirstDataHouseAgent(Check:type{_(:house_manager,:agent)<decides><transacts>:void}, Data:agent)<decides><transacts>:house_manager=
	var Ret:?house_manager = false
	N := Arr.Length
	var X :int= 0 
	loop:
		if(Item := Arr[X], Check[Item, Data]):
			set Ret = option. Item
			break
			
		set X += 1
		if(X >= N):
			break
			
	if(Item := Ret?):
		Item
	else:
		Fail[]
		Err()
#id-a04ed3f1-c711-4623-a9b6-618e4ea43704