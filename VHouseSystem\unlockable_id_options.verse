using. VGiga

unlockable_id_options<public> := class<concrete>:
	Name<public>:string= ""
	var GoldCost<public>:int = 0
	FansCost<public>:int = 0

	GoldGainPerSec<public>:int = 0
	FansGainPerSec<public>:int = 0
	ShowAfterUnlocking<public>:[]string = array{}
	ShowArrow<public>:logic = true
	UseForCounter<public>:logic = false

	# EventDataBeforeUnlocking<public>:[]string = array{}
	# values: spawner_upgrade
	EventDataAfterUnlocking<public>:[]string = array{}
	AdditionalData<public>:[]unlockable_additional_data = array{}

unlockable_ids_options<public> := class<computes>(class_interface):
	Map<public>:[string]unlockable_id_options
	
	Get<public>(Preset:string)<decides><transacts>:unlockable_id_options=
		Map[Preset]
unlockable_additional_data<public> := enum:
	HideOnly
	ShowUpdatePopup
	ExternalResources


# CompareUnlocksByGold<public>(UnlockA:unlockable_id_options, UnlockB:unlockable_id_options)<transacts>:int=
# 	A := UnlockA.GoldCost
# 	B := UnlockB.GoldCost
# 	if(A < B) then -1
# 	else if(A > B) then 1
# 	else 0