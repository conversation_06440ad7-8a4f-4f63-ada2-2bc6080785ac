# using {/Verse.org/Simulation}
# using {/Verse.org/Simulation/Tags}
# using {/Verse.org/Assets}
# using {/Verse.org/Verse}
# using {/Verse.org/Random}
# using {/Verse.org/Colors}
# using {/Verse.org/Colors/NamedColors}
# using {/Verse.org/Native}
# using {/Verse.org/Concurrency}
# using {/UnrealEngine.com/Temporary}
# using {/UnrealEngine.com/Temporary/UI}
# using {/UnrealEngine.com/Temporary/SpatialMath}
# using {/UnrealEngine.com/Temporary/Diagnostics}
# using {/UnrealEngine.com/Temporary/Curves}
# using {/Fortnite.com/UI}
# using {/Fortnite.com/Devices}
# using {/Fortnite.com/Devices/CreativeAnimation}
# using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
# using {/Fortnite.com/Vehicles}
# using {/Fortnite.com/Teams}
# using {/Fortnite.com/Playspaces}
# using {/Fortnite.com/Game}
# using {/Fortnite.com/FortPlayerUtilities}
# using {/Fortnite.com/Characters}
# using {/Fortnite.com/AI}
# using { VGiga }
# using{VGoldGranter}



# quest_ui_c():quest_ui={
# 	Text := text_block{
# 		DefaultJustification := text_justification.Center
# 		DefaultTextColor := White
# 	}
# 	Text.SetShadowOffset(option. vector2{X := 1.0, Y := 1.0})
# 	Text.SetShadowOpacity(1.0)
# 	# Widget := text_block{
# 	# 	DefaultText := "Test".ToMessage()
# 	# 	DefaultJustification := text_justification.Center
# 	# 	DefaultTextColor := White
# 	# }
		
# 	Widget := overlay{
# 		Slots := array{
# 			overlay_slot{
# 				HorizontalAlignment := horizontal_alignment.Fill
# 				VerticalAlignment := vertical_alignment.Fill
# 				Widget := color_block{
# 					DefaultColor := NamedColors.Black
# 					DefaultOpacity := 0.5
# 					# DefaultDesiredSize := Vector2(500.0,50.0)
# 				}
# 				# Padding := margin{
# 				# 	Left := 0.0
# 				# 	Top := 0.0
# 				# 	Right := 0.0
# 				# 	Bottom := 0.0
# 				# }
# 			}
# 			overlay_slot{
# 				HorizontalAlignment := horizontal_alignment.Fill
# 				VerticalAlignment := vertical_alignment.Fill
# 				Widget := Text
# 					Padding := margin{
# 						Left := 10.0
# 						Top := 10.0
# 						Right := 10.0
# 						Bottom := 10.0
# 					}
# 			}
# 		}
# 	}
# 	StackSlot := stack_box_slot{
# 		Widget := Widget
# 		HorizontalAlignment := horizontal_alignment.Fill
# 		VerticalAlignment := vertical_alignment.Fill
# 	}
# 	QuestUi := quest_ui{
# 		Widget := Widget
# 		StackSlot := StackSlot
# 		Text := Text
# 	}
# }
	
# quest_ui := class(){
# 	Text:text_block
# 	Widget:widget
# 	StackSlot:stack_box_slot

# 	UpdateText(ActiveQuest:active_quest_data):void={
# 		Text.SetText("{ActiveQuest.Data.Name} {ActiveQuest.Progress}/{ActiveQuest.Data.MaxProgress}  +{ActiveQuest.Data.GoldReward}💸".ToMessage())
# 	}
# }
# # p_data_my_system := class{
# # }
# # quest_type := enum{
# # 	KillZombies
# # 	AwaitEvent
# # }

# # p_data_map_quest_system := class{
# # 	var DataMap : [agent]p_data_quest_system = map{}

# # 	Get<public>(Player:agent)<decides><transacts>:p_data_quest_system={
# # 		if(Data := DataMap[Player]){
# # 			Data
# # 		}else{
# # 			FailError[]
# # 			Err()
# # 		}
# # 	}

# # 	Set<public>(Player:agent, Data:p_data_quest_system):void={
# # 		if. set DataMap[Player] = Data
# # 	}

# # 	Remove<public>(Player:agent):void={
# # 		set DataMap = DataMap.WithRemoved(Player)
# # 	}
# # }