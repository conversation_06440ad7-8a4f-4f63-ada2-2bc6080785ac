using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
using. VGiga.Pool

cinematic_step := class<concrete>():
	@editable Title:string = ""
	@editable MStartAnalytic:?analytics_device = false
	@editable MEndAnalytic:?analytics_device = false
	@editable MCinematicPool:?cinematic_sequence_device_pool_editable = false
	@editable CinematicPlayRate:float = 1.0
	@editable CinematicPlaybackTime:float = 0.0
	@editable WaitForCinematicEnd:logic = true
	@editable MWaitForInputClick:?input_trigger_device = false
	@editable MWaitForVolumeEnter:?volume_device = false
	@editable MWaitForTimeout:?float = false
	@editable MTeleporterForPlayer:?teleporter_device = false
	@editable MHudMessage:?hud_message_device = false
	@editable MHudMessageTextToTranslate:?string = false
	@editable MChannelToTransmit:?channel_device = false
	@editable MHidePlayer:?logic = false
	@editable MUnhidePlayer:?logic = false
	@editable MFreezePlayer:?logic = false
	@editable MUnfreezePlayer:?logic = false
	@editable MDelayStart:?float = false
	@editable MNextCinematicPlayer:?cinematic_player = false
	