using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga
using. VPopup
using. VResourcesSystem

quiz_datas := class:
	CybersecurityDatas :[]quiz_data = array:
		quiz_data:
			CorrectAnswerId := 0
			Question := "In which year was Bank Pekao S.A. founded?"
			Answer1 := "1929"
			Answer2 := "1949"
			Answer3 := "1999"
		quiz_data:
			CorrectAnswerId := 0
			Question := "What animal is in the logo of Bank Pekao S.A.?"
			Answer1 := "Bison"
			Answer2 := "Eagle"
			Answer3 := "Wolf"
		quiz_data:
			CorrectAnswerId := 2
			Question := "Where is the headquarters of Bank Pekao S.A. located?"
			Answer1 := "Kraków"
			Answer2 := "Gdańsk"
			Answer3 := "Warsaw"
		quiz_data:
			CorrectAnswerId := 0
			Question := "What is the name of the mobile application of Bank Pekao S.A.?"
			Answer1 := "PeoPay"
			Answer2 := "PekaoGo"
			Answer3 := "Pekao24"
		quiz_data:
			CorrectAnswerId := 1
			Question := "Which of these products is offered by Bank Pekao S.A.?"
			Answer1 := "Telecommunication services"
			Answer2 := "Mortgage loans"
			Answer3 := "Car sales"
		quiz_data:
			CorrectAnswerId := 2
			Question := "What is the name of the online banking service of Bank Pekao S.A.?"
			Answer1 := "PekaoNet"
			Answer2 := "ePekao"
			Answer3 := "Pekao24"
		quiz_data:
			CorrectAnswerId := 0
			Question := "What is the customer service hotline number of Bank Pekao S.A.?"
			Answer1 := "519 222 222"
			Answer2 := "800 123 456"
			Answer3 := "700 555 555"
		quiz_data:
			CorrectAnswerId := 1
			Question := "What is the name of the online currency exchange service of Bank Pekao S.A.?"
			Answer1 := "eKantor"
			Answer2 := "Kantor Pekao24"
			Answer3 := "PekaoExchange"
		quiz_data:
			CorrectAnswerId := 2
			Question := "What can you invest in at Bank Pekao S.A.?"
			Answer1 := "Cryptocurrencies"
			Answer2 := "Physical gold"
			Answer3 := "Investment funds"
		quiz_data:
			CorrectAnswerId := 0
			Question := "What is the name of the private banking service of Bank Pekao S.A.?"
			Answer1 := "Private Banking"
			Answer2 := "Pekao Elite"
			Answer3 := "Premium Banking"
		


		
	CybersecurityDatasPl :[]quiz_data = array:	
		quiz_data:
			CorrectAnswerId := 0
			Question := "W którym roku założono Bank Pekao S.A.?"
			Answer1 := "1929"
			Answer2 := "1949"
			Answer3 := "1999"
		quiz_data:
			CorrectAnswerId := 0
			Question := "Jakie zwierzę znajduje się w logo Banku Pekao S.A.?"
			Answer1 := "Żubr"
			Answer2 := "Orzeł"
			Answer3 := "Wilk"
		quiz_data:
			CorrectAnswerId := 2
			Question := "Gdzie znajduje się siedziba główna Banku Pekao S.A.?"
			Answer1 := "Kraków"
			Answer2 := "Gdańsk"
			Answer3 := "Warszawa"
		quiz_data:
			CorrectAnswerId := 0
			Question := "Jak nazywa się aplikacja mobilna Banku Pekao S.A.?"
			Answer1 := "PeoPay"
			Answer2 := "PekaoGo"
			Answer3 := "Pekao24"
		quiz_data:
			CorrectAnswerId := 1
			Question := "Który z tych produktów oferuje Bank Pekao S.A.?"
			Answer1 := "Usługi telekomunikacyjne"
			Answer2 := "Kredyty hipoteczne"
			Answer3 := "Sprzedaż samochodów"
		quiz_data:
			CorrectAnswerId := 2
			Question := "Jak nazywa się usługa bankowości internetowej Banku Pekao S.A.?"
			Answer1 := "PekaoNet"
			Answer2 := "ePekao"
			Answer3 := "Pekao24"
		quiz_data:
			CorrectAnswerId := 0
			Question := "Jaki jest numer infolinii Banku Pekao S.A.?"
			Answer1 := "519 222 222"
			Answer2 := "800 123 456"
			Answer3 := "700 555 555"
		quiz_data:
			CorrectAnswerId := 1
			Question := "Jak nazywa się usługa wymiany walut online Banku Pekao S.A.?"
			Answer1 := "eKantor"
			Answer2 := "Kantor Pekao24"
			Answer3 := "PekaoExchange"
		quiz_data:
			CorrectAnswerId := 2
			Question := "W co można inwestować w Banku Pekao S.A.?"
			Answer1 := "Kryptowaluty"
			Answer2 := "Złoto fizyczne"
			Answer3 := "Fundusze inwestycyjne"
		quiz_data:
			CorrectAnswerId := 0
			Question := "Jak nazywa się usługa bankowości prywatnej w Banku Pekao S.A.?"
			Answer1 := "Private Banking"
			Answer2 := "Pekao Elite"
			Answer3 := "Bankowość Premium"



	MathDatas :[]quiz_data = array:
		quiz_data:
			CorrectAnswerId := 0
			Question := "What is the name of the main character in 'The Legend of Zelda' series?"
			Answer1 := "Link"
			Answer2 := "Zelda"
			Answer3 := "Ganon"
		quiz_data:
			CorrectAnswerId := 2
			Question := "Which studio created the famous racing game series 'DiRT'?"
			Answer1 := "EA"
			Answer2 := "Rockstar"
			Answer3 := "Codemasters"
		quiz_data:
			CorrectAnswerId := 1
			Question := "How many main 'Assassin's Creed' games are there? (excluding spin-offs like 'Chronicles')"
			Answer1 := "10"
			Answer2 := "13"
			Answer3 := "7"
		quiz_data:
			CorrectAnswerId := 2
			Question := "What does 'GTA' stand for?"
			Answer1 := "Grand Terrory Army"
			Answer2 := "Green Thief Android"
			Answer3 := "Grand Theft Auto"
		quiz_data:
			CorrectAnswerId := 0
			Question := "What is the in-game currency in Fortnite?"
			Answer1 := "V-Bucks"
			Answer2 := "Robux"
			Answer3 := "Coins"
		quiz_data:
			CorrectAnswerId := 2
			Question := "Which game originally featured the famous 'Konami Code'?"
			Answer1 := "Castlevania"
			Answer2 := "Contra"
			Answer3 := "Gradius"
		quiz_data:
			CorrectAnswerId := 1
			Question := "Who is the main creator of the 'Super Mario Bros' series?"
			Answer1 := "Tsunekazu Ishihara"
			Answer2 := "Shigeru Miyamoto"
			Answer3 := "Hideo Kojima"
		quiz_data:
			CorrectAnswerId := 1
			Question := "What is the name of the main character in 'The Witcher' series?"
			Answer1 := "Dandelion"
			Answer2 := "Geralt"
			Answer3 := "Vesemir"
		quiz_data:
			CorrectAnswerId := 1
			Question := "What is Aloy's primary weapon in 'Horizon Zero Dawn'?"
			Answer1 := "Sword"
			Answer2 := "Bow"
			Answer3 := "Pistol"
		quiz_data:
			CorrectAnswerId := 2
			Question := "Which material is needed to craft a Nether portal in Minecraft?"
			Answer1 := "Stone"
			Answer2 := "Iron"
			Answer3 := "Obsidian"

	MathDatasPll :[]quiz_data = array:
		quiz_data:
			CorrectAnswerId := 0
			Question := "Jak ma na imię główny bohater serii 'The Legend of Zelda'?"
			Answer1 := "Link"
			Answer2 := "Zelda"
			Answer3 := "Ganon"
		quiz_data:
			CorrectAnswerId := 2
			Question := "Które studio stworzyło słynną serię gier wyścigowych 'DiRT'?"
			Answer1 := "EA"
			Answer2 := "Rockstar"
			Answer3 := "Codemasters"
		quiz_data:
			CorrectAnswerId := 1
			Question := "Ile jest głównych gier z serii 'Assassin's Creed'? (nie licząc spin-offów jak 'Chronicles')"
			Answer1 := "10"
			Answer2 := "13"
			Answer3 := "7"
		quiz_data:
			CorrectAnswerId := 2
			Question := "Co oznacza skrót 'GTA'?"
			Answer1 := "Grand Terrory Army"
			Answer2 := "Green Thief Android"
			Answer3 := "Grand Theft Auto"
		quiz_data:
			CorrectAnswerId := 0
			Question := "Jak nazywa się waluta w Fortnite?"
			Answer1 := "V-Bucks"
			Answer2 := "Robux"
			Answer3 := "Monety"
		quiz_data:
			CorrectAnswerId := 2
			Question := "W której grze po raz pierwszy pojawił się słynny 'Konami Code'?"
			Answer1 := "Castlevania"
			Answer2 := "Contra"
			Answer3 := "Gradius"
		quiz_data:
			CorrectAnswerId := 1
			Question := "Kto jest głównym twórcą serii 'Super Mario Bros'?"
			Answer1 := "Tsunekazu Ishihara"
			Answer2 := "Shigeru Miyamoto"
			Answer3 := "Hideo Kojima"
		quiz_data:
			CorrectAnswerId := 1
			Question := "Jak ma na imię główny bohater serii 'Wiedźmin'?"
			Answer1 := "Jaskier"
			Answer2 := "Geralt"
			Answer3 := "Vesemir"
		quiz_data:
			CorrectAnswerId := 1
			Question := "Jaka jest główna broń Aloy - bohaterki gry 'Horizon Zero Dawn'?"
			Answer1 := "Miecz"
			Answer2 := "Łuk"
			Answer3 := "Pistolet"
		quiz_data:
			CorrectAnswerId := 2
			Question := "Z jakiego materiału trzeba zbudować portal do Netheru w Minecraft?"
			Answer1 := "Kamień"
			Answer2 := "Żelazo"
			Answer3 := "Obsydian"

	
	BankingDatas :[]quiz_data = array:
		quiz_data:
			CorrectAnswerId := 0
			Question := "Where do you keep money safely?"
			Answer1 := "Bank"
			Answer2 := "Backpack"
			Answer3 := "Under bed"
		quiz_data:
			CorrectAnswerId := 0
			Question := "What do banks give to save money?"
			Answer1 := "Savings account"
			Answer2 := "Toy box"
			Answer3 := "Wallet"
		quiz_data:
			CorrectAnswerId := 2
			Question := "What is used to pay at stores?"
			Answer1 := "Gift"
			Answer2 := "Receipt"
			Answer3 := "Credit Card"
		quiz_data:
			CorrectAnswerId := 0
			Question := "What do banks give for keeping your money?"
			Answer1 := "Interest"
			Answer2 := "Taxes"
			Answer3 := "Bills"
		quiz_data:
			CorrectAnswerId := 1
			Question := "Who helps you at the bank?"
			Answer1 := "Chef"
			Answer2 := "Teller"
			Answer3 := "Driver"
		quiz_data:
			CorrectAnswerId := 2
			Question := "What do you use to take money out?"
			Answer1 := "Receipt"
			Answer2 := "Coupon"
			Answer3 := "ATM"
		quiz_data:
			CorrectAnswerId := 0
			Question := "What do banks use to keep money safe?"
			Answer1 := "Vault"
			Answer2 := "Bag"
			Answer3 := "Box"
		quiz_data:
			CorrectAnswerId := 1
			Question := "What is money you borrow called?"
			Answer1 := "Savings"
			Answer2 := "Loan"
			Answer3 := "Deposit"
		quiz_data:
			CorrectAnswerId := 2
			Question := "What do you need to open an account?"
			Answer1 := "Receipt"
			Answer2 := "Card"
			Answer3 := "ID"
		quiz_data:
			CorrectAnswerId := 0
			Question := "What helps track your spending?"
			Answer1 := "Bank statement"
			Answer2 := "Receipt"
			Answer3 := "Coin jar"
		quiz_data:
			CorrectAnswerId := 1
			Question := "What do you pay when borrowing money?"
			Answer1 := "Tax"
			Answer2 := "Interest"
			Answer3 := "Deposit"
		quiz_data:
			CorrectAnswerId := 0
			Question := "Who owns the money in a bank account?"
			Answer1 := "Account holder"
			Answer2 := "Bank teller"
			Answer3 := "Neighbor"
		quiz_data:
			CorrectAnswerId := 1
			Question := "What do you use to pay without cash?"
			Answer1 := "Note"
			Answer2 := "Debit card"
			Answer3 := "Receipt"
		quiz_data:
			CorrectAnswerId := 2
			Question := "What is saving money for the future called?"
			Answer1 := "Spending"
			Answer2 := "Lending"
			Answer3 := "Saving"

	ChristmasDatas :[]quiz_data = array:
		quiz_data:
			CorrectAnswerId := 0
			Question := "What principle is worth applying when saving?"
			Answer1 := "Save regularly"
			Answer2 := "Spend on everything"
			Answer3 := "Don't think about saving"
		
		quiz_data:
			CorrectAnswerId := 1
			Question := "What is the best account for saving?"
			Answer1 := "Personal account"
			Answer2 := "Savings account"
			Answer3 := "Current account"
		
		quiz_data:
			CorrectAnswerId := 0
			Question := "What helps with effective saving?"
			Answer1 := "Household budget"
			Answer2 := "Lack of plan"
			Answer3 := "Daily shopping"
		
		quiz_data:
			CorrectAnswerId := 0
			Question := "What should you limit to save money?"
			Answer1 := "Subscriptions"
			Answer2 := "Food expenses"
			Answer3 := "Clothing shopping"
		
		quiz_data:
			CorrectAnswerId := 2
			Question := "Where is the best place to keep savings?"
			Answer1 := "Cash at home"
			Answer2 := "Savings account"
			Answer3 := "Fixed-term deposit"
		
		quiz_data:
			CorrectAnswerId := 1
			Question := "Why should you have an emergency fund?"
			Answer1 := "For unexpected expenses"
			Answer2 := "For shopping"
			Answer3 := "For vacation"
		
		quiz_data:
			CorrectAnswerId := 1
			Question := "What should you do before starting to save?"
			Answer1 := "Invest"
			Answer2 := "Pay off debts"
			Answer3 := "Open a bank account"
		
		quiz_data:
			CorrectAnswerId := 2
			Question := "What helps to grow savings?"
			Answer1 := "Consumer credit"
			Answer2 := "Investments"
			Answer3 := "Luxury expenses"
		
		quiz_data:
			CorrectAnswerId := 0
			Question := "How can you lower your electricity bills?"
			Answer1 := "Turn off devices"
			Answer2 := "Change provider"
			Answer3 := "Buy new devices"
		
		quiz_data:
			CorrectAnswerId := 1
			Question := "What’s better for large purchases?"
			Answer1 := "Loan"
			Answer2 := "Installment purchase"
			Answer3 := "Spend savings"


			
	BankingDatasPl :[]quiz_data = array:
		quiz_data:
			CorrectAnswerId := 0
			Question := "Gdzie bezpiecznie przechowywać pieniądze?"
			Answer1 := "Bank"
			Answer2 := "Plecak"
			Answer3 := "Pod łóżkiem"
		quiz_data:
			CorrectAnswerId := 1
			Question := "Jaki produkt w bank służy do oszczędzania?"
			Answer1 := "Pudełko"
			Answer2 := "Konto oszczędnościowe"
			Answer3 := "Portfel"
		quiz_data:
			CorrectAnswerId := 2
			Question := "Czego używasz, aby płacić w sklepie?"
			Answer1 := "Prezent"
			Answer2 := "Paragon"
			Answer3 := "Pieniądze"
		quiz_data:
			CorrectAnswerId := 0
			Question := "Co bank nalicza za trzymanie oszczędności?"
			Answer1 := "Odsetki"
			Answer2 := "Podatki"
			Answer3 := "Rachunki"
		quiz_data:
			CorrectAnswerId := 1
			Question := "Kto obsługuje klienta w banku?"
			Answer1 := "Kucharz"
			Answer2 := "Doradca"
			Answer3 := "Kierowca"
		quiz_data:
			CorrectAnswerId := 2
			Question := "Czego używasz, aby wypłacić pieniądze?"
			Answer1 := "Paragon"
			Answer2 := "Kupon"
			Answer3 := "Bankomat"
		quiz_data:
			CorrectAnswerId := 2
			Question := "Gdzie podejrzysz swoje produkty w banku?"
			Answer1 := "Na poczcie"
			Answer2 := "W zeszycie"
			Answer3 := "W aplikacji bankowej"
		quiz_data:
			CorrectAnswerId := 1
			Question := "Jeżeli chcesz pożyczyć od banku pieniądze to potrzebujesz...?"
			Answer1 := "Lokaty"
			Answer2 := "Kredytu"
			Answer3 := "Konta"
		quiz_data:
			CorrectAnswerId := 2
			Question := "Co jest potrzebne do otwarcia konta?"
			Answer1 := "Paragon"
			Answer2 := "Karta"
			Answer3 := "Dowód osobisty"
		quiz_data:
			CorrectAnswerId := 0
			Question := "Gdzie możesz sprawdzić stan swojego konta?"
			Answer1 := "W aplikacji bankowej"
			Answer2 := "Na paragonie"
			Answer3 := "W skarbonce"

	ChristmasDatasPl :[]quiz_data = array:
		quiz_data:
			CorrectAnswerId := 0
			Question := "Jaką zasadę warto stosować przy oszczędzaniu?"
			Answer1 := "Oszczędzaj regularnie"
			Answer2 := "Wydawaj na wszystko"
			Answer3 := "Nie myśl o oszczędnościach"
		
		quiz_data:
			CorrectAnswerId := 1
			Question := "Jakie konto jest najlepsze do oszczędzania?"
			Answer1 := "Osobiste"
			Answer2 := "Oszczędnościowe"
			Answer3 := "Rachunek bieżący"
		
		quiz_data:
			CorrectAnswerId := 1
			Question := "Co pomaga w skutecznym oszczędzaniu?"
			Answer1 := "Budżet domowy"
			Answer2 := "Brak planu"
			Answer3 := "Codzienne zakupy"
		
		quiz_data:
			CorrectAnswerId := 0
			Question := "Co warto ograniczyć, aby oszczędzać?"
			Answer1 := "Subskrypcje"
			Answer2 := "Wydatki na jedzenie"
			Answer3 := "Zakupy odzieżowe"
		
		quiz_data:
			CorrectAnswerId := 2
			Question := "Gdzie najlepiej trzymać oszczędności?"
			Answer1 := "Gotówka w domu"
			Answer2 := "Konto oszczędnościowe"
			Answer3 := "Lokata terminowa"
		
		quiz_data:
			CorrectAnswerId := 0
			Question := "Po co warto mieć fundusz awaryjny?"
			Answer1 := "Na nagłe wydatki"
			Answer2 := "Na zakupy"
			Answer3 := "Na wakacje"
		
		quiz_data:
			CorrectAnswerId := 1
			Question := "Co warto zrobić przed rozpoczęciem oszczędzania?"
			Answer1 := "Zainwestować"
			Answer2 := "Spłacić długi"
			Answer3 := "Otworzyć konto bankowe"

		quiz_data:
			CorrectAnswerId := 1
			Question := "Co pomaga w pomnażaniu oszczędności?"
			Answer1 := "Kredyt konsumpcyjny"
			Answer2 := "Inwestycje"
			Answer3 := "Wydatki na luksusy"

		
		quiz_data:
			CorrectAnswerId := 0
			Question := "Jak obniżyć rachunki za prąd?"
			Answer1 := "Wyłączaj urządzenia"
			Answer2 := "Zmień dostawcę"
			Answer3 := "Kup nowe urządzenia"

		quiz_data:
			CorrectAnswerId := 1
			Question := "Co jest lepsze przy dużych zakupach?"
			Answer1 := "Pożyczka"
			Answer2 := "Zakup na raty"
			Answer3 := "Wydanie oszczędności"
