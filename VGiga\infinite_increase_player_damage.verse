using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 

infinite_increase_player_damage<public> := class(increase_player_damage){}

