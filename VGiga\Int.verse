(Intt:int).PadLeftWithLeadingZeros<public>(DesiredNumOfIntegerDigist:int):string=
	var Ret :string= "{Intt}"
	var NumOfIntegerDigits:int = Ret.Length
	ZerosToAdd := DesiredNumOfIntegerDigist - NumOfIntegerDigits
	
	for(X:=0..ZerosToAdd-1):
		set Ret = "0{Ret}"

	Ret

(Intt:int).TrimLeft<public>(DesiredNumOfDigits:int):int=
	Modulo :int= Pow(10.0, DesiredNumOfDigits * 1.0).ToInt[] or block:
		LError()
		100
	if(Trimmed := Mod[Intt, Modulo]):
		Trimmed
	else:
		Intt

(Intt:int).IntToFloat<public>()<computes>:float=
    Intt * 1.0
