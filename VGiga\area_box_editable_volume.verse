using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI 

area_box_editable_volume<public> := class<concrete>(area_box_editable, i_init_per_player_async):
	@editable CheckRate:float = 1.0

	var MParentArea:?area_box_editable_volume = false

	PlayerEnteredEvent<public>:event(agent) = event(agent){}
	PlayerExitedEvent<public>:event(agent) = event(agent){}

	Init<public>(Container:player_events_manager_devic):void=
		Container.Register(Self)
		Init()

	Init<public>(Container:player_events_manager_devic, ParentArea:area_box_editable_volume):void=
		set MParentArea = option. ParentArea
		Init(Container)

	InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
		Print("area_box_editable_volume InitPlayerAsync"); Sleep(0.0)
		var IsAgentInside:logic = false
		# LPrint("InitPlayerAsync volume")

		if(ParentArea := MParentArea?):
			loop:
				ParentArea.PlayerEnteredEvent.Await()
				race:
					ParentArea.PlayerExitedEvent.Await()
					loop:
						Sleep(CheckRate)
						if(Pos := Agent.GetFortCharacterActive[].GetTransform().Translation
						):
							if(IsAgentInside?, not IsInside[Pos]):
								set IsAgentInside = false
								PlayerExitedEvent.Signal(Agent)
							else if(not IsAgentInside?, IsInside[Pos]):
								set IsAgentInside = true
								PlayerEnteredEvent.Signal(Agent)
				if(IsAgentInside?):
					set IsAgentInside = false
					PlayerExitedEvent.Signal(Agent)

		else:
			loop:
				Sleep(CheckRate)
				if(Pos := Agent.GetFortCharacterActive[].GetTransform().Translation
				):
					if(IsAgentInside?, not IsInside[Pos]):
						set IsAgentInside = false
						PlayerExitedEvent.Signal(Agent)
					else if(not IsAgentInside?, IsInside[Pos]):
						set IsAgentInside = true
						PlayerEnteredEvent.Signal(Agent)

		
		# set Area = area_box_c(Prop)
	# StartOnEnterDetection(Float:rate):void=

	