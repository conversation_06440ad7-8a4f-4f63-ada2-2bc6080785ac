#gen
#pool_component
#prop_manipulator_device
#id-539bc92a-ae4d-4031-9ca0-9bf4f8bc3cea
using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using.  VGiga


		
prop_manipulator_device_pool_component_c<public>(Area:area_interface, D:creative_device)<transacts>:prop_manipulator_device_pool_component=
	prop_manipulator_device_pool_component:
		Area := Area
		D := D
	.Init()

prop_manipulator_device_pool_component<public> := class:
	Area<public>:area_interface
	var FreeCreativeDevices : []prop_manipulator_device = array{}
	D<public>:creative_device

	Init()<transacts>:prop_manipulator_device_pool_component=
		set FreeCreativeDevices = for(Obj : D.GetDevicesInArea(prop_manipulator_device, Area)):
			Obj
		Self

	GetAllFree<public>():[]prop_manipulator_device=
		FreeCreativeDevices

	Rent<public>()<decides><transacts>:prop_manipulator_device=
		if(CreativeDevice := FreeCreativeDevices[0]
			Val := FreeCreativeDevices.RemoveElement[0]
			set FreeCreativeDevices = Val
		):
			return CreativeDevice
		else:
			FailError[]
			return prop_manipulator_device{}

	RentDisposable<public>()<decides><transacts>:pooled_editable_prop_manipulator_device=
		Device := Rent[]
		pooled_editable_prop_manipulator_device:
			Device := Device
			MyPool := Self

	Return<public>(CreativeDevice:prop_manipulator_device):void=
		if. CreativeDevice.TeleportTo[Area.GetCenterPoint(), rotation{}]
		set FreeCreativeDevices += array. CreativeDevice


pooled_editable_prop_manipulator_device<public> := class(i_disposable):
	MyPool<public>:prop_manipulator_device_pool_component
	Device<public>:prop_manipulator_device

	Dispose<override>():void=
		MyPool.Return(Device)

	
#id-539bc92a-ae4d-4031-9ca0-9bf4f8bc3cea