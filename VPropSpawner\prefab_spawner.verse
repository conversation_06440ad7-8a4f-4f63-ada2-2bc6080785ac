using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga
using. VResourcesSystem

prefab_spawner <public> := class(auto_creative_device, i_init_async):
	@editable MLocation:?creative_prop = false
	@editable MPrefabDefinition:?prefab_definition_devic = false
	@editable InfiniteSpawn:logic = true
	@editable RespawnTime:float = 4.0

	DestroyedEvent<public>:event()=event(){}
	PropDestroyedEvent<public>:event()=event(){}
		
	InitAsync<override>(Container:vcontainer)<suspends>:void=
		if(PropSpawner := Container.Resolve[prop_spawner]
			ResourceManager := Container.Resolve[resources_manager]
			PrefabDefinition := MPrefabDefinition.VError[]
			Location := MLocation.VError[].GetTransform()
		):
			PrefabDefinition.OnBegin()
			if(InfiniteSpawn?):
				loop:
					PropsManager := PrefabDefinition.Spawn(Location, PropSpawner)
					ResourceManager.StartToDropGoldOnPropDestroy(PropsManager)
					PropDestroyedEv := PropsManager.GetPropDestroyedEv()
					race:
						loop:
							PropDestroyedEv.Await()
							PropDestroyedEvent.Signal()
						PropsManager.DisposedEv.Await() 
					DestroyedEvent.Signal()
					Sleep(RespawnTime)
					# all props destroyed
					
			else:
				PropsManager := PrefabDefinition.Spawn(Location, PropSpawner)
				PropDestroyedEv := PropsManager.GetPropDestroyedEv()
				PropsManager.DisposedEv.Await() 
				DestroyedEvent.Signal()

		
		
		
