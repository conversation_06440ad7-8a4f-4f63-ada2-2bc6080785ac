﻿using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga
using. VResourcesSystem
using. VCoop

time_rewards := class(auto_creative_device, i_init_per_player_async, i_init):
	OpenUiEv:event(agent) = event(agent){}
	var Loc:i_localization = empty_i_localization{}
	var Resources:?resources_manager = false
	var Save :?save_system= false
	var Coop:?coop = false
	
	# RewardsTime:[]int = array:
	# 	60 * 1
	# 	60 * 2
	# 	60 * 3
	# 	60 * 4
	# 	60 * 5
	# 	60 * 6
	RewardsTime:[]int = array:
		60 * 10
		60 * 20
		60 * 30
		60 * 40
		60 * 50
		60 * 60

	RewardsGold:[]int = array:
		10000
		15000
		20000
		25000
		30000
		35000

	RewardsAmount :int= 6

	Init<override>(Container:vcontainer):void=
		# LPrint("Init")
		set Save = Container.ResolveOp[save_system] or Err()
		set Loc = Container.Resolve_i_localization()
		set Resources = Container.ResolveOp[resources_manager] or Err()
		set Coop = Container.ResolveOp[coop] or Err()

		OpenVolumes := tag_open_rewards_trigger{}.GetAll(Self)
		# LPrint("OpenVolumes {OpenVolumes.Length}")
		
		for(Volume:OpenVolumes):
			Volume.TriggeredEvent.Subscribe(OnOpenVolume)

	OnOpenVolume(Agent:?agent):void=
		# LPrint("OPEN rewards")
		if(Ag := Agent?):
			OpenUiEv.Signal(Ag)

	ClaimArrChar :arr_char= arr_char:
		Arr := "Claim!"
	ClaimedArrChar :arr_char= arr_char:
		Arr := "CLAIMED"
	TimeRewardArrChar :arr_char= arr_char:
		Arr := "Time Reward"
	ReadyArrChar :arr_char= arr_char:
		Arr := "Ready!"
	YellowColor :color= MakeColorFromHex("EBFA6E")
# 
	InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
		# Skip for non-admin players in coop mode
		RestrictedForAdminOnly :logic= Coop.G().CoopAdmin? <> Agent and true or false
		
		Print("time_rewards InitPlayerAsync"); Sleep(0.0)
		var NextRewardTime :int= RewardsTime[0] or Err()
		var Playtime:int=0
		var CurRewardId :int= 0
		
		# NextRewardUnlockedEv:event(int) = event(int){}
		
		var UnlockedButNotClaimedRewardsIds:[]int = array{}
		var UnlockedButNotClaimedRewardsIdsLength:int = 0
		
		TimerUi := make_time_rewards_timer_generated()
		ClaimUi := make_time_rewards_claim_generated()
		Agent.AddToPlayerUi(TimerUi.TimeRewardsTimer)

		Timers:[]text_block = array:
			ClaimUi.Timer1
			ClaimUi.Timer2
			ClaimUi.Timer3
			ClaimUi.Timer4
			ClaimUi.Timer5
			ClaimUi.Timer6
		for(Timer:Timers):
			Timer.SetText("...".ToMessage())
		
		VSave := Save.G()
		Player := Agent.WaitForPlayerActive()
		race:
			PlayerRemoved.Await()
			loop:
				if(RestrictedForAdminOnly?):
					Sleep(Inf)
				if(UnlockedButNotClaimedRewardsIdsLength = 0):
					TimerUi.TimerText.SetText(ToMsg((NextRewardTime - Playtime).TimeToStringMinsAndSeconds()))
				set Playtime += 1
				VSave.AddPlaytime(Player, 1)
				
				if(NextRewardTime < Playtime):
					# NextRewardUnlockedEv.Signal(CurRewardId)
					set UnlockedButNotClaimedRewardsIds += array. CurRewardId
					set UnlockedButNotClaimedRewardsIdsLength += 1
					TimerUi.TimerText.SetText(Loc.GMsg(Agent, ClaimArrChar))
					
					set CurRewardId += 1
					if(NextTime := RewardsTime[CurRewardId]):
						set NextRewardTime = NextTime
					else:
						# Logic over we just increase playtime
						loop:
							set Playtime += 10
							VSave.AddPlaytime(Player, 10)
							Sleep(10.0)
				Sleep(1.0)
			loop:
				if(RestrictedForAdminOnly?):
					Sleep(Inf)
				OpenUiEv.AwaitFor(Agent)
				
				Agent.AddToPlayerUi(ClaimUi.TimeRewardsClaim, true, 3)
				
				for(UnlockedId:UnlockedButNotClaimedRewardsIds):
					if(Timer := Timers[UnlockedId]):
						Timer.SetText(Loc.GMsg(Agent, ReadyArrChar))

				race:
					Sleep(9.0)
					ClaimUi.ButtonClaimPlay.OnClick().Await()
					loop: 
						for(X:= 0..5):
							if(RewardTime := RewardsTime[X]
								Timer := Timers[X]
							):
								if(Playtime < RewardTime):
									Timer.SetText(ToMsg((RewardTime - Playtime).TimeToStringMinsAndSeconds()))
								else if (Playtime = RewardTime):
									Timer.SetText(Loc.GMsg(Agent, ReadyArrChar))
						Sleep(1.0)


				for(UnlockedId:UnlockedButNotClaimedRewardsIds):
					if(Timer := Timers[UnlockedId]):
						Timer.SetText(Loc.GMsg(Agent, ClaimedArrChar))
						Timer.SetTextColor(YellowColor)
					else:
						LError()
					if(GoldReward := RewardsGold[UnlockedId]):
						Resources.G().GiveGoldText(Agent, GoldReward, "Time Reward", ?ShowNotification := true)
					else:
						LError()
				if(UnlockedButNotClaimedRewardsIds.Find[RewardsAmount - 1]):
					Agent.RemoveFromPlayerUi(TimerUi.TimeRewardsTimer)

				set UnlockedButNotClaimedRewardsIds = array{}
				set UnlockedButNotClaimedRewardsIdsLength = 0
				
				Sleep(1.0)
				Agent.RemoveFromPlayerUi(ClaimUi.TimeRewardsClaim)
			loop:
				if(not RestrictedForAdminOnly?):
					Sleep(Inf)
				OpenUiEv.AwaitFor(Agent)
				Resources.G().RedHudMessageTop.Show(Agent, "{Loc.G(Agent, "Only admin can claim rewards!")}".ToMessage())


				


		
		Agent.RemoveFromPlayerUi(TimerUi.TimeRewardsTimer)
		Agent.RemoveFromPlayerUi(ClaimUi.TimeRewardsClaim)
		
					
				
