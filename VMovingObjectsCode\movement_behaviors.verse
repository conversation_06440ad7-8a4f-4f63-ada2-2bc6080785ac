	# This file stores functions common to animating Creative props using keyframes.

	# It also defines the move_to_ease_type enum to help in building animations.

	 

	using { /Fortnite.com/Devices }

	using { /UnrealEngine.com/Temporary/SpatialMath }

	using { /Fortnite.com/Characters}

	using { /Fortnite.com/Devices/CreativeAnimation }

	 

	# Represents the different movement easing types.

	move_to_ease_type<public> := enum {Linear, Ease, EaseIn, EaseOut, EaseInOut}

	 

	# Return the cubic_bezier_parameters based on the given move_to_ease_type.

	GetCubicBezierForEaseType(EaseType:move_to_ease_type):cubic_bezier_parameters=

	    case (EaseType):

	        move_to_ease_type.Linear => InterpolationTypes.Linear

	        move_to_ease_type.Ease => InterpolationTypes.Ease

	        move_to_ease_type.EaseIn => InterpolationTypes.EaseIn

	        move_to_ease_type.EaseOut => InterpolationTypes.EaseOut

	        move_to_ease_type.EaseInOut => InterpolationTypes.EaseInOut

	 

	# Initializes a vector3 with all values set to 1.0.

	VectorOnes<public>:vector3 = vector3{X:=1.0, Y:=1.0, Z:=1.0}

	 

	# An overload of MoveToEase() that changes the position of the prop while keeping the rotation and scale the same.

	(CreativeProp:creative_prop).MoveToEase<public>(Position:vector3, Duration:float, EaseType:move_to_ease_type, AnimationMode:animation_mode)<suspends>:void=

	    CreativeProp.MoveToEase(Position, IdentityRotation(), VectorOnes, Duration, EaseType, AnimationMode)

	 

	# An overload of MoveToEase() that changes the rotation of the prop while keeping the position and scale the same.

	(CreativeProp:creative_prop).MoveToEase<public>(Rotation:rotation, Duration:float, EaseType:move_to_ease_type, AnimationMode:animation_mode)<suspends>:void=

	    CreativeProp.MoveToEase(CreativeProp.GetTransform().Translation, Rotation, VectorOnes, Duration, EaseType, AnimationMode)

	 

	# An overload of MoveToEase() that changes the position and scale of the prop while keeping the rotation the same.

	(CreativeProp:creative_prop).MoveToEase<public>(Duration:float, Scale:vector3, EaseType:move_to_ease_type, AnimationMode:animation_mode)<suspends>:void=

	    CreativeProp.MoveToEase(CreativeProp.GetTransform().Translation, IdentityRotation(), Scale, Duration, EaseType, AnimationMode)

	 

	# An overload of MoveToEase() that takes a pre-built array of keyframes and plays an animation.

	(CreativeProp:creative_prop).MoveToEase<public>(Keyframes:[]keyframe_delta, AnimationMode:animation_mode)<suspends>:void=

	    if (AnimController := CreativeProp.GetAnimationController[]):

	        AnimController.SetAnimation(Keyframes, ?Mode:=AnimationMode)

	        AnimController.Play()

	        AnimController.MovementCompleteEvent.Await()

	 

	# Animate a creative_prop by constructing an animation from a single keyframe, and then playing that animation on the prop.

	# This method takes a Position, Rotation, and Scale for the prop to end at, the duration of the animation,

	# the type of easing to apply to the movement, and the animation mode of the animation.

	(CreativeProp:creative_prop).MoveToEase<public>(Position:vector3, Rotation:rotation, Scale:vector3, Duration:float, EaseType:move_to_ease_type, AnimationMode:animation_mode)<suspends>:void=

	 

	    # Get the animation controller for the CreativeProp to move.

	    if (AnimController := CreativeProp.GetAnimationController[]):

	 

	        # Calculate the multiplicative scale for the keyframe to scale to.

	        ScaleMultiplicative:vector3 = VectorOnes + ((Scale - CreativeProp.GetTransform().Scale) / CreativeProp.GetTransform().Scale)

	 

	        # Build the keyframe array from a single keyframe_delta of the given values.

	        Keyframes:[]keyframe_delta = array:

	            keyframe_delta:

	                DeltaLocation := Position - CreativeProp.GetTransform().Translation,

	                DeltaRotation := Rotation,

	                DeltaScale := ScaleMultiplicative,

	                Time := Duration,

	                Interpolation := GetCubicBezierForEaseType(EaseType)

	 

	        # Set the animation on the animation controller, play it, and await the animation ending.

	        AnimController.SetAnimation(Keyframes, ?Mode:=AnimationMode)

	        AnimController.Play()

	        AnimController.MovementCompleteEvent.Await()

	 

	# Builds an array of keyframes that animate movement and rotation from the OriginalTransform to the TargetTransform.

	BuildMovingAnimationKeyframes(MoveDuration:float, RotationRate:float, AdditionalRotation:rotation, OriginalTransform:transform, TargetTransform:transform,MoveEaseType:move_to_ease_type, UseEasePerKeyframe:logic):[]keyframe_delta=

	 

	    # The array of keyframes to return.

	    var Keyframes:[]keyframe_delta = array{}

	 

	    # The total amount of time spent animating.

	    var TotalTime:float = 0.0

	 

	    # The starting transform for building keyframes. This is the

	    # transform of the RootProp at the start of each keyframe.

	    var StartTransform:transform = OriginalTransform

	 

	    # The ending transform for building keyframes. This is the

	    # transform of the RootProp at the end of each keyframe.

	    var EndTransform:transform = OriginalTransform

	 

	    # The actual rotation to apply to the RootProp. Usually this is the

	    # AdditionalRotation, but will change in cases with fractional rotations.

	    var RotationToApply:rotation = AdditionalRotation

	 

	    # The time it takes for each keyframe of animation to complete.

	    # This is initialized to 1.0 / Rotation rate since the RootProp needs to make a

	    # RotationRate number of rotations per second.

	    var AnimationTime:float = 1.0 / RotationRate

	 

	    # The total number of rotations to make.

	    TotalRotations:float = MoveDuration * RotationRate

	 

	    # The time it takes one rotation to complete.

	    TimePerRotation:float = MoveDuration/TotalRotations

	 

	    # Build each keyframe of animation and add it to the Keyframes array.

	    # The loop breaks when the TotalTime goes past the MoveDuration.

	    loop:

	        # Add the TimePerRotation to the TotalTime.

	        set TotalTime += TimePerRotation

	        if:

	            # If the TotalTime is greater than the MoveDuration, the final keyframe needs

	            # to be shortened. This means making a fraction of a rotation.

	            LeftoverTime := TotalTime - MoveDuration > 0.0

	 

	            # The fraction of a rotation to make.

	            RotationFraction := (TimePerRotation - LeftoverTime)/TimePerRotation

	 

	            # Make a modified fractional rotation by using Slerp(). The Slerp() function does spherical interpolation

	            # between rotations to find the shortest rotation between two different rotations.

	            ModifiedRotation := Slerp[IdentityRotation(),  IdentityRotation().RotateBy(RotationToApply), RotationFraction]

	        then:

	            # Set the RotationToApply to the modified rotation, and multiply the animation time by

	            # the RotationFraction to get the modified animation time.

	            set RotationToApply = ModifiedRotation

	            set AnimationTime = AnimationTime * RotationFraction

	            # Since the TotalTime should not go past the MoveDuration,

	            # set TotalTime to MoveDuration.

	            set TotalTime = MoveDuration

	 

	        # The parameter to determine how far along the root prop is in the animation.

	        # The Lerp Parameter is based on the total number of rotations since the RootProp should guarantee that it makes

	        # at least that many rotations over the whole animation.

	        LerpParameter := (TotalTime * RotationRate) / (TotalRotations)

	        # Build the ending transform for the RootProp to move to.

	        set EndTransform = transform:

	            # Use Lerp() to find how far between the StartingTransform and the TargetTransform the RootProp should translate.

	            # Do the same for scale, and find the shortest rotation between the original transform and a rotation to apply to it.

	            Translation := Lerp(OriginalTransform.Translation, TargetTransform.Translation, LerpParameter)

	            Rotation := MakeShortestRotationBetween(OriginalTransform.Rotation, OriginalTransform.Rotation.RotateBy(RotationToApply))

	            Scale := Lerp(OriginalTransform.Scale, TargetTransform.Scale, LerpParameter)

	 

	        # Build the animation keyframe to animate the RootProp.

	        Keyframe := keyframe_delta:

	            DeltaLocation := EndTransform.Translation - StartTransform.Translation,

	            DeltaRotation := EndTransform.Rotation,

	            DeltaScale := EndTransform.Scale/StartTransform.Scale,

	            Time := AnimationTime,

	            # Use the MoveEaseType for interpolation if UseEasePerKeyframe is true,

	            # otherwise use the Linear movement type.

	            Interpolation :=

	                if:

	                    UseEasePerKeyframe?

	                then:

	                    GetCubicBezierForEaseType(MoveEaseType)

	                else:

	                    GetCubicBezierForEaseType(move_to_ease_type.Linear)

	 

	        # Add the new keyframe to the KeyFrames array, and set the

	        # StartTransform to the EndTransform.

	        set Keyframes += array{Keyframe}

	        set StartTransform = EndTransform

	        # Break out of the loop if the TotalTime passes the MoveDuration.

	        if:

	            TotalTime >= MoveDuration

	        then:

	            break

	 

	    # Return the completed array of keyframes.

	    Keyframes