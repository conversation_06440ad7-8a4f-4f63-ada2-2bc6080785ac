using. /Fortnite.com/AI
using. /Fortnite.com/Characters
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Game
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Teams
using. /Fortnite.com/UI
using. /Fortnite.com/Vehicles
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/Curves
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/UI
using. /Verse.org/Assets
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Concurrency
using. /Verse.org/Native
using. /Verse.org/Random
using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Verse
using. VGiga


race_track_tag := class(tag):
	GetAll<public>(D:creative_device)<transacts>:[]race_track=
		for(Device:D.FindCreativeObjectsWithTag(Self),
			Casted := race_track[Device]
		):
			Casted

RaceTipTrackType<localizes>:message = "Car - spawns car, WalkCheckpoints - uses checkpoints, Walk - no checkpoints and WinVolume required"
RaceTipStartChannel<localizes>:message = "Required. External devices can use this channnel to start the race."
RaceTipMStartTeleport<localizes>:message = "Optional. Moves player to this teleporter on race start."
RaceTipMExitVolume<localizes>:message = "Optional. Will end (AS CANCEL) the race when player enters this volume."
RaceTipMExitTeleport<localizes>:message = "Optional. Will teleport player to this teleporter when race is finished."
RaceTipMWinVolume<localizes>:message = "Optional. Will end (AS WIN) the race when player enters this volume. Can be used instead of checkpoints (last checkpoint automatically finishes the race too)"

race_track<public> := class(creative_device):

	@editable:
	GlobalTrackForAllPlayers:logic = false
	var GlobalTrackRunInProgress :observable_value_logic= observable_value_logic:
		Value := false
		
	@editable:
		ToolTip := RaceTipTrackType
	TrackType:track_type = track_type.Car

	@editable:
		ToolTip := RaceTipStartChannel
	StartChannel:?channel_device = false

	@editable:
		ToolTip := RaceTipMStartTeleport
	MStartTeleport:?teleporter_device = false

	@editable:
		ToolTip := RaceTipMExitVolume
	MExitVolume:?volume_device = false

	@editable:
		ToolTip := RaceTipMExitVolume
	MExitVolume2:?volume_device = false

	@editable:
		ToolTip := RaceTipMExitTeleport
	MExitTeleport:?teleporter_device = false

	@editable:
		ToolTip := RaceTipMWinVolume
	MWinVolume:?volume_device = false

	@editable:
	MEstimatedSecondsToFinishForReward:?int = false

	@editable:
	MEstimatedSecondsToFinishChceckpointForReward:?int = false

	@editable:
	GrantRewardsEvenWhenDied:logic = false

	@editable:
	MGateToStartTimer:?volume_device = false

	@editable:
	MUseCoinsProgressBarPointsInitialValue:?int = false

	@editable:
	MDecreaseCoinsOverTimeInterval:?float = option. 1.0

	@editable:
	ObjectsToMoveOnStart:[]move_object = array{}

	@editable:
	MTimeLimit:?float = false

	@editable
	MGlobalTrack_EndMinigameNoPlayersInVolume:?volume_device = false
	
	@editable
	MGoldRewardPerCoinTimeSec:?int = false
