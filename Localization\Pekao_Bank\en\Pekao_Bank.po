﻿# Pekao_Bank English translation.
# 
msgid ""
msgstr ""
"Project-Id-Version: Pekao_Bank\n"
"POT-Creation-Date: 2025-05-21 11:51\n"
"PO-Revision-Date: 2025-05-21 11:51\n"
"Language-Team: \n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. Key:	EC2FAF7D482C6EEBB0A14BBB29764F0C
#. SourceLocation:	/Pekao_Bank/Conversations/Guard_InvestingAcc.Guard_InvestingAcc:VkConversationNode_Choice_0.ChoiceText
#: /Pekao_Bank/Conversations/Guard_InvestingAcc.Guard_InvestingAcc:VkConversationNode_Choice_0.ChoiceText
msgctxt ",EC2FAF7D482C6EEBB0A14BBB29764F0C"
msgid "Dzie<PERSON> dobry! Co mogę tutaj z<PERSON>?"
msgstr "Good morning! What can I do here?"

#. Key:	9C92F3F2430E453BB109FC9E18552F5B
#. SourceLocation:	/Pekao_Bank/Conversations/Guard_InvestingAcc.Guard_InvestingAcc:VkConversationNode_Choice_1.ChoiceText
#: /Pekao_Bank/Conversations/Guard_InvestingAcc.Guard_InvestingAcc:VkConversationNode_Choice_1.ChoiceText
msgctxt ",9C92F3F2430E453BB109FC9E18552F5B"
msgid "Jak to działa? "
msgstr "How does it work? "

#. Key:	98E7436F449FD2C12A9CD7A314D6E427
#. SourceLocation:	/Pekao_Bank/Conversations/Guard_InvestingAcc.Guard_InvestingAcc:VkConversationNode_Choice_2.ChoiceText
#: /Pekao_Bank/Conversations/Guard_InvestingAcc.Guard_InvestingAcc:VkConversationNode_Choice_2.ChoiceText
msgctxt ",98E7436F449FD2C12A9CD7A314D6E427"
msgid "Wiem już wszystko. Do zobaczenia!"
msgstr "I already know everything. See you soon!"

#. Key:	CF85B3DF44C7AFF5B961C7B37CD69179
#. SourceLocation:	/Pekao_Bank/Conversations/Guard_InvestingAcc.Guard_InvestingAcc:VkConversationNode_Choice_3.ChoiceText
#: /Pekao_Bank/Conversations/Guard_InvestingAcc.Guard_InvestingAcc:VkConversationNode_Choice_3.ChoiceText
msgctxt ",CF85B3DF44C7AFF5B961C7B37CD69179"
msgid "Do czego jest ZIELONY przycisk?"
msgstr "What is the green button for?"

#. Key:	4870958D454B911205F8FF81F7997A3C
#. SourceLocation:	/Pekao_Bank/Conversations/Guard_InvestingAcc.Guard_InvestingAcc:VkConversationNode_Choice_4.ChoiceText
#: /Pekao_Bank/Conversations/Guard_InvestingAcc.Guard_InvestingAcc:VkConversationNode_Choice_4.ChoiceText
msgctxt ",4870958D454B911205F8FF81F7997A3C"
msgid "Do czego jest NIEBIESKI przycisk?"
msgstr "What is the BLUE button for?"

#. Key:	DF451380489AA1DDBAF9D0B555DE82E3
#. SourceLocation:	/Pekao_Bank/Conversations/Guard_InvestingAcc.Guard_InvestingAcc:VkConversationNode_Speech_0.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Guard_InvestingAcc.Guard_InvestingAcc:VkConversationNode_Speech_0.GeneralConfig.DefaultMessage
msgctxt ",DF451380489AA1DDBAF9D0B555DE82E3"
msgid "***"
msgstr "***"

#. Key:	444266D840A03E221F9819A5A9313F68
#. SourceLocation:	/Pekao_Bank/Conversations/Guard_InvestingAcc.Guard_InvestingAcc:VkConversationNode_Speech_1.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Guard_InvestingAcc.Guard_InvestingAcc:VkConversationNode_Speech_1.GeneralConfig.DefaultMessage
msgctxt ",444266D840A03E221F9819A5A9313F68"
msgid "W tym miejscu możesz skorzystać z konta oszczędnościowego i pomnożyć pieniądze Twojego miasta!"
msgstr "This is where you can use your savings account and multiply your city's money!"

#. Key:	10A04A0A43A4A49A0E9F7F8C2C5AFE2D
#. SourceLocation:	/Pekao_Bank/Conversations/Guard_InvestingAcc.Guard_InvestingAcc:VkConversationNode_Speech_2.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Guard_InvestingAcc.Guard_InvestingAcc:VkConversationNode_Speech_2.GeneralConfig.DefaultMessage
msgctxt ",10A04A0A43A4A49A0E9F7F8C2C5AFE2D"
msgid "To bardzo proste. Wpłać pieniądze, poczekaj, aż się namnożą, i je wypłać"
msgstr "It's very simple. Deposit money, wait for it to multiply, and withdraw it"

#. Key:	908616394D3BC991E141D1A49F6403C2
#. SourceLocation:	/Pekao_Bank/Conversations/Guard_InvestingAcc.Guard_InvestingAcc:VkConversationNode_Speech_3.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Guard_InvestingAcc.Guard_InvestingAcc:VkConversationNode_Speech_3.GeneralConfig.DefaultMessage
msgctxt ",908616394D3BC991E141D1A49F6403C2"
msgid "ZIELONYM przyciskiem WYPŁACASZ pieniądze."
msgstr "With the GREEN button, you withdraw money."

#. Key:	F8C7EDB447BDE078DB739988F5E6714D
#. SourceLocation:	/Pekao_Bank/Conversations/Guard_InvestingAcc.Guard_InvestingAcc:VkConversationNode_Speech_4.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Guard_InvestingAcc.Guard_InvestingAcc:VkConversationNode_Speech_4.GeneralConfig.DefaultMessage
msgctxt ",F8C7EDB447BDE078DB739988F5E6714D"
msgid "NIEBIESKIM przyciskiem WPŁACASZ pieniądze."
msgstr "With the BLUE button you DEPOSIT money."

#. Key:	7C9FA66D4B669C9DA7FB2F855A7D77DB
#. SourceLocation:	/Pekao_Bank/Conversations/Guard_InvestingAcc.Guard_InvestingAcc:VkConversationNode_Speech_5.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Guard_InvestingAcc.Guard_InvestingAcc:VkConversationNode_Speech_5.GeneralConfig.DefaultMessage
msgctxt ",7C9FA66D4B669C9DA7FB2F855A7D77DB"
msgid "Miłego dnia"
msgstr "Have a nice day"

#. Key:	A6E0C9584449F7DF2ED696909ACF6DE3
#. SourceLocation:	/Pekao_Bank/Conversations/JewelaryStore_Owner.JewelaryStore_Owner:VkConversationNode_Choice_0.ChoiceText
#: /Pekao_Bank/Conversations/JewelaryStore_Owner.JewelaryStore_Owner:VkConversationNode_Choice_0.ChoiceText
msgctxt ",A6E0C9584449F7DF2ED696909ACF6DE3"
msgid "Dzień dobry, chciałbym otworzyć tutaj sklep jublierski!"
msgstr "Good afternoon, I would like to open a jubilee shop here!"

#. Key:	9BFAB1B743BF9A17C63FF6A9F516694A
#. SourceLocation:	/Pekao_Bank/Conversations/JewelaryStore_Owner.JewelaryStore_Owner:VkConversationNode_Choice_1.ChoiceText
#: /Pekao_Bank/Conversations/JewelaryStore_Owner.JewelaryStore_Owner:VkConversationNode_Choice_1.ChoiceText
msgctxt ",9BFAB1B743BF9A17C63FF6A9F516694A"
msgid "Oczywiście! Otwierajmy! "
msgstr "Of course! Let's open up! "

#. Key:	1F47311A4F79F9FA9CD9CAACE6E6A6AA
#. SourceLocation:	/Pekao_Bank/Conversations/JewelaryStore_Owner.JewelaryStore_Owner:VkConversationNode_Choice_2.ChoiceText
#: /Pekao_Bank/Conversations/JewelaryStore_Owner.JewelaryStore_Owner:VkConversationNode_Choice_2.ChoiceText
msgctxt ",1F47311A4F79F9FA9CD9CAACE6E6A6AA"
msgid "Prosze udac się do banku i podpisac umowę kredytową a ja zniszczę ten kamień za Panem."
msgstr "Please go to the bank and sign the loan agreement and I will destroy this stone after you."

#. Key:	2C421D6B4A26A38054E2038744F0E889
#. SourceLocation:	/Pekao_Bank/Conversations/JewelaryStore_Owner.JewelaryStore_Owner:VkConversationNode_Choice_3.ChoiceText
#: /Pekao_Bank/Conversations/JewelaryStore_Owner.JewelaryStore_Owner:VkConversationNode_Choice_3.ChoiceText
msgctxt ",2C421D6B4A26A38054E2038744F0E889"
msgid "Nie mam tyle  pieniędzy"
msgstr "I don't have that much money"

#. Key:	78C2B6944DF6478B3218BE87C73A0734
#. SourceLocation:	/Pekao_Bank/Conversations/JewelaryStore_Owner.JewelaryStore_Owner:VkConversationNode_Speech_0.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/JewelaryStore_Owner.JewelaryStore_Owner:VkConversationNode_Speech_0.GeneralConfig.DefaultMessage
msgctxt ",78C2B6944DF6478B3218BE87C73A0734"
msgid "*czeka*"
msgstr "*waiting*"

#. Key:	8C4967874EA240A64F46F3B2F68F185E
#. SourceLocation:	/Pekao_Bank/Conversations/JewelaryStore_Owner.JewelaryStore_Owner:VkConversationNode_Speech_1.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/JewelaryStore_Owner.JewelaryStore_Owner:VkConversationNode_Speech_1.GeneralConfig.DefaultMessage
msgctxt ",8C4967874EA240A64F46F3B2F68F185E"
msgid "Oczywiście Burmistrzu! Budowa będzie kosztowała 90 000 $. Czy masz wystarczającą ilość pieniędzy?"
msgstr "Of course, Mayor! Construction will cost $90,000. Do you have enough money?"

#. Key:	C74DCCFA4E16CCE6A08B21B98EFCB6DC
#. SourceLocation:	/Pekao_Bank/Conversations/JewelaryStore_Owner.JewelaryStore_Owner:VkConversationNode_Speech_2.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/JewelaryStore_Owner.JewelaryStore_Owner:VkConversationNode_Speech_2.GeneralConfig.DefaultMessage
msgctxt ",C74DCCFA4E16CCE6A08B21B98EFCB6DC"
msgid "Zajmę się formalnościami, a tymczasem proszę mi pomóc i usunąć kamienie torujące drogę do placu budowy!"
msgstr "I will take care of the formalities, and in the meantime, please help me and remove the stones that are paving the way to the construction site!"

#. Key:	91CC2C554555A07B50A7129D7AFAE077
#. SourceLocation:	/Pekao_Bank/Conversations/JewelaryStore_Owner.JewelaryStore_Owner:VkConversationNode_Speech_3.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/JewelaryStore_Owner.JewelaryStore_Owner:VkConversationNode_Speech_3.GeneralConfig.DefaultMessage
msgctxt ",91CC2C554555A07B50A7129D7AFAE077"
msgid "Pamiętaj, że jeśli będziesz potrzebować środków, zawsze możesz cofnąć się do banku i skorzystać ze skarbonki, kredytu lub konta oszczędnościowego!"
msgstr "Remember that if you need funds, you can always go back to the bank and use your piggy bank, credit or savings account!"

#. Key:	9192EAAB47E76B1FAB8ACABE87E8448E
#. SourceLocation:	/Pekao_Bank/Conversations/Jewelery_End.Jewelery_End:VkConversationNode_Choice_0.ChoiceText
#: /Pekao_Bank/Conversations/Jewelery_End.Jewelery_End:VkConversationNode_Choice_0.ChoiceText
msgctxt ",9192EAAB47E76B1FAB8ACABE87E8448E"
msgid "Jak idzie biznes?"
msgstr "How's the business going?"

#. Key:	D9DF74F642585E722A728184794B590D
#. SourceLocation:	/Pekao_Bank/Conversations/Jewelery_End.Jewelery_End:VkConversationNode_Choice_1.ChoiceText
#: /Pekao_Bank/Conversations/Jewelery_End.Jewelery_End:VkConversationNode_Choice_1.ChoiceText
msgctxt ",D9DF74F642585E722A728184794B590D"
msgid "Jak zdobyć diamenty?"
msgstr "How to get diamonds?"

#. Key:	E34CAF29425ECAEFB4DA379C61D00BAE
#. SourceLocation:	/Pekao_Bank/Conversations/Jewelery_End.Jewelery_End:VkConversationNode_Speech_0.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Jewelery_End.Jewelery_End:VkConversationNode_Speech_0.GeneralConfig.DefaultMessage
msgctxt ",E34CAF29425ECAEFB4DA379C61D00BAE"
msgid "*czeka na klientów*"
msgstr "*waiting for customers*"

#. Key:	8B2F06A64FD86066FAE39B98D478792B
#. SourceLocation:	/Pekao_Bank/Conversations/Jewelery_End.Jewelery_End:VkConversationNode_Speech_1.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Jewelery_End.Jewelery_End:VkConversationNode_Speech_1.GeneralConfig.DefaultMessage
msgctxt ",8B2F06A64FD86066FAE39B98D478792B"
msgid "Jak widać. Interes idzie świetnie!"
msgstr "As you can see. Business is going great!"

#. Key:	8DC8B0BA469B12F4A061B996D293EA6C
#. SourceLocation:	/Pekao_Bank/Conversations/Jewelery_End.Jewelery_End:VkConversationNode_Speech_2.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Jewelery_End.Jewelery_End:VkConversationNode_Speech_2.GeneralConfig.DefaultMessage
msgctxt ",8DC8B0BA469B12F4A061B996D293EA6C"
msgid "Słyszałem, ze w banku jest maszyna, która potrafi zamienić węgiel w diament. Chętnie odkupie jeden diament za 5000$."
msgstr "I heard there's a machine in the bank that can turn coal into diamond. He will gladly buy one diamond for $5000."

#. Key:	93C7AA014498E4DC563849B20C46CC3E
#. SourceLocation:	/Pekao_Bank/Conversations/LoanMan.LoanMan:VkConversationNode_Choice_0.ChoiceText
#: /Pekao_Bank/Conversations/LoanMan.LoanMan:VkConversationNode_Choice_0.ChoiceText
msgctxt ",93C7AA014498E4DC563849B20C46CC3E"
msgid "Chciałbym wsiąć kredyt na rozbudowe miasta"
msgstr "I would like to take a loan for the expansion of the city"

#. Key:	24594128436A0966EC9258AFEB830BD2
#. SourceLocation:	/Pekao_Bank/Conversations/LoanMan.LoanMan:VkConversationNode_Choice_1.ChoiceText
#: /Pekao_Bank/Conversations/LoanMan.LoanMan:VkConversationNode_Choice_1.ChoiceText
msgctxt ",24594128436A0966EC9258AFEB830BD2"
msgid "Czym sie Pan zajmuje?"
msgstr "What do you do?"

#. Key:	ADEB0B3D493ECB9F542D70A11D10CA26
#. SourceLocation:	/Pekao_Bank/Conversations/LoanMan.LoanMan:VkConversationNode_Choice_2.ChoiceText
#: /Pekao_Bank/Conversations/LoanMan.LoanMan:VkConversationNode_Choice_2.ChoiceText
msgctxt ",ADEB0B3D493ECB9F542D70A11D10CA26"
msgid "A to nie potrzebuje kredytu - nara!"
msgstr "And this does not need credit - nara!"

#. Key:	618ACE204939DCAE46759F9593C17683
#. SourceLocation:	/Pekao_Bank/Conversations/LoanMan.LoanMan:VkConversationNode_Speech_0.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/LoanMan.LoanMan:VkConversationNode_Speech_0.GeneralConfig.DefaultMessage
msgctxt ",618ACE204939DCAE46759F9593C17683"
msgid "*czeka*"
msgstr "*waiting*"

#. Key:	A38AF7FA4650D297ED1E0C9388469E26
#. SourceLocation:	/Pekao_Bank/Conversations/LoanMan.LoanMan:VkConversationNode_Speech_1.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/LoanMan.LoanMan:VkConversationNode_Speech_1.GeneralConfig.DefaultMessage
msgctxt ",A38AF7FA4650D297ED1E0C9388469E26"
msgid "Spoko mordo trzymaj 10 000$"
msgstr "Cool mordo, hold $10,000"

#. Key:	0355D0E349C01859597784B2EED6A629
#. SourceLocation:	/Pekao_Bank/Conversations/LoanMan.LoanMan:VkConversationNode_Speech_2.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/LoanMan.LoanMan:VkConversationNode_Speech_2.GeneralConfig.DefaultMessage
msgctxt ",0355D0E349C01859597784B2EED6A629"
msgid "Zajmuje się przyznawaniem kredytów"
msgstr "It deals with the granting of loans"

#. Key:	66B67E504B6D52077BC3ECB38493863C
#. SourceLocation:	/Pekao_Bank/Conversations/LoanManCreditRunning.LoanManCreditRunning:VkConversationNode_Choice_0.ChoiceText
#: /Pekao_Bank/Conversations/LoanManCreditRunning.LoanManCreditRunning:VkConversationNode_Choice_0.ChoiceText
msgctxt ",66B67E504B6D52077BC3ECB38493863C"
msgid "Chciałbym wsiąć kredyt na rozbudowe miasta"
msgstr "I would like to take a loan for the expansion of the city"

#. Key:	F6E239F54427D570EF1450A204B702FB
#. SourceLocation:	/Pekao_Bank/Conversations/LoanManCreditRunning.LoanManCreditRunning:VkConversationNode_Choice_1.ChoiceText
#: /Pekao_Bank/Conversations/LoanManCreditRunning.LoanManCreditRunning:VkConversationNode_Choice_1.ChoiceText
msgctxt ",F6E239F54427D570EF1450A204B702FB"
msgid "Czym sie Pan zajmuje?"
msgstr "What do you do?"

#. Key:	AAAE1EB24E5C56C73AA92BA597B3DEE0
#. SourceLocation:	/Pekao_Bank/Conversations/LoanManCreditRunning.LoanManCreditRunning:VkConversationNode_Choice_2.ChoiceText
#: /Pekao_Bank/Conversations/LoanManCreditRunning.LoanManCreditRunning:VkConversationNode_Choice_2.ChoiceText
msgctxt ",AAAE1EB24E5C56C73AA92BA597B3DEE0"
msgid "A to nie potrzebuje kredytu - nara!"
msgstr "And this does not need credit - nara!"

#. Key:	3A66ACC64E90ABA51079DFBB8A28100B
#. SourceLocation:	/Pekao_Bank/Conversations/LoanManCreditRunning.LoanManCreditRunning:VkConversationNode_Speech_0.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/LoanManCreditRunning.LoanManCreditRunning:VkConversationNode_Speech_0.GeneralConfig.DefaultMessage
msgctxt ",3A66ACC64E90ABA51079DFBB8A28100B"
msgid "*czeka*"
msgstr "*waiting*"

#. Key:	FDF0C3C44095E3FCCE84DA807D4D21EA
#. SourceLocation:	/Pekao_Bank/Conversations/LoanManCreditRunning.LoanManCreditRunning:VkConversationNode_Speech_1.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/LoanManCreditRunning.LoanManCreditRunning:VkConversationNode_Speech_1.GeneralConfig.DefaultMessage
msgctxt ",FDF0C3C44095E3FCCE84DA807D4D21EA"
msgid "Już dostałeś kredyt, poczekaj aż go spłacisz, żeby dostać kolejny."
msgstr "You've already got a loan, wait until you pay it off to get another one."

#. Key:	C1F293E94EC18D3E6B5149840D9A7733
#. SourceLocation:	/Pekao_Bank/Conversations/LoanManCreditRunning.LoanManCreditRunning:VkConversationNode_Speech_2.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/LoanManCreditRunning.LoanManCreditRunning:VkConversationNode_Speech_2.GeneralConfig.DefaultMessage
msgctxt ",C1F293E94EC18D3E6B5149840D9A7733"
msgid "Zajmuje się przyznawaniem kredytów"
msgstr "It deals with the granting of loans"

#. Key:	BC604E124346C0F8BD6F5486054A6596
#. SourceLocation:	/Pekao_Bank/Conversations/Piggybank_Guard.Piggybank_Guard:VkConversationNode_Choice_0.ChoiceText
#: /Pekao_Bank/Conversations/Piggybank_Guard.Piggybank_Guard:VkConversationNode_Choice_0.ChoiceText
msgctxt ",BC604E124346C0F8BD6F5486054A6596"
msgid "Co to jest?"
msgstr "What is it?"

#. Key:	93088CBE484D8309ADAE77BFAA6913CD
#. SourceLocation:	/Pekao_Bank/Conversations/Piggybank_Guard.Piggybank_Guard:VkConversationNode_Choice_1.ChoiceText
#: /Pekao_Bank/Conversations/Piggybank_Guard.Piggybank_Guard:VkConversationNode_Choice_1.ChoiceText
msgctxt ",93088CBE484D8309ADAE77BFAA6913CD"
msgid "Wygląda... uroczo."
msgstr "It looks... cute."

#. Key:	B008809C451E4931245BE9879173B60D
#. SourceLocation:	/Pekao_Bank/Conversations/Piggybank_Guard.Piggybank_Guard:VkConversationNode_Choice_2.ChoiceText
#: /Pekao_Bank/Conversations/Piggybank_Guard.Piggybank_Guard:VkConversationNode_Choice_2.ChoiceText
msgctxt ",B008809C451E4931245BE9879173B60D"
msgid "Mogę coś wrzucić?"
msgstr "Can I throw something in?"

#. Key:	031AF3FA459C45F53830CAB3B5CD75DA
#. SourceLocation:	/Pekao_Bank/Conversations/Piggybank_Guard.Piggybank_Guard:VkConversationNode_Choice_4.ChoiceText
#: /Pekao_Bank/Conversations/Piggybank_Guard.Piggybank_Guard:VkConversationNode_Choice_4.ChoiceText
msgctxt ",031AF3FA459C45F53830CAB3B5CD75DA"
msgid "Wiem juz wszystko. Do widzenia."
msgstr "I already know everything. Goodbye."

#. Key:	6E8149D0496FC2F98A82B4B6E3B84A84
#. SourceLocation:	/Pekao_Bank/Conversations/Piggybank_Guard.Piggybank_Guard:VkConversationNode_Speech_0.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Piggybank_Guard.Piggybank_Guard:VkConversationNode_Speech_0.GeneralConfig.DefaultMessage
msgctxt ",6E8149D0496FC2F98A82B4B6E3B84A84"
msgid "*pilnuje*"
msgstr "*watching*"

#. Key:	F597DFFB4D259F8C44F31CBEB2242732
#. SourceLocation:	/Pekao_Bank/Conversations/Piggybank_Guard.Piggybank_Guard:VkConversationNode_Speech_1.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Piggybank_Guard.Piggybank_Guard:VkConversationNode_Speech_1.GeneralConfig.DefaultMessage
msgctxt ",F597DFFB4D259F8C44F31CBEB2242732"
msgid "To  Świnka Skarbonka."
msgstr "It's Piggy Bank."

#. Key:	E3E0CA23408B5143159C16968D1FB028
#. SourceLocation:	/Pekao_Bank/Conversations/Piggybank_Guard.Piggybank_Guard:VkConversationNode_Speech_2.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Piggybank_Guard.Piggybank_Guard:VkConversationNode_Speech_2.GeneralConfig.DefaultMessage
msgctxt ",E3E0CA23408B5143159C16968D1FB028"
msgid "Urocza? To fakt. Zna też kilka imponujących sztuczek! "
msgstr "Charming? It's a fact. He also knows some impressive tricks! "

#. Key:	A2615E44402F0411ED6080BE53A6AC2C
#. SourceLocation:	/Pekao_Bank/Conversations/Piggybank_Guard.Piggybank_Guard:VkConversationNode_Speech_3.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Piggybank_Guard.Piggybank_Guard:VkConversationNode_Speech_3.GeneralConfig.DefaultMessage
msgctxt ",A2615E44402F0411ED6080BE53A6AC2C"
msgid "Możesz, ale pamiętaj, że będziesz mógł wykorzystać swoje środki dopiero wtedy, gdy urośnie do maksymalnego poziomu! "
msgstr "You can, but remember that you will only be able to use your funds when it grows to the maximum level! "

#. Key:	671239934B442C95A4FEE19FA483D311
#. SourceLocation:	/Pekao_Bank/Conversations/Piggybank_Guard.Piggybank_Guard:VkConversationNode_Speech_4.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Piggybank_Guard.Piggybank_Guard:VkConversationNode_Speech_4.GeneralConfig.DefaultMessage
msgctxt ",671239934B442C95A4FEE19FA483D311"
msgid "Do widzenia"
msgstr "Goodbye"

#. Key:	7458E3A6497410F4562410B712680A1F
#. SourceLocation:	/Pekao_Bank/Conversations/PreGameAssistant.PreGameAssistant:VkConversationNode_Choice_0.ChoiceText
#: /Pekao_Bank/Conversations/PreGameAssistant.PreGameAssistant:VkConversationNode_Choice_0.ChoiceText
msgctxt ",7458E3A6497410F4562410B712680A1F"
msgid "Chciałbym wsiąć kredyt na rozbudowe miasta"
msgstr "I would like to take a loan for the expansion of the city"

#. Key:	E4D718E540EC16257780A6AA7CCDB257
#. SourceLocation:	/Pekao_Bank/Conversations/PreGameAssistant.PreGameAssistant:VkConversationNode_Choice_1.ChoiceText
#: /Pekao_Bank/Conversations/PreGameAssistant.PreGameAssistant:VkConversationNode_Choice_1.ChoiceText
msgctxt ",E4D718E540EC16257780A6AA7CCDB257"
msgid "Czym sie Pan zajmuje?"
msgstr "What do you do?"

#. Key:	B497B4C64613DFE4EF631D92BA826074
#. SourceLocation:	/Pekao_Bank/Conversations/PreGameAssistant.PreGameAssistant:VkConversationNode_Choice_2.ChoiceText
#: /Pekao_Bank/Conversations/PreGameAssistant.PreGameAssistant:VkConversationNode_Choice_2.ChoiceText
msgctxt ",B497B4C64613DFE4EF631D92BA826074"
msgid "A to nie potrzebuje kredytu - nara!"
msgstr "And this does not need credit - nara!"

#. Key:	5EECF2184C1BA73E3E3B09946B068BC2
#. SourceLocation:	/Pekao_Bank/Conversations/PreGameAssistant.PreGameAssistant:VkConversationNode_Choice_3.ChoiceText
#: /Pekao_Bank/Conversations/PreGameAssistant.PreGameAssistant:VkConversationNode_Choice_3.ChoiceText
msgctxt ",5EECF2184C1BA73E3E3B09946B068BC2"
msgid "Nie potrzebuję pomocy"
msgstr "I don't need help"

#. Key:	643D0E8440AEBFF7922CBFB25EF514CE
#. SourceLocation:	/Pekao_Bank/Conversations/PreGameAssistant.PreGameAssistant:VkConversationNode_Choice_4.ChoiceText
#: /Pekao_Bank/Conversations/PreGameAssistant.PreGameAssistant:VkConversationNode_Choice_4.ChoiceText
msgctxt ",643D0E8440AEBFF7922CBFB25EF514CE"
msgid "Co mam zrobić?"
msgstr "What am I supposed to do?"

#. Key:	C5D0C0DB4D6148EAF90CADACEB6200CD
#. SourceLocation:	/Pekao_Bank/Conversations/PreGameAssistant.PreGameAssistant:VkConversationNode_Choice_5.ChoiceText
#: /Pekao_Bank/Conversations/PreGameAssistant.PreGameAssistant:VkConversationNode_Choice_5.ChoiceText
msgctxt ",C5D0C0DB4D6148EAF90CADACEB6200CD"
msgid "Dzięki, już wszystko wiem."
msgstr "Thanks, I already know everything."

#. Key:	1ECC8CD649008A429E656F9E6401AB59
#. SourceLocation:	/Pekao_Bank/Conversations/PreGameAssistant.PreGameAssistant:VkConversationNode_Speech_0.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/PreGameAssistant.PreGameAssistant:VkConversationNode_Speech_0.GeneralConfig.DefaultMessage
msgctxt ",1ECC8CD649008A429E656F9E6401AB59"
msgid "Cześć, graczu! Jestem Twoim asystentem. Chętnie pomogę Ci zrozumieć zasady gry."
msgstr "Hey, player! I'm your assistant. I will be happy to help you understand the rules of the game."

#. Key:	9B58EB2C448C4A03D777C1AE634CB1A4
#. SourceLocation:	/Pekao_Bank/Conversations/PreGameAssistant.PreGameAssistant:VkConversationNode_Speech_1.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/PreGameAssistant.PreGameAssistant:VkConversationNode_Speech_1.GeneralConfig.DefaultMessage
msgctxt ",9B58EB2C448C4A03D777C1AE634CB1A4"
msgid "Spoko mordo trzymaj 10 000$"
msgstr "Cool mordo, hold $10,000"

#. Key:	1D28815745E8E1259B5F969C6070371F
#. SourceLocation:	/Pekao_Bank/Conversations/PreGameAssistant.PreGameAssistant:VkConversationNode_Speech_2.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/PreGameAssistant.PreGameAssistant:VkConversationNode_Speech_2.GeneralConfig.DefaultMessage
msgctxt ",1D28815745E8E1259B5F969C6070371F"
msgid "Zajmuje się przyznawaniem kredytów"
msgstr "It deals with the granting of loans"

#. Key:	22AC01A640DA741A59618EA81D413922
#. SourceLocation:	/Pekao_Bank/Conversations/PreGameAssistant.PreGameAssistant:VkConversationNode_Speech_3.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/PreGameAssistant.PreGameAssistant:VkConversationNode_Speech_3.GeneralConfig.DefaultMessage
msgctxt ",22AC01A640DA741A59618EA81D413922"
msgid "W porządku, powodzenia!"
msgstr "All right, good luck!"

#. Key:	B26DAE2045BE7AD1EC5A0A9EE9595CC0
#. SourceLocation:	/Pekao_Bank/Conversations/PreGameAssistant.PreGameAssistant:VkConversationNode_Speech_4.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/PreGameAssistant.PreGameAssistant:VkConversationNode_Speech_4.GeneralConfig.DefaultMessage
msgctxt ",B26DAE2045BE7AD1EC5A0A9EE9595CC0"
msgid "Twoim celem jest wybranie burmistrza miasta. Burmistrz przechowuje stan gry — dopóki jest w grze, rozgrywka pozostaje aktywna. Podejdź do posągu gracza, którego chcesz mianować burmistrzem."
msgstr "Your goal is to elect the mayor of the city. The mayor stores the state of the game - as long as he is in the game, the game remains active. Approach the statue of the player you want to appoint as mayor."

#. Key:	D90DBC0F44E0B10E6DB19BA50DB48D5C
#. SourceLocation:	/Pekao_Bank/Conversations/Shop_Owner_End.Shop_Owner_End:VkConversationNode_Choice_0.ChoiceText
#: /Pekao_Bank/Conversations/Shop_Owner_End.Shop_Owner_End:VkConversationNode_Choice_0.ChoiceText
msgctxt ",D90DBC0F44E0B10E6DB19BA50DB48D5C"
msgid "Dzien dobry, jestem przedstawicielem banku PEKAO S.A. W czym moge Panu pomóc?"
msgstr "Good afternoon, I am a representative of PEKAO S.A. How can I help you?"

#. Key:	4ABB30D843DA0EC23FE85BB387684F7F
#. SourceLocation:	/Pekao_Bank/Conversations/Shop_Owner_End.Shop_Owner_End:VkConversationNode_Choice_1.ChoiceText
#: /Pekao_Bank/Conversations/Shop_Owner_End.Shop_Owner_End:VkConversationNode_Choice_1.ChoiceText
msgctxt ",4ABB30D843DA0EC23FE85BB387684F7F"
msgid "Świetnie. Bank PEKAO S.A pomaga wszystkim przedsiombiorcom. Ile wynosi koszt budowy?"
msgstr "That's great. Bank PEKAO S.A helps all entrepreneurs. How much is the construction cost?"

#. Key:	435A747D4E4CD0D81002E4AA89EA049B
#. SourceLocation:	/Pekao_Bank/Conversations/Shop_Owner_End.Shop_Owner_End:VkConversationNode_Choice_2.ChoiceText
#: /Pekao_Bank/Conversations/Shop_Owner_End.Shop_Owner_End:VkConversationNode_Choice_2.ChoiceText
msgctxt ",435A747D4E4CD0D81002E4AA89EA049B"
msgid "Prosze udac się do banku i podpisac umowę kredytową a ja zniszczę ten kamień za Panem."
msgstr "Please go to the bank and sign the loan agreement and I will destroy this stone after you."

#. Key:	99576E87462CB71D6F15F38F925FE0C0
#. SourceLocation:	/Pekao_Bank/Conversations/Shop_Owner_End.Shop_Owner_End:VkConversationNode_Choice_3.ChoiceText
#: /Pekao_Bank/Conversations/Shop_Owner_End.Shop_Owner_End:VkConversationNode_Choice_3.ChoiceText
msgctxt ",99576E87462CB71D6F15F38F925FE0C0"
msgid "Wróce do Pana później chwilowo jestem zajęty"
msgstr "I'll come back to you later, I'm busy."

#. Key:	D45E6C664B880EC62A025689CFF87FBA
#. SourceLocation:	/Pekao_Bank/Conversations/Shop_Owner_End.Shop_Owner_End:VkConversationNode_Choice_4.ChoiceText
#: /Pekao_Bank/Conversations/Shop_Owner_End.Shop_Owner_End:VkConversationNode_Choice_4.ChoiceText
msgctxt ",D45E6C664B880EC62A025689CFF87FBA"
msgid "Jak idzie biznes?"
msgstr "How's the business going?"

#. Key:	970148D44FAA009096B3278036D674EF
#. SourceLocation:	/Pekao_Bank/Conversations/Shop_Owner_End.Shop_Owner_End:VkConversationNode_Speech_0.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Shop_Owner_End.Shop_Owner_End:VkConversationNode_Speech_0.GeneralConfig.DefaultMessage
msgctxt ",970148D44FAA009096B3278036D674EF"
msgid "*czeka*"
msgstr "*waiting*"

#. Key:	DC17E1AC410C66EF6E854091627F0F00
#. SourceLocation:	/Pekao_Bank/Conversations/Shop_Owner_End.Shop_Owner_End:VkConversationNode_Speech_1.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Shop_Owner_End.Shop_Owner_End:VkConversationNode_Speech_1.GeneralConfig.DefaultMessage
msgctxt ",DC17E1AC410C66EF6E854091627F0F00"
msgid "Dzień dobry. Cieszę się, że znalazł pan czas. Chcemy otworzyć nowy salon jubilerski."
msgstr "Good morning. I'm glad you found the time. We want to open a new jewelry store."

#. Key:	35F77B424BDC12A707F12288A18E3EDD
#. SourceLocation:	/Pekao_Bank/Conversations/Shop_Owner_End.Shop_Owner_End:VkConversationNode_Speech_2.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Shop_Owner_End.Shop_Owner_End:VkConversationNode_Speech_2.GeneralConfig.DefaultMessage
msgctxt ",35F77B424BDC12A707F12288A18E3EDD"
msgid "Około 2 000 000$. Liczę na wsparcie banku w tej inwestycji."
msgstr "About $2,000,000. I look forward to the bank's support in this investment."

#. Key:	067F1D534642BF8DD1F150AFD40810C0
#. SourceLocation:	/Pekao_Bank/Conversations/Shop_Owner_End.Shop_Owner_End:VkConversationNode_Speech_3.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Shop_Owner_End.Shop_Owner_End:VkConversationNode_Speech_3.GeneralConfig.DefaultMessage
msgctxt ",067F1D534642BF8DD1F150AFD40810C0"
msgid "*czeka*"
msgstr "*waiting*"

#. Key:	FC9E42D24F5BEA4D4BDF7FA07F08B244
#. SourceLocation:	/Pekao_Bank/Conversations/Shop_Owner_End.Shop_Owner_End:VkConversationNode_Speech_4.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Shop_Owner_End.Shop_Owner_End:VkConversationNode_Speech_4.GeneralConfig.DefaultMessage
msgctxt ",FC9E42D24F5BEA4D4BDF7FA07F08B244"
msgid "Jak widać. Interes idzie świetnie!"
msgstr "As you can see. Business is going great!"

#. Key:	961C5E5645A34E9AFC1F7193F93B7753
#. SourceLocation:	/Pekao_Bank/Conversations/ShopOwnerConversation.ShopOwnerConversation:VkConversationNode_Choice_0.ChoiceText
#: /Pekao_Bank/Conversations/ShopOwnerConversation.ShopOwnerConversation:VkConversationNode_Choice_0.ChoiceText
msgctxt ",961C5E5645A34E9AFC1F7193F93B7753"
msgid "Oczywiście! Otwierajmy! "
msgstr "Of course! Let's open up! "

#. Key:	14BC04CB410D11C341A393B90B7F922A
#. SourceLocation:	/Pekao_Bank/Conversations/ShopOwnerConversation.ShopOwnerConversation:VkConversationNode_Choice_1.ChoiceText
#: /Pekao_Bank/Conversations/ShopOwnerConversation.ShopOwnerConversation:VkConversationNode_Choice_1.ChoiceText
msgctxt ",14BC04CB410D11C341A393B90B7F922A"
msgid "Nie mam tyle  pieniędzy"
msgstr "I don't have that much money"

#. Key:	43ADF0744F4D0757DF85ED9E76FF6E10
#. SourceLocation:	/Pekao_Bank/Conversations/ShopOwnerConversation.ShopOwnerConversation:VkConversationNode_Choice_2.ChoiceText
#: /Pekao_Bank/Conversations/ShopOwnerConversation.ShopOwnerConversation:VkConversationNode_Choice_2.ChoiceText
msgctxt ",43ADF0744F4D0757DF85ED9E76FF6E10"
msgid "Dzień dobry, chciałbym otworzyć tutaj sklep budowlany!"
msgstr "Good afternoon, I would like to open a construction shop here!"

#. Key:	1176B58E4157A16E46D8E28384CDFB40
#. SourceLocation:	/Pekao_Bank/Conversations/ShopOwnerConversation.ShopOwnerConversation:VkConversationNode_Choice_3.ChoiceText
#: /Pekao_Bank/Conversations/ShopOwnerConversation.ShopOwnerConversation:VkConversationNode_Choice_3.ChoiceText
msgctxt ",1176B58E4157A16E46D8E28384CDFB40"
msgid "Prosze udac się do banku i podpisac umowę kredytową a ja zniszczę ten kamień za Panem i zacznę pracę."
msgstr "Please go to the bank and sign the loan agreement and I will destroy this stone after you and start working."

#. Key:	8CF0D2454C388E5392AD6CAC2FBB462C
#. SourceLocation:	/Pekao_Bank/Conversations/ShopOwnerConversation.ShopOwnerConversation:VkConversationNode_Speech_0.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/ShopOwnerConversation.ShopOwnerConversation:VkConversationNode_Speech_0.GeneralConfig.DefaultMessage
msgctxt ",8CF0D2454C388E5392AD6CAC2FBB462C"
msgid "Pamiętaj, że jeśli będziesz potrzebować środków, zawsze możesz cofnąć się do banku i skorzystać ze skarbonki lub konta oszczędnościowego!"
msgstr "Remember that if you need funds, you can always go back to the bank and use your piggy bank or savings account!"

#. Key:	BED2799742A3DC35DF207C9C0E459C1E
#. SourceLocation:	/Pekao_Bank/Conversations/ShopOwnerConversation.ShopOwnerConversation:VkConversationNode_Speech_2.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/ShopOwnerConversation.ShopOwnerConversation:VkConversationNode_Speech_2.GeneralConfig.DefaultMessage
msgctxt ",BED2799742A3DC35DF207C9C0E459C1E"
msgid "*czeka*"
msgstr "*waiting*"

#. Key:	E46D8F48451EE3476D722695D289DBE1
#. SourceLocation:	/Pekao_Bank/Conversations/ShopOwnerConversation.ShopOwnerConversation:VkConversationNode_Speech_3.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/ShopOwnerConversation.ShopOwnerConversation:VkConversationNode_Speech_3.GeneralConfig.DefaultMessage
msgctxt ",E46D8F48451EE3476D722695D289DBE1"
msgid "Oczywiście Burmistrzu! Budowa będzie kosztowała 60 000$. Czy jesteś gotowy do działania?"
msgstr "Of course, Mayor! Construction will cost $60,000. Are you ready to take action?"

#. Key:	A612330045FCECBDA121C1B8EBE7B5F8
#. SourceLocation:	/Pekao_Bank/Conversations/ShopOwnerConversation.ShopOwnerConversation:VkConversationNode_Speech_4.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/ShopOwnerConversation.ShopOwnerConversation:VkConversationNode_Speech_4.GeneralConfig.DefaultMessage
msgctxt ",A612330045FCECBDA121C1B8EBE7B5F8"
msgid "Zajmę się formalnościami, a tymczasem proszę mi pomóc i usunąć kamienie torujące drogę do placu budowy!"
msgstr "I will take care of the formalities, and in the meantime, please help me and remove the stones that are paving the way to the construction site!"

#. Key:	9F62397249EDD4CB995035885004BA26
#. SourceLocation:	/Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Choice_0.ChoiceText
#: /Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Choice_0.ChoiceText
msgctxt ",9F62397249EDD4CB995035885004BA26"
msgid "Dzień dobry. Co mogę tutaj zrobić?"
msgstr "Good morning. What can I do here?"

#. Key:	63E7A8404F90D56784488EBA4E5931C4
#. SourceLocation:	/Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Choice_1.ChoiceText
#: /Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Choice_1.ChoiceText
msgctxt ",63E7A8404F90D56784488EBA4E5931C4"
msgid "Jak mogę generować pieniądze?"
msgstr "How can I generate money?"

#. Key:	AF7D10D74B18D9C42348CC92610FA9BC
#. SourceLocation:	/Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Choice_2.ChoiceText
#: /Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Choice_2.ChoiceText
msgctxt ",AF7D10D74B18D9C42348CC92610FA9BC"
msgid "Kopanie?"
msgstr "Digging?"

#. Key:	1ACE2BBA437D1F4B6305E38222D71FE7
#. SourceLocation:	/Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Choice_3.ChoiceText
#: /Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Choice_3.ChoiceText
msgctxt ",1ACE2BBA437D1F4B6305E38222D71FE7"
msgid "Jak mogę inaczej generować pieniądze?"
msgstr "How else can I generate money?"

#. Key:	5A0A3A084AC2E5411A9719B552989892
#. SourceLocation:	/Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Choice_4.ChoiceText
#: /Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Choice_4.ChoiceText
msgctxt ",5A0A3A084AC2E5411A9719B552989892"
msgid "Generowanie Pasywne?"
msgstr "Passive Generation?"

#. Key:	594B7B7D4D766ACFD65F5F9C5C59DCC5
#. SourceLocation:	/Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Choice_5.ChoiceText
#: /Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Choice_5.ChoiceText
msgctxt ",594B7B7D4D766ACFD65F5F9C5C59DCC5"
msgid "Generowanie aktywne?"
msgstr "Generating active?"

#. Key:	BC17093B4D84A67C3BF7DDB8BFA9F7A5
#. SourceLocation:	/Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Choice_6.ChoiceText
#: /Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Choice_6.ChoiceText
msgctxt ",BC17093B4D84A67C3BF7DDB8BFA9F7A5"
msgid "Ile informacji! Dziękuję, do widzenia."
msgstr "How much information! Thank you. Goodbye."

#. Key:	238C17F148E7D0E9A49DA2B075A7C722
#. SourceLocation:	/Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Speech_0.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Speech_0.GeneralConfig.DefaultMessage
msgctxt ",238C17F148E7D0E9A49DA2B075A7C722"
msgid "^pilnuje*"
msgstr "^watching*"

#. Key:	3A62DA954A1B2C2E8AAE3A83AD603665
#. SourceLocation:	/Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Speech_1.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Speech_1.GeneralConfig.DefaultMessage
msgctxt ",3A62DA954A1B2C2E8AAE3A83AD603665"
msgid "Dzień dobry burmistrzu. W tym miejscu możesz generować środki na rozbudowę miasta. Proces jest zautomatyzowany, ale Twoja pomoc zdecydowanie się przyda. Masz do dyspozycji 3 sposoby: kopanie, generowanie pasywne i aktywne."
msgstr "Good morning, Mayor. This is where you can generate funds for the expansion of the city. The process is automated, but your help will definitely come in handy. You have 3 modes at your disposal: digging, passive and active generation."

#. Key:	BAC110F64FBC6C54F93104B7EF8BAD57
#. SourceLocation:	/Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Speech_2.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Speech_2.GeneralConfig.DefaultMessage
msgctxt ",BAC110F64FBC6C54F93104B7EF8BAD57"
msgid "Będziesz miał do dyspozycji 3 sposoby: kopanie, generowanie pasywne i aktywne."
msgstr "You will have 3 modes at your disposal: digging, passive and active generation."

#. Key:	284FFA5F4CABD1E6CA3F60BC2C41CD9E
#. SourceLocation:	/Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Speech_3.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Speech_3.GeneralConfig.DefaultMessage
msgctxt ",284FFA5F4CABD1E6CA3F60BC2C41CD9E"
msgid "Widzisz strzałkę? Użyj kilofa i zdobywaj $ oraz kamień."
msgstr "See the arrow? Use a pickaxe and earn $ and a stone."

#. Key:	208D1D8F4A221466C271B19342B2B172
#. SourceLocation:	/Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Speech_4.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Speech_4.GeneralConfig.DefaultMessage
msgctxt ",208D1D8F4A221466C271B19342B2B172"
msgid "Rozwijaj skarbiec, aby kupić mennicę. Mennica generuje monety i nie wymaga od Ciebie uwagi."
msgstr "Develop a vault to buy a mint. Mint generates coins and does not require any attention from you."

#. Key:	75316E5A47B294BA3B8FB2A25E51C242
#. SourceLocation:	/Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Speech_5.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Speech_5.GeneralConfig.DefaultMessage
msgctxt ",75316E5A47B294BA3B8FB2A25E51C242"
msgid "Rozwijaj skarbiec i kup maszyny generujące banknoty z kamienia i drewna. Przez pewnien czas będziesz zarabiać o wiele więcej $ na sekunde."
msgstr "Develop a treasury and buy machines that generate banknotes from stone and wood. For some time, you will earn a lot more $ per second."

#. Key:	F3355E5C461B535D12475CA13F5163A1
#. SourceLocation:	/Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Speech_6.GeneralConfig.DefaultMessage
#: /Pekao_Bank/Conversations/Vault_Guard.Vault_Guard:VkConversationNode_Speech_6.GeneralConfig.DefaultMessage
msgctxt ",F3355E5C461B535D12475CA13F5163A1"
msgid "Miłego dnia"
msgstr "Have a nice day"

#. Key:	4444302546C1F8C7C5260D9B16C13933
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:BP_DistantInteractionPointWidget_C_19.WidgetTree_0.TextBlock_Distance.Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:BP_DistantInteractionPointWidget_C_19.WidgetTree_0.TextBlock_Distance.Text
msgctxt ",4444302546C1F8C7C5260D9B16C13933"
msgid "1000 M"
msgstr "1000 M"

#. Key:	77F21AA54D7159870709ADBA7D4D18C5
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Accolades_V2_C_UAID_047C16507C206F3802_1560760299.Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Accolades_V2_C_UAID_047C16507C206F3802_1560760299.Name
msgctxt ",77F21AA54D7159870709ADBA7D4D18C5"
msgid "Prezent!"
msgstr "A gift!"

#. Key:	1496496E4C0A213FD4275A86C09A2FF7
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Accolades_V2_C_UAID_9C6B0031E888673402_1587281550.Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Accolades_V2_C_UAID_9C6B0031E888673402_1587281550.Name
msgctxt ",1496496E4C0A213FD4275A86C09A2FF7"
msgid "Bank zgłoszony!"
msgstr "Bank reported!"

#. Key:	6C01A4334559F59C70AB45AE7A506AD5
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Accolades_V2_C_UAID_9C6B0031E888673402_1649603551.Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Accolades_V2_C_UAID_9C6B0031E888673402_1649603551.Name
msgctxt ",6C01A4334559F59C70AB45AE7A506AD5"
msgid "Aktualizacja banku!"
msgstr "Bank update!"

#. Key:	580F0E70430D50C2602E04A41437750D
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Accolades_V2_C_UAID_9C6B0031E888673402_1685111552.Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Accolades_V2_C_UAID_9C6B0031E888673402_1685111552.Name
msgctxt ",580F0E70430D50C2602E04A41437750D"
msgid "Aktualizacja banku!"
msgstr "Bank update!"

#. Key:	4F1034F343504A343820F5B620C0F60B
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Accolades_V2_C_UAID_9C6B0031E888673402_1685949553.Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Accolades_V2_C_UAID_9C6B0031E888673402_1685949553.Name
msgctxt ",4F1034F343504A343820F5B620C0F60B"
msgid "Aktualizacja banku!"
msgstr "Bank update!"

#. Key:	25B7050E4EBC62CE93BB9D819BD1527E
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Accolades_V2_C_UAID_9C6B0031E888673402_1686735554.Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Accolades_V2_C_UAID_9C6B0031E888673402_1686735554.Name
msgctxt ",25B7050E4EBC62CE93BB9D819BD1527E"
msgid "Aktualizacja banku!"
msgstr "Bank update!"

#. Key:	31E2D7ED44D3854593D827812505526B
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Accolades_V2_C_UAID_9C6B0031E888673402_1687424555.Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Accolades_V2_C_UAID_9C6B0031E888673402_1687424555.Name
msgctxt ",31E2D7ED44D3854593D827812505526B"
msgid "Aktualizacja banku!"
msgstr "Bank update!"

#. Key:	8502470F4E89AAFDF17C9D9E41018529
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Accolades_V2_C_UAID_9C6B0031E888673402_1724397556.Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Accolades_V2_C_UAID_9C6B0031E888673402_1724397556.Name
msgctxt ",8502470F4E89AAFDF17C9D9E41018529"
msgid "Zadanie zakończone!"
msgstr "The task is complete!"

#. Key:	75894C54495A47BE34546599A35CC616
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Accolades_V2_C_UAID_9C6B0031E888673402_1851423557.Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Accolades_V2_C_UAID_9C6B0031E888673402_1851423557.Name
msgctxt ",75894C54495A47BE34546599A35CC616"
msgid "Świnka złota!"
msgstr "Gold pig!"

#. Key:	6A4A5AD6445271BDF1EA4B85E90F32F7
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Accolades_V2_C_UAID_9C6B0031E888BE4F02_1647427398.Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Accolades_V2_C_UAID_9C6B0031E888BE4F02_1647427398.Name
msgctxt ",6A4A5AD6445271BDF1EA4B85E90F32F7"
msgid "Points"
msgstr "Puntos"

#. Key:	3057555646EBE6FBEF2231AF26D2ACAD
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Billboard_V2_C_UAID_047C16507C200A5602_1615234173.Billboard_WidgetComp.BillboardDetails.Text_3_F1B35DC7484B004015ED62BBD41D9681
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Billboard_V2_C_UAID_047C16507C200A5602_1615234173.Billboard_WidgetComp.BillboardDetails.Text_3_F1B35DC7484B004015ED62BBD41D9681
msgctxt ",3057555646EBE6FBEF2231AF26D2ACAD"
msgid "HIT TO EARN MONEY"
msgstr "HIT TO EARN MONEY"

#. Key:	65714D22455BC358DBB504B284CF4D24
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Billboard_V2_C_UAID_047C16507C20186002_1748769586.Billboard_WidgetComp.BillboardDetails.Text_3_F1B35DC7484B004015ED62BBD41D9681
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Billboard_V2_C_UAID_047C16507C20186002_1748769586.Billboard_WidgetComp.BillboardDetails.Text_3_F1B35DC7484B004015ED62BBD41D9681
msgctxt ",65714D22455BC358DBB504B284CF4D24"
msgid "Marketing"
msgstr "Marketing"

#. Key:	9D3718F64C4AE9D2E45CF499FDF72DD8
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Billboard_V2_C_UAID_047C16507C20186002_1846141588.Billboard_WidgetComp.BillboardDetails.Text_3_F1B35DC7484B004015ED62BBD41D9681
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Billboard_V2_C_UAID_047C16507C20186002_1846141588.Billboard_WidgetComp.BillboardDetails.Text_3_F1B35DC7484B004015ED62BBD41D9681
msgctxt ",9D3718F64C4AE9D2E45CF499FDF72DD8"
msgid "CLIENTS"
msgstr "CLIENTS"

#. Key:	606319E548A3722A80ED67862CDDFD8A
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Billboard_V2_C_UAID_047C16507C20186002_1895880589.Billboard_WidgetComp.BillboardDetails.Text_3_F1B35DC7484B004015ED62BBD41D9681
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Billboard_V2_C_UAID_047C16507C20186002_1895880589.Billboard_WidgetComp.BillboardDetails.Text_3_F1B35DC7484B004015ED62BBD41D9681
msgctxt ",606319E548A3722A80ED67862CDDFD8A"
msgid "115233"
msgstr "115233"

#. Key:	53D9FBDD44CF73D732DC6C90A9D081FE
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Billboard_V2_C_UAID_047C16507C20186002_1940247590.Billboard_WidgetComp.BillboardDetails.Text_3_F1B35DC7484B004015ED62BBD41D9681
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Billboard_V2_C_UAID_047C16507C20186002_1940247590.Billboard_WidgetComp.BillboardDetails.Text_3_F1B35DC7484B004015ED62BBD41D9681
msgctxt ",53D9FBDD44CF73D732DC6C90A9D081FE"
msgid "151661$"
msgstr "$151661"

#. Key:	40DFAB8C4D1B208D4CBC1DA9DC132661
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Billboard_V2_C_UAID_047C16507C20186002_1998012591.Billboard_WidgetComp.BillboardDetails.Text_3_F1B35DC7484B004015ED62BBD41D9681
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Billboard_V2_C_UAID_047C16507C20186002_1998012591.Billboard_WidgetComp.BillboardDetails.Text_3_F1B35DC7484B004015ED62BBD41D9681
msgctxt ",40DFAB8C4D1B208D4CBC1DA9DC132661"
msgid "1Client = 0,001$/s"
msgstr "1Client = 0.001$/s"

#. Key:	F8D094BA411D593040EEE892C87A913F
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Billboard_V2_C_UAID_047C16507C20186002_2050475592.Billboard_WidgetComp.BillboardDetails.Text_3_F1B35DC7484B004015ED62BBD41D9681
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Billboard_V2_C_UAID_047C16507C20186002_2050475592.Billboard_WidgetComp.BillboardDetails.Text_3_F1B35DC7484B004015ED62BBD41D9681
msgctxt ",F8D094BA411D593040EEE892C87A913F"
msgid "More Marketing = More Clients"
msgstr "More Marketing = More Customers"

#. Key:	9864904C42A5DB00B0B0C79CCFB242F0
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Billboard_V2_C_UAID_047C16507C20186002_2115495593.Billboard_WidgetComp.BillboardDetails.Text_3_F1B35DC7484B004015ED62BBD41D9681
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Billboard_V2_C_UAID_047C16507C20186002_2115495593.Billboard_WidgetComp.BillboardDetails.Text_3_F1B35DC7484B004015ED62BBD41D9681
msgctxt ",9864904C42A5DB00B0B0C79CCFB242F0"
msgid "Biznesy"
msgstr "Businesses"

#. Key:	1AE9F6214C789F2EEBBAEC8311E0CB9A
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Billboard_V2_C_UAID_047C16507C20196002_1139393772.Billboard_WidgetComp.BillboardDetails.Text_3_F1B35DC7484B004015ED62BBD41D9681
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Billboard_V2_C_UAID_047C16507C20196002_1139393772.Billboard_WidgetComp.BillboardDetails.Text_3_F1B35DC7484B004015ED62BBD41D9681
msgctxt ",1AE9F6214C789F2EEBBAEC8311E0CB9A"
msgid "Help Citizens Build Theis Businesses\r\n1 business = $1/s"
msgstr "Help Citizens Build These Businesses\n1 business = $1/s"

#. Key:	9AD0D98D4DC9116E65AEEE8259937EA8
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Billboard_V2_C_UAID_047C16507C20196002_1274412773.Billboard_WidgetComp.BillboardDetails.Text_3_F1B35DC7484B004015ED62BBD41D9681
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Billboard_V2_C_UAID_047C16507C20196002_1274412773.Billboard_WidgetComp.BillboardDetails.Text_3_F1B35DC7484B004015ED62BBD41D9681
msgctxt ",9AD0D98D4DC9116E65AEEE8259937EA8"
msgid "Transakcje Aplikacji Mobilnej"
msgstr "Mobile App Transactions"

#. Key:	A8B5E09C43AC79CA190EF697DD6FB278
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Billboard_V2_C_UAID_047C16507C20196002_1378949776.Billboard_WidgetComp.BillboardDetails.Text_3_F1B35DC7484B004015ED62BBD41D9681
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Billboard_V2_C_UAID_047C16507C20196002_1378949776.Billboard_WidgetComp.BillboardDetails.Text_3_F1B35DC7484B004015ED62BBD41D9681
msgctxt ",A8B5E09C43AC79CA190EF697DD6FB278"
msgid "Rozwijaj Aplikacje, aby więcej zarobić!\r\nUżyj przycisku na stole."
msgstr "Grow Apps to earn more!\nUse the button on the table."

#. Key:	710A73B3443FD777D70820AF8E0C679C
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20146002_1805934548.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20146002_1805934548.Interaction Text
msgctxt ",710A73B3443FD777D70820AF8E0C679C"
msgid "Take Money"
msgstr "Take Money"

#. Key:	59A88C0B4DCF09F7466FDB979373A657
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20156002_1960667808.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20156002_1960667808.Interaction Text
msgctxt ",59A88C0B4DCF09F7466FDB979373A657"
msgid "Zainwestuj w aplikacje -$1000"
msgstr "Invest in Apps -$1000"

#. Key:	55A332994935F3294F13C9B3CE2E144A
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20156002_2071068823.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20156002_2071068823.Interaction Text
msgctxt ",55A332994935F3294F13C9B3CE2E144A"
msgid "Zainwestuj w marketing"
msgstr "Invest in marketing"

#. Key:	EE95B58448CD9E1949B68C89224C90B9
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20264E02_1803534578.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20264E02_1803534578.Interaction Text
msgctxt ",EE95B58448CD9E1949B68C89224C90B9"
msgid "PLAY"
msgstr "GIOCARE"

#. Key:	005968E245446B6F762A7F8E78734771
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20264E02_1905793579.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20264E02_1905793579.Interaction Text
msgctxt ",005968E245446B6F762A7F8E78734771"
msgid "STOP"
msgstr "STOP"

#. Key:	0578C0B14FFFA565D6AAFA9F902140EF
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20283302_1794464550.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20283302_1794464550.Interaction Text
msgctxt ",0578C0B14FFFA565D6AAFA9F902140EF"
msgid "Zmień muzykę"
msgstr "Change the music"

#. Key:	96580B324381E6E8D7CF0E94470B6866
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20466802_1272776681.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20466802_1272776681.Interaction Text
msgctxt ",96580B324381E6E8D7CF0E94470B6866"
msgid "Wymień $ na złoto"
msgstr "Convert $ to gold"

#. Key:	3ABAA0DD4E916C1E2E3207A8BA3A5587
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20466802_1358429686.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20466802_1358429686.Interaction Text
msgctxt ",3ABAA0DD4E916C1E2E3207A8BA3A5587"
msgid "Wymień $ na złoto"
msgstr "Convert $ to gold"

#. Key:	02BC11F84E59AAF040217C9A2E67AB82
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20506102_2051893855.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20506102_2051893855.Interaction Text
msgctxt ",02BC11F84E59AAF040217C9A2E67AB82"
msgid "Wpłać 1000$"
msgstr "Deposit 1000$"

#. Key:	3521B2D24F6A6ACDD0E2A5987F300CF3
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20506102_2051896856.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20506102_2051896856.Interaction Text
msgctxt ",3521B2D24F6A6ACDD0E2A5987F300CF3"
msgid "Wypłać 1000$"
msgstr "Withdraw 1000$"

#. Key:	332B90AC4F6A5278C76F6288D8A5EB9B
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20516102_1700658043.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20516102_1700658043.Interaction Text
msgctxt ",332B90AC4F6A5278C76F6288D8A5EB9B"
msgid "Put Money"
msgstr "Put Money"

#. Key:	3A02EABA45621022674A1E9B823294C0
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20526102_1253659950.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20526102_1253659950.Interaction Text
msgctxt ",3A02EABA45621022674A1E9B823294C0"
msgid "Play"
msgstr "Igraj"

#. Key:	5640366D4A9BCF12871CE69DCF94EDA7
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20526102_2000097673.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20526102_2000097673.Interaction Text
msgctxt ",5640366D4A9BCF12871CE69DCF94EDA7"
msgid "PLAY"
msgstr "GIOCARE"

#. Key:	1468744347121DECD2252F8DE2F8F72F
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20526102_2000100674.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20526102_2000100674.Interaction Text
msgctxt ",1468744347121DECD2252F8DE2F8F72F"
msgid "STOP"
msgstr "STOP"

#. Key:	380FD8414E43655EE6F3B4855D2AFD0B
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C206F3802_1454022297.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C206F3802_1454022297.Interaction Text
msgctxt ",380FD8414E43655EE6F3B4855D2AFD0B"
msgid "!!!"
msgstr "!!!"

#. Key:	BB157F95490C9AE2A96B8EBE497DAC22
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C207F3202_1502495671.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C207F3202_1502495671.Interaction Text
msgctxt ",BB157F95490C9AE2A96B8EBE497DAC22"
msgid "Zmień muzykę"
msgstr "Change the music"

#. Key:	38307703456011D127F8A08AC14FE629
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20833802_1689967360.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20833802_1689967360.Interaction Text
msgctxt ",38307703456011D127F8A08AC14FE629"
msgid "Otwórz prezent"
msgstr "Open a gift"

#. Key:	22B097E748307438311D96A957F225CC
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C209E6102_1242077861.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C209E6102_1242077861.Interaction Text
msgctxt ",22B097E748307438311D96A957F225CC"
msgid "PLAY"
msgstr "GIOCARE"

#. Key:	399E34BE456AAE867B10C5AC4EDCA164
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C209E6102_1242080862.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C209E6102_1242080862.Interaction Text
msgctxt ",399E34BE456AAE867B10C5AC4EDCA164"
msgid "STOP"
msgstr "STOP"

#. Key:	457818A8420033D50FD5A1B39CBC5BDC
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20B16602_1431833394.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20B16602_1431833394.Interaction Text
msgctxt ",457818A8420033D50FD5A1B39CBC5BDC"
msgid "Wymień $ na złoto"
msgstr "Convert $ to gold"

#. Key:	6F68503446211E557A5C1D97BF941A78
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20B16602_1460115398.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20B16602_1460115398.Interaction Text
msgctxt ",6F68503446211E557A5C1D97BF941A78"
msgid "Wymień $ na złoto"
msgstr "Convert $ to gold"

#. Key:	99DD61A84A3651340B6F6FAD40649638
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20B74F02_1479158449.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20B74F02_1479158449.Interaction Text
msgctxt ",99DD61A84A3651340B6F6FAD40649638"
msgid "PLAY"
msgstr "GIOCARE"

#. Key:	A03201994894E498D50C6F9CBCDA2B68
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20B74F02_1479161450.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20B74F02_1479161450.Interaction Text
msgctxt ",A03201994894E498D50C6F9CBCDA2B68"
msgid "STOP"
msgstr "STOP"

#. Key:	AA03A8EB48F558B9EE776C8A775722D2
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20BF4F02_2091272070.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20BF4F02_2091272070.Interaction Text
msgctxt ",AA03A8EB48F558B9EE776C8A775722D2"
msgid "PLAY"
msgstr "GIOCARE"

#. Key:	8A10381842A936A3BD552AA3EAA75DFA
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20BF4F02_2091276071.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_047C16507C20BF4F02_2091276071.Interaction Text
msgctxt ",8A10381842A936A3BD552AA3EAA75DFA"
msgid "STOP"
msgstr "STOP"

#. Key:	2752A50241C3A9513852E6B9B00DCEC6
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_9C6B0031E888206602_1743409896.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_9C6B0031E888206602_1743409896.Interaction Text
msgctxt ",2752A50241C3A9513852E6B9B00DCEC6"
msgid "Wymień $ na złoto"
msgstr "Convert $ to gold"

#. Key:	6C72B7954BE05FA2A489D685FFB25EB4
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_9C6B0031E888563502_1655694639.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_9C6B0031E888563502_1655694639.Interaction Text
msgctxt ",6C72B7954BE05FA2A489D685FFB25EB4"
msgid "Pieniądze x2"
msgstr "Money x2"

#. Key:	13B630784C0F6503335481BD9FD05AE6
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_9C6B0031E888BB4F02_1458065868.Interaction Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Button_V2_C_UAID_9C6B0031E888BB4F02_1458065868.Interaction Text
msgctxt ",13B630784C0F6503335481BD9FD05AE6"
msgid "Play"
msgstr "Igraj"

#. Key:	D87A01574EA5A674E36718A8412D1038
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Character_V2_C_UAID_047C16507C202A6302_1490961448.InteractText
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Character_V2_C_UAID_047C16507C202A6302_1490961448.InteractText
msgctxt ",D87A01574EA5A674E36718A8412D1038"
msgid "Porozmawiaj"
msgstr "Talk"

#. Key:	CC942A47413DA757B9790388A0D98B65
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Character_V2_C_UAID_047C16507C203B6202_1161716395.InteractText
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Character_V2_C_UAID_047C16507C203B6202_1161716395.InteractText
msgctxt ",CC942A47413DA757B9790388A0D98B65"
msgid "Porozmawiaj"
msgstr "Talk"

#. Key:	9838C2AE4D30B97DBB42AEAB48A82131
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Character_V2_C_UAID_047C16507C203B6202_1423179396.InteractText
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Character_V2_C_UAID_047C16507C203B6202_1423179396.InteractText
msgctxt ",9838C2AE4D30B97DBB42AEAB48A82131"
msgid "Porozmawiaj"
msgstr "Talk"

#. Key:	43C461F64E9FA3191E805498E3D0C650
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Character_V2_C_UAID_047C16507C203B6202_1534210404.InteractText
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Character_V2_C_UAID_047C16507C203B6202_1534210404.InteractText
msgctxt ",43C461F64E9FA3191E805498E3D0C650"
msgid "Porozmawiaj"
msgstr "Talk"

#. Key:	9608785E45A79B67487640A687B5161A
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Character_V2_C_UAID_047C16507C203F6202_1885567113.InteractText
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Character_V2_C_UAID_047C16507C203F6202_1885567113.InteractText
msgctxt ",9608785E45A79B67487640A687B5161A"
msgid "Porozmawiaj"
msgstr "Talk"

#. Key:	A0F0000C40A47ACD8379F4AE1B323719
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Character_V2_C_UAID_047C16507C20416202_2057361483.InteractText
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Character_V2_C_UAID_047C16507C20416202_2057361483.InteractText
msgctxt ",A0F0000C40A47ACD8379F4AE1B323719"
msgid "Porozmawiaj"
msgstr "Talk"

#. Key:	DAC66AFA4E9F8DDDB1E08E9709562A33
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Character_V2_C_UAID_047C16507C208D6802_1123570168.InteractText
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Character_V2_C_UAID_047C16507C208D6802_1123570168.InteractText
msgctxt ",DAC66AFA4E9F8DDDB1E08E9709562A33"
msgid "Porozmawiaj"
msgstr "Talk"

#. Key:	D739770841FE1CF1872BD1A6BBF570CE
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Character_V2_C_UAID_047C16507C20926802_1845297055.InteractText
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Character_V2_C_UAID_047C16507C20926802_1845297055.InteractText
msgctxt ",D739770841FE1CF1872BD1A6BBF570CE"
msgid "Porozmawiaj"
msgstr "Talk"

#. Key:	DF5C76FA42B0549A2BE2D8AFAEF0EE8E
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Character_V2_C_UAID_047C16507C20976702_1105947937.InteractText
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Character_V2_C_UAID_047C16507C20976702_1105947937.InteractText
msgctxt ",DF5C76FA42B0549A2BE2D8AFAEF0EE8E"
msgid "Porozmawiaj"
msgstr "Talk"

#. Key:	C27D24F0439A1F5C222F4B9B77FAAD16
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Character_V2_C_UAID_047C16507C20DB6802_1981906919.InteractText
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Character_V2_C_UAID_047C16507C20DB6802_1981906919.InteractText
msgctxt ",C27D24F0439A1F5C222F4B9B77FAAD16"
msgid "Porozmawiaj"
msgstr "Talk"

#. Key:	BADCFB9B414D23B00029B7B82926051B
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Character_V2_C_UAID_047C16507C20E86102_1479383794.InteractText
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Character_V2_C_UAID_047C16507C20E86102_1479383794.InteractText
msgctxt ",BADCFB9B414D23B00029B7B82926051B"
msgid "Porozmawiaj"
msgstr "Talk"

#. Key:	7194C5F1497E3BAB503ADAAB48D8CE70
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_ConditionalButton_V2_C_UAID_047C16507C207E6302_2042800240.LockHologram_11.Display Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_ConditionalButton_V2_C_UAID_047C16507C207E6302_2042800240.LockHologram_11.Display Name
msgctxt ",7194C5F1497E3BAB503ADAAB48D8CE70"
msgid "Stone"
msgstr "Stone"

#. Key:	182B6FC24CA7CCE45B37C48DCD9E5AF0
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_ConditionalButton_V2_C_UAID_047C16507C20816302_2071581761.LockHologram_11.Display Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_ConditionalButton_V2_C_UAID_047C16507C20816302_2071581761.LockHologram_11.Display Name
msgctxt ",182B6FC24CA7CCE45B37C48DCD9E5AF0"
msgid "Wood"
msgstr "Wood"

#. Key:	E4F59416420B478FCFE02DBE22C8EC69
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_ConditionalButton_V2_C_UAID_047C16507C20846302_1092897320.LockHologram_11.Display Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_ConditionalButton_V2_C_UAID_047C16507C20846302_1092897320.LockHologram_11.Display Name
msgctxt ",E4F59416420B478FCFE02DBE22C8EC69"
msgid "Coal"
msgstr "Coal"

#. Key:	D3017386442FB3BB0D795680BA2ECFD0
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_ConditionalButton_V2_C_UAID_047C16507C20B16602_1953292413.LockHologram_11.Display Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_ConditionalButton_V2_C_UAID_047C16507C20B16602_1953292413.LockHologram_11.Display Name
msgctxt ",D3017386442FB3BB0D795680BA2ECFD0"
msgid "Diamond"
msgstr "Diamant"

#. Key:	3A1028394B99FD0685BD54B0BB72A06D
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_ConditionalButton_V2_C_UAID_047C16507C20F46102_1264844967.LockHologram_11.Display Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_ConditionalButton_V2_C_UAID_047C16507C20F46102_1264844967.LockHologram_11.Display Name
msgctxt ",3A1028394B99FD0685BD54B0BB72A06D"
msgid "Gold"
msgstr "Gull"

#. Key:	38BCF6B042E5BAF578BC62BC2F10E554
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C202A6302_1540597449.ConversationMaxedName
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C202A6302_1540597449.ConversationMaxedName
msgctxt ",38BCF6B042E5BAF578BC62BC2F10E554"
msgid "Nie moge teraz rozmawiać"
msgstr "I can't talk right now"

#. Key:	4B6CF597400E351D350D15BA45F5A43A
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C202A6302_1540597449.Display Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C202A6302_1540597449.Display Name
msgctxt ",4B6CF597400E351D350D15BA45F5A43A"
msgid "Asystent - Sklep budowlany"
msgstr "Assistant - Building Shop"

#. Key:	3D5881FD4BBD954F7BEA4188D4C07E63
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C203B6202_1666701406.ConversationMaxedName
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C203B6202_1666701406.ConversationMaxedName
msgctxt ",3D5881FD4BBD954F7BEA4188D4C07E63"
msgid "Nie moge teraz rozmawiać"
msgstr "I can't talk right now"

#. Key:	9F67DC9E47731E79D02ED99887C85033
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C203B6202_1666701406.Display Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C203B6202_1666701406.Display Name
msgctxt ",9F67DC9E47731E79D02ED99887C85033"
msgid "Asystent konta oszczędnościowego"
msgstr "Savings Account Assistant"

#. Key:	F088CD5A46C08DF11280A286CABDA203
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C203D6202_1776514759.ConversationMaxedName
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C203D6202_1776514759.ConversationMaxedName
msgctxt ",F088CD5A46C08DF11280A286CABDA203"
msgid "Nie moge teraz rozmawiać"
msgstr "I can't talk right now"

#. Key:	375D403D4AB86D1460B8DF9ADFA9AC80
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C203D6202_1776514759.Display Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C203D6202_1776514759.Display Name
msgctxt ",375D403D4AB86D1460B8DF9ADFA9AC80"
msgid "Asystent skarbonki"
msgstr "Piggy bank assistant"

#. Key:	02C2286A48C9447F09CD18B63150D85B
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C203E6202_1617287936.ConversationMaxedName
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C203E6202_1617287936.ConversationMaxedName
msgctxt ",02C2286A48C9447F09CD18B63150D85B"
msgid "Nie moge teraz rozmawiać"
msgstr "I can't talk right now"

#. Key:	65D10FE9498CAA8B86E6CCBC399F496D
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C203E6202_1617287936.Display Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C203E6202_1617287936.Display Name
msgctxt ",65D10FE9498CAA8B86E6CCBC399F496D"
msgid "Asystent w skarbcu"
msgstr "Assistant in the treasury"

#. Key:	908F9B5F44BB542E4B8A43BD1042C833
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C203F6202_1885577114.ConversationMaxedName
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C203F6202_1885577114.ConversationMaxedName
msgctxt ",908F9B5F44BB542E4B8A43BD1042C833"
msgid "Nie moge teraz rozmawiać"
msgstr "I can't talk right now"

#. Key:	E1E6F66F4D179F601FD5069D4C024800
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C203F6202_1885577114.Display Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C203F6202_1885577114.Display Name
msgctxt ",E1E6F66F4D179F601FD5069D4C024800"
msgid "Asystent - Jubiler"
msgstr "Assistant - Jeweler"

#. Key:	CD19D72E4C8FF796E2A77AAC383F48D7
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C20426202_1097458662.ConversationMaxedName
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C20426202_1097458662.ConversationMaxedName
msgctxt ",CD19D72E4C8FF796E2A77AAC383F48D7"
msgid "Nie moge teraz rozmawiać"
msgstr "I can't talk right now"

#. Key:	08351F5E4D10839BB9DBA6B19B407A1B
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C20426202_1097458662.Display Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C20426202_1097458662.Display Name
msgctxt ",08351F5E4D10839BB9DBA6B19B407A1B"
msgid "Asystent - Jubiler"
msgstr "Assistant - Jeweler"

#. Key:	3F16B54248A2EDABF5136EA61FA39138
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C208D6802_1152413170.ConversationMaxedName
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C208D6802_1152413170.ConversationMaxedName
msgctxt ",3F16B54248A2EDABF5136EA61FA39138"
msgid "Nie moge teraz rozmawiać"
msgstr "I can't talk right now"

#. Key:	50B8A62B48BCD5388DC2EF8E47495BCB
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C208D6802_1152413170.Display Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C208D6802_1152413170.Display Name
msgctxt ",50B8A62B48BCD5388DC2EF8E47495BCB"
msgid "Kredyty i pozyczki"
msgstr "Loans and Loans"

#. Key:	C687E9FE4600CD0BD2F7EBA0EB794EB3
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C20926802_1845313056.ConversationMaxedName
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C20926802_1845313056.ConversationMaxedName
msgctxt ",C687E9FE4600CD0BD2F7EBA0EB794EB3"
msgid "Nie moge teraz rozmawiać"
msgstr "I can't talk right now"

#. Key:	76D3EF2040D9B69B4AE3DE9B7834A5F3
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C20926802_1845313056.Display Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C20926802_1845313056.Display Name
msgctxt ",76D3EF2040D9B69B4AE3DE9B7834A5F3"
msgid "Mr. Carman"
msgstr "Herr Carman"

#. Key:	455FDBAF4AF99E53DBB21B8DAA728773
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C20976702_1105963938.ConversationMaxedName
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C20976702_1105963938.ConversationMaxedName
msgctxt ",455FDBAF4AF99E53DBB21B8DAA728773"
msgid "Nie moge teraz rozmawiać"
msgstr "I can't talk right now"

#. Key:	637B7A1D46E191D4D5229EAAA1C31959
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C20976702_1105963938.Display Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C20976702_1105963938.Display Name
msgctxt ",637B7A1D46E191D4D5229EAAA1C31959"
msgid "Mr. Carman"
msgstr "Herr Carman"

#. Key:	4C6B661F441B16223611BEA892EA317F
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C20DB6802_1981936920.ConversationMaxedName
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C20DB6802_1981936920.ConversationMaxedName
msgctxt ",4C6B661F441B16223611BEA892EA317F"
msgid "Nie moge teraz rozmawiać"
msgstr "I can't talk right now"

#. Key:	310E57F847FEFE8DF9D2CEA4AD4E79C7
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C20DB6802_1981936920.Display Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C20DB6802_1981936920.Display Name
msgctxt ",310E57F847FEFE8DF9D2CEA4AD4E79C7"
msgid "Asystent Gry"
msgstr "Game Assistant"

#. Key:	42E4154943CE1689DAA617BB18C073F8
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C20E86102_1425870790.ConversationMaxedName
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C20E86102_1425870790.ConversationMaxedName
msgctxt ",42E4154943CE1689DAA617BB18C073F8"
msgid "Nie moge teraz rozmawiać"
msgstr "I can't talk right now"

#. Key:	25A7AB6F478BC85D064CBC9C576CD8D8
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C20E86102_1425870790.Display Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_047C16507C20E86102_1425870790.Display Name
msgctxt ",25A7AB6F478BC85D064CBC9C576CD8D8"
msgid "Asystent - Sklep budowlany"
msgstr "Assistant - Building Shop"

#. Key:	6F79368B4FC5C9C5F51CE488A641587D
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_9C6B0031E888DC6802_2129678053.ConversationMaxedName
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_9C6B0031E888DC6802_2129678053.ConversationMaxedName
msgctxt ",6F79368B4FC5C9C5F51CE488A641587D"
msgid "Nie moge teraz rozmawiać"
msgstr "I can't talk right now"

#. Key:	4E33A19D49CAAFE541A4CC819274FC23
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_9C6B0031E888DC6802_2129678053.Display Name
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Conversation_C_UAID_9C6B0031E888DC6802_2129678053.Display Name
msgctxt ",4E33A19D49CAAFE541A4CC819274FC23"
msgid "Kredyty i pozyczki"
msgstr "Loans and Loans"

#. Key:	715DE60E4A83D48ED96F8CA84CECC9A1
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_HUDMessage_V2_C_UAID_9C6B0031E888894902_1436048727.Message
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_HUDMessage_V2_C_UAID_9C6B0031E888894902_1436048727.Message
msgctxt ",715DE60E4A83D48ED96F8CA84CECC9A1"
msgid "{instigatorname}, JUMP FROM THE HELI!"
msgstr "{instigatorname}, JUMP FROM THE HELI!"

#. Key:	1E0E928D43345265D9C43EA837B9C84D
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_HUDMessage_V2_C_UAID_9C6B0031E888894F02_1090707131.Message
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_HUDMessage_V2_C_UAID_9C6B0031E888894F02_1090707131.Message
msgctxt ",1E0E928D43345265D9C43EA837B9C84D"
msgid "{instigatorname}, congrats, here is your rebirth bonus!"
msgstr "{instigatorname}, congrats, here is your rebirth bonus!"

#. Key:	852714B640E4869620F9F38A1D962CF5
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_HUDMessage_V2_C_UAID_9C6B0031E888894F02_1334132132.Message
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_HUDMessage_V2_C_UAID_9C6B0031E888894F02_1334132132.Message
msgctxt ",852714B640E4869620F9F38A1D962CF5"
msgid "Build bank on another island and keep earning points!"
msgstr "Build a bank on another island and keep earning points!"

#. Key:	8E36C19D4CA00C49B42C80B167982A8A
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_PopUpDialog_V2_C_UAID_9C6B0031E888D94D02_2000390056.Button1Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_PopUpDialog_V2_C_UAID_9C6B0031E888D94D02_2000390056.Button1Text
msgctxt ",8E36C19D4CA00C49B42C80B167982A8A"
msgid "Cancel"
msgstr "Cancelar"

#. Key:	A3EBA8FE419655B2CEFEB589A03ED3F1
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_PopUpDialog_V2_C_UAID_9C6B0031E888D94D02_2000390056.Button2Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_PopUpDialog_V2_C_UAID_9C6B0031E888D94D02_2000390056.Button2Text
msgctxt ",A3EBA8FE419655B2CEFEB589A03ED3F1"
msgid "OK"
msgstr "OK"

#. Key:	F72EC41040E76FBB8F3088AA632DA6E3
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_PopUpDialog_V2_C_UAID_9C6B0031E888D94D02_2000390056.Title
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_PopUpDialog_V2_C_UAID_9C6B0031E888D94D02_2000390056.Title
msgctxt ",F72EC41040E76FBB8F3088AA632DA6E3"
msgid "Rebirth?"
msgstr "Rebirth?"

#. Key:	0F677AA54DEF2453E9AD4D924D9E0BDF
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_SkilledInteractionDevice_C_UAID_047C16507C20146002_1805930546.HeaderText
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_SkilledInteractionDevice_C_UAID_047C16507C20146002_1805930546.HeaderText
msgctxt ",0F677AA54DEF2453E9AD4D924D9E0BDF"
msgid "Opening Safe"
msgstr "Opening Safe"

#. Key:	094D33FB4E3CD99E33E0AEBF3D70FE06
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_SkilledInteractionDevice_C_UAID_047C16507C20516102_1700672046.HeaderText
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_SkilledInteractionDevice_C_UAID_047C16507C20516102_1700672046.HeaderText
msgctxt ",094D33FB4E3CD99E33E0AEBF3D70FE06"
msgid "Piggy Gold"
msgstr "Piggy Gold"

#. Key:	94C089BC44D05C239427808FF8AD7539
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_SkilledInteractionDevice_C_UAID_9C6B0031E888BB4F02_1405490867.HeaderText
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_SkilledInteractionDevice_C_UAID_9C6B0031E888BB4F02_1405490867.HeaderText
msgctxt ",94C089BC44D05C239427808FF8AD7539"
msgid "Minigame"
msgstr "Minigame"

#. Key:	7BC2AA904680A7EB8401EDABC92277C9
#. SourceLocation:	/Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Timer_V2_C_UAID_9C6B0031E8885C4B02_1884205902.PrimaryTextWidget.Text
#: /Pekao_Bank/Pekao_Bank.Pekao_Bank:PersistentLevel.Device_Timer_V2_C_UAID_9C6B0031E8885C4B02_1884205902.PrimaryTextWidget.Text
msgctxt ",7BC2AA904680A7EB8401EDABC92277C9"
msgid "TIMER TEST NAME"
msgstr "TIMER TEST NAME"

#. Key:	9906F0AB48BD426AF25F2AA7EE4B13A9
#. SourceLocation:	/Pekao_Bank/VLeaderboard/UI/LeaderboardLandingPageUi.LeaderboardLandingPageUi_C:WidgetTree.TextPoints.Text
#: /Pekao_Bank/VLeaderboard/UI/LeaderboardLandingPageUi.LeaderboardLandingPageUi_C:WidgetTree.TextPoints.Text
msgctxt ",9906F0AB48BD426AF25F2AA7EE4B13A9"
msgid "Points: 9999"
msgstr "Bodov: 9999"

#. Key:	481C378646FCBFB561A184980A1ABAFB
#. SourceLocation:	/Pekao_Bank/VLeaderboard/UI/LeaderboardLandingPageUi.LeaderboardLandingPageUi_C:WidgetTree.TextVerification.Text
#: /Pekao_Bank/VLeaderboard/UI/LeaderboardLandingPageUi.LeaderboardLandingPageUi_C:WidgetTree.TextVerification.Text
msgctxt ",481C378646FCBFB561A184980A1ABAFB"
msgid "Verification Code:\r\n9999"
msgstr "Verifikationskode:\n9999"

#. Key:	C5E324C54972CC4AB2D7C4B4D4E80996
#. SourceLocation:	/Pekao_Bank/VLocalization/Assets/WB_LangSelect1.WB_LangSelect1_C:WidgetTree.UEFN_TextBlock_C.Text
#: /Pekao_Bank/VLocalization/Assets/WB_LangSelect1.WB_LangSelect1_C:WidgetTree.UEFN_TextBlock_C.Text
msgctxt ",C5E324C54972CC4AB2D7C4B4D4E80996"
msgid "Język angielski"
msgstr "English language"

#. Key:	DBE5354F43D266749EA7548FFF2FB8D0
#. SourceLocation:	/Pekao_Bank/VMinigames/Assets/WBP_MinigameResult.WBP_MinigameResult_C:WidgetTree.UEFN_TextBlock_C.Text
#: /Pekao_Bank/VMinigames/Assets/WBP_MinigameResult.WBP_MinigameResult_C:WidgetTree.UEFN_TextBlock_C.Text
msgctxt ",DBE5354F43D266749EA7548FFF2FB8D0"
msgid "PT"
msgstr "PT"

#. Key:	F6040F5144AF309EDB77818BE5C62173
#. SourceLocation:	/Pekao_Bank/VMinigames/Assets/WBP_MinigameResult.WBP_MinigameResult_C:WidgetTree.UEFN_TextBlock_C_39.Text
#: /Pekao_Bank/VMinigames/Assets/WBP_MinigameResult.WBP_MinigameResult_C:WidgetTree.UEFN_TextBlock_C_39.Text
msgctxt ",F6040F5144AF309EDB77818BE5C62173"
msgid "100"
msgstr "100"

#. Key:	9AEE118348EA629C3B9C04AE2F4320DE
#. SourceLocation:	/Pekao_Bank/VMinigames/WBP_OneButtonEndTime.WBP_OneButtonEndTime_C:WidgetTree.QuestionText.Text
#: /Pekao_Bank/VMinigames/WBP_OneButtonEndTime.WBP_OneButtonEndTime_C:WidgetTree.QuestionText.Text
msgctxt ",9AEE118348EA629C3B9C04AE2F4320DE"
msgid "Cały bank został odblokowany w 10:55\r\nSpróbuj jeszcze lepiej wykorzystać i zostań królem finansowym!"
msgstr "The entire bank was unlocked at 10:55\nTry to make even better use and become the financial king!"

#. Key:	27F64335479AB8BF363A8E9FC00BB905
#. SourceLocation:	/Pekao_Bank/VMinigames/WBP_OneButtonEndTime.WBP_OneButtonEndTime_C:WidgetTree.Txt3.Text
#: /Pekao_Bank/VMinigames/WBP_OneButtonEndTime.WBP_OneButtonEndTime_C:WidgetTree.Txt3.Text
msgctxt ",27F64335479AB8BF363A8E9FC00BB905"
msgid "Okej"
msgstr "Okay"

#. Key:	F6966187439108B812FF9C9DBEF9A77C
#. SourceLocation:	/Pekao_Bank/VMinigames/WBP_Quiz.WBP_Quiz_C:WidgetTree.QuestionText.Text
#: /Pekao_Bank/VMinigames/WBP_Quiz.WBP_Quiz_C:WidgetTree.QuestionText.Text
msgctxt ",F6966187439108B812FF9C9DBEF9A77C"
msgid "10:9910:9910:9 910:9910:9910:9910:9910:9910:9910:991 1sf 0:9910:990:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:991 0:9910:9910:9910:99"
msgstr "10:9910:9910:9 910:9910:9910:9910:9910:9910:9910:9910:991 1sf 0:9910:990:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:991 0:9910:9910:9910:99"

#. Key:	DB38AAB4450D8DCBC9923D9ED4802357
#. SourceLocation:	/Pekao_Bank/VMinigames/WBP_Quiz.WBP_Quiz_C:WidgetTree.Txt1.Text
#: /Pekao_Bank/VMinigames/WBP_Quiz.WBP_Quiz_C:WidgetTree.Txt1.Text
msgctxt ",DB38AAB4450D8DCBC9923D9ED4802357"
msgid "Tekst Blockfasdfsdas"
msgstr "Blockfasdfsdas Text"

#. Key:	8E6531254EF1CD91122FC794E41B2A37
#. SourceLocation:	/Pekao_Bank/VMinigames/WBP_QuizBadAnswer.WBP_QuizBadAnswer_C:WidgetTree.QuestionText.Text
#: /Pekao_Bank/VMinigames/WBP_QuizBadAnswer.WBP_QuizBadAnswer_C:WidgetTree.QuestionText.Text
msgctxt ",8E6531254EF1CD91122FC794E41B2A37"
msgid "ZŁA ODPOWIEDŹ :("
msgstr "WRONG ANSWER :("

#. Key:	BD91C59F4D5A37C90CC48FBA377FC99C
#. SourceLocation:	/Pekao_Bank/VMinigames/WBP_QuizBadAnswer.WBP_QuizBadAnswer_C:WidgetTree.Txt3.Text
#: /Pekao_Bank/VMinigames/WBP_QuizBadAnswer.WBP_QuizBadAnswer_C:WidgetTree.Txt3.Text
msgctxt ",BD91C59F4D5A37C90CC48FBA377FC99C"
msgid "Tekst Blockfasdfsdas"
msgstr "Blockfasdfsdas Text"

#. Key:	40C16B714CC370C430DBAD98C661B638
#. SourceLocation:	/Pekao_Bank/VMinigames/WBP_QuizGoodAnswer.WBP_QuizGoodAnswer_C:WidgetTree.QuestionText.Text
#: /Pekao_Bank/VMinigames/WBP_QuizGoodAnswer.WBP_QuizGoodAnswer_C:WidgetTree.QuestionText.Text
msgctxt ",40C16B714CC370C430DBAD98C661B638"
msgid "DOBRY ANWSER, GRATULACJE!"
msgstr "GOOD ANWSER, CONGRATULATIONS!"

#. Key:	F8F0829D40828EAC1BC53582D89E9DB2
#. SourceLocation:	/Pekao_Bank/VMinigames/WBP_QuizGoodAnswer.WBP_QuizGoodAnswer_C:WidgetTree.Txt3.Text
#: /Pekao_Bank/VMinigames/WBP_QuizGoodAnswer.WBP_QuizGoodAnswer_C:WidgetTree.Txt3.Text
msgctxt ",F8F0829D40828EAC1BC53582D89E9DB2"
msgid "Tekst Blockfasdfsdas"
msgstr "Blockfasdfsdas Text"

#. Key:	08CCAB50409F4E46A9A475857777ADAE
#. SourceLocation:	/Pekao_Bank/VMinigames/WBP_QuizOneButton.WBP_QuizOneButton_C:WidgetTree.QuestionText.Text
#: /Pekao_Bank/VMinigames/WBP_QuizOneButton.WBP_QuizOneButton_C:WidgetTree.QuestionText.Text
msgctxt ",08CCAB50409F4E46A9A475857777ADAE"
msgid "10:9910:9910:9 910:9910:9910:9910:9910:9910:9910:991 1sf 0:9910:990:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:991 0:9910:9910:9910:99"
msgstr "10:9910:9910:9 910:9910:9910:9910:9910:9910:9910:9910:991 1sf 0:9910:990:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:9910:991 0:9910:9910:9910:99"

#. Key:	BCE2618547EDC96EFAB322AD3EA7A24B
#. SourceLocation:	/Pekao_Bank/VMinigames/WBP_QuizOneButton.WBP_QuizOneButton_C:WidgetTree.Txt3.Text
#: /Pekao_Bank/VMinigames/WBP_QuizOneButton.WBP_QuizOneButton_C:WidgetTree.Txt3.Text
msgctxt ",BCE2618547EDC96EFAB322AD3EA7A24B"
msgid "Tekst Blockfasdfsdas"
msgstr "Blockfasdfsdas Text"

#. Key:	61FA7CCF4AF931E5A7B190BA7D84B772
#. SourceLocation:	/Pekao_Bank/VNews/Assets/WB_News.WB_News_C:WidgetTree.UEFN_TextBlock_C.Text
#: /Pekao_Bank/VNews/Assets/WB_News.WB_News_C:WidgetTree.UEFN_TextBlock_C.Text
msgctxt ",61FA7CCF4AF931E5A7B190BA7D84B772"
msgid "AKTUALIZACJA 1.2"
msgstr "UPDATE 1.2"

#. Key:	479AD1F44D73E369917AB1B445F59E6F
#. SourceLocation:	/Pekao_Bank/VNews/Assets/WB_News.WB_News_C:WidgetTree.UEFN_TextBlock_C_1.Text
#: /Pekao_Bank/VNews/Assets/WB_News.WB_News_C:WidgetTree.UEFN_TextBlock_C_1.Text
msgctxt ",479AD1F44D73E369917AB1B445F59E6F"
msgid "- Naprawione elementy znikające czasami"
msgstr "- Fixed items disappearing at times"

#. Key:	087D97C84913B2E7BB27059FF04967AB
#. SourceLocation:	/Pekao_Bank/VNews/Assets/WB_News.WB_News_C:WidgetTree.UEFN_TextBlock_C_2.Text
#: /Pekao_Bank/VNews/Assets/WB_News.WB_News_C:WidgetTree.UEFN_TextBlock_C_2.Text
msgctxt ",087D97C84913B2E7BB27059FF04967AB"
msgid "- Dodano mechanikę 3 piłek w 60 minutach czasu serwera"
msgstr "- Added 3 ball mechanics in 60 minutes of server time"

#. Key:	5DD6335842AAEE10E4A147AAF220280A
#. SourceLocation:	/Pekao_Bank/VNews/Assets/WB_News.WB_News_C:WidgetTree.UEFN_TextBlock_C_3.Text
#: /Pekao_Bank/VNews/Assets/WB_News.WB_News_C:WidgetTree.UEFN_TextBlock_C_3.Text
msgctxt ",5DD6335842AAEE10E4A147AAF220280A"
msgid "- Dodano Heal Power Ups w pobliżu baz"
msgstr "- Added Heal Power Ups near Bases"

#. Key:	95B5126A42B18244EB0906A634A9EBF3
#. SourceLocation:	/Pekao_Bank/VNews/Assets/WB_News.WB_News_C:WidgetTree.UEFN_TextBlock_C_4.Text
#: /Pekao_Bank/VNews/Assets/WB_News.WB_News_C:WidgetTree.UEFN_TextBlock_C_4.Text
msgctxt ",95B5126A42B18244EB0906A634A9EBF3"
msgid "PRZYCHODZĄCE W 1.3"
msgstr "INCOMING IN 1.3"

#. Key:	69CD7E29454B339818E826B549520513
#. SourceLocation:	/Pekao_Bank/VNews/Assets/WB_News.WB_News_C:WidgetTree.UEFN_TextBlock_C_5.Text
#: /Pekao_Bank/VNews/Assets/WB_News.WB_News_C:WidgetTree.UEFN_TextBlock_C_5.Text
msgctxt ",69CD7E29454B339818E826B549520513"
msgid "- Wzmocnienia i umiejętności oparte na piłkarzach!"
msgstr "- Boosts and skills based on footballers!"

#. Key:	AB793C8F47E618CC4218D88B191B8950
#. SourceLocation:	/Pekao_Bank/VNews/Assets/WB_News.WB_News_C:WidgetTree.UEFN_TextBlock_C_6.Text
#: /Pekao_Bank/VNews/Assets/WB_News.WB_News_C:WidgetTree.UEFN_TextBlock_C_6.Text
msgctxt ",AB793C8F47E618CC4218D88B191B8950"
msgid "- Dodano wybór zespołu na początku i w pobliżu baz"
msgstr "- Added team selection at the start and near the bases"

#. Key:	6D5F60B2438E1D2BFCA1EB8E48F88944
#. SourceLocation:	/Pekao_Bank/VNews/Assets/WB_News.WB_News_C:WidgetTree.UEFN_TextBlock_C_7.Text
#: /Pekao_Bank/VNews/Assets/WB_News.WB_News_C:WidgetTree.UEFN_TextBlock_C_7.Text
msgctxt ",6D5F60B2438E1D2BFCA1EB8E48F88944"
msgid "- Naprawiono eliminacje i rangi, które nie są zapisywane"
msgstr "- Fixed eliminations and ranks not being saved"

#. Key:	0E9F255C41BAFD1328B614B2F4EF5798
#. SourceLocation:	/Pekao_Bank/VNews/Assets/WB_News1.WB_News1_C:WidgetTree.UEFN_TextBlock_C.Text
#: /Pekao_Bank/VNews/Assets/WB_News1.WB_News1_C:WidgetTree.UEFN_TextBlock_C.Text
msgctxt ",0E9F255C41BAFD1328B614B2F4EF5798"
msgid "- Naprawiono czasami znikanie piłki nożnej"
msgstr "- Fixed occasional disappearances of football"

#. Key:	CA1947844512B5BEC02AFB82ED7A0B4A
#. SourceLocation:	/Pekao_Bank/VNews/Assets/WB_News1.WB_News1_C:WidgetTree.UEFN_TextBlock_C_1.Text
#: /Pekao_Bank/VNews/Assets/WB_News1.WB_News1_C:WidgetTree.UEFN_TextBlock_C_1.Text
msgctxt ",CA1947844512B5BEC02AFB82ED7A0B4A"
msgid "- Naprawiono leczenie do kupienia"
msgstr "- Fixed a treatment to buy"

#. Key:	C0EEF2E6477963719950128210DCA7B4
#. SourceLocation:	/Pekao_Bank/VNews/Assets/WB_News1.WB_News1_C:WidgetTree.UEFN_TextBlock_C_2.Text
#: /Pekao_Bank/VNews/Assets/WB_News1.WB_News1_C:WidgetTree.UEFN_TextBlock_C_2.Text
msgctxt ",C0EEF2E6477963719950128210DCA7B4"
msgid "- Codzienne nagrody Playtime Xp (Reset poprawiony w 1.41!)"
msgstr "- Daily rewards Playtime Xp (Reset fixed in 1.41!)"

#. Key:	FCBDEE2B4C7BA5F63E0CCBAFA242B649
#. SourceLocation:	/Pekao_Bank/VNews/Assets/WB_News1.WB_News1_C:WidgetTree.UEFN_TextBlock_C_3.Text
#: /Pekao_Bank/VNews/Assets/WB_News1.WB_News1_C:WidgetTree.UEFN_TextBlock_C_3.Text
msgctxt ",FCBDEE2B4C7BA5F63E0CCBAFA242B649"
msgid "- Przetworniki kolekcjonerskie Xp"
msgstr "- Xp Collector Transducers"

#. Key:	29C95D1944FE951CC03A73804CA83E53
#. SourceLocation:	/Pekao_Bank/VNews/Assets/WB_News1.WB_News1_C:WidgetTree.UEFN_TextBlock_C_4.Text
#: /Pekao_Bank/VNews/Assets/WB_News1.WB_News1_C:WidgetTree.UEFN_TextBlock_C_4.Text
msgctxt ",29C95D1944FE951CC03A73804CA83E53"
msgid "AKTUALIZACJA 1.3"
msgstr "UPDATE 1.3"

#. Key:	C09ADDF346BA13737A24AE859355332B
#. SourceLocation:	/Pekao_Bank/VNews/Assets/WB_News1.WB_News1_C:WidgetTree.UEFN_TextBlock_C_5.Text
#: /Pekao_Bank/VNews/Assets/WB_News1.WB_News1_C:WidgetTree.UEFN_TextBlock_C_5.Text
msgctxt ",C09ADDF346BA13737A24AE859355332B"
msgid "- Wzmocnienia i umiejętności oparte na piłkarzach!"
msgstr "- Boosts and skills based on footballers!"

#. Key:	94672C5F44E5BB3D11AAB791B03409ED
#. SourceLocation:	/Pekao_Bank/VNews/Assets/WB_News1.WB_News1_C:WidgetTree.UEFN_TextBlock_C_6.Text
#: /Pekao_Bank/VNews/Assets/WB_News1.WB_News1_C:WidgetTree.UEFN_TextBlock_C_6.Text
msgctxt ",94672C5F44E5BB3D11AAB791B03409ED"
msgid "- Eliminacja botów Grants Xp"
msgstr "- Elimination of Grants Xp bots"

#. Key:	1FE9FDD7446F8AA88544B598065B099E
#. SourceLocation:	/Pekao_Bank/VNews/Assets/WB_News1.WB_News1_C:WidgetTree.UEFN_TextBlock_C_8.Text
#: /Pekao_Bank/VNews/Assets/WB_News1.WB_News1_C:WidgetTree.UEFN_TextBlock_C_8.Text
msgctxt ",1FE9FDD7446F8AA88544B598065B099E"
msgid "AKTUALIZACJA 1.41"
msgstr "UPDATE 1.41"

#. Key:	A0AF9E314E54A8C770EC749639283CEA
#. SourceLocation:	/Pekao_Bank/VNotifications/WBP_BigTopNotification.WBP_BigTopNotification_C:WidgetTree.UEFN_TextBlock_C.Text
#: /Pekao_Bank/VNotifications/WBP_BigTopNotification.WBP_BigTopNotification_C:WidgetTree.UEFN_TextBlock_C.Text
msgctxt ",A0AF9E314E54A8C770EC749639283CEA"
msgid "PT"
msgstr "PT"

#. Key:	996919CD4DC52129E0468DA4DCF5F5B6
#. SourceLocation:	/Pekao_Bank/VNotifications/WBP_BigTopNotification.WBP_BigTopNotification_C:WidgetTree.UEFN_TextBlock_C_39.Text
#: /Pekao_Bank/VNotifications/WBP_BigTopNotification.WBP_BigTopNotification_C:WidgetTree.UEFN_TextBlock_C_39.Text
msgctxt ",996919CD4DC52129E0468DA4DCF5F5B6"
msgid "100"
msgstr "100"

#. Key:	958E35A344CC93451C99708E8263C97B
#. SourceLocation:	/Pekao_Bank/VNotifications/WBP_BigTopNotification1.WBP_BigTopNotification1_C:WidgetTree.UEFN_TextBlock_C_39.Text
#: /Pekao_Bank/VNotifications/WBP_BigTopNotification1.WBP_BigTopNotification1_C:WidgetTree.UEFN_TextBlock_C_39.Text
msgctxt ",958E35A344CC93451C99708E8263C97B"
msgid "Maszyna zatrzymała się -${AmountPerSec}/s"
msgstr "The machine stopped -${AmountPerSec}/s"

#. Key:	8B2B560342AD4411E383DC9725E7C5A2
#. SourceLocation:	/Pekao_Bank/VNotifications/WidgeManyResouceNotifs.WidgeManyResouceNotifs_C:WidgetTree.TextFull.Text
#: /Pekao_Bank/VNotifications/WidgeManyResouceNotifs.WidgeManyResouceNotifs_C:WidgetTree.TextFull.Text
msgctxt ",8B2B560342AD4411E383DC9725E7C5A2"
msgid "Woofffffffd +3fffffffdfsdf"
msgstr "Woofffffffd +3fffffffffdfsdf"

#. Key:	89C22FD8475148185162C1864F0A4E7E
#. SourceLocation:	/Pekao_Bank/VNotifications/WidgeManyResouceNotifs.WidgeManyResouceNotifs_C:WidgetTree.TextRes.Text
#: /Pekao_Bank/VNotifications/WidgeManyResouceNotifs.WidgeManyResouceNotifs_C:WidgetTree.TextRes.Text
msgctxt ",89C22FD8475148185162C1864F0A4E7E"
msgid "🌲"
msgstr "🌲"

#. Key:	44B12CCE48E619E8BF1276944047D75D
#. SourceLocation:	/Pekao_Bank/VPhone/Assets/WB_Popup_PhoneAchievments2_Verse.WB_Popup_PhoneAchievments2_Verse_C:WidgetTree.TextQuestDesc.Text
#: /Pekao_Bank/VPhone/Assets/WB_Popup_PhoneAchievments2_Verse.WB_Popup_PhoneAchievments2_Verse_C:WidgetTree.TextQuestDesc.Text
msgctxt ",44B12CCE48E619E8BF1276944047D75D"
msgid "Odbierz swój pierwszy bank!"
msgstr "Pick up your first bank!"

#. Key:	BB7F487A4BDC7532FF12A4A8562CE729
#. SourceLocation:	/Pekao_Bank/VPhone/Assets/WB_Popup_PhoneAchievments2_Verse.WB_Popup_PhoneAchievments2_Verse_C:WidgetTree.TextReward.Text
#: /Pekao_Bank/VPhone/Assets/WB_Popup_PhoneAchievments2_Verse.WB_Popup_PhoneAchievments2_Verse_C:WidgetTree.TextReward.Text
msgctxt ",BB7F487A4BDC7532FF12A4A8562CE729"
msgid "1000"
msgstr "1000"

#. Key:	D9FD1D144AAE69385876FEB8197468FF
#. SourceLocation:	/Pekao_Bank/VPhone/Assets/WB_Popup_PhoneInvest5_Verse.WB_Popup_PhoneInvest5_Verse_C:WidgetTree.TextCash.Text
#: /Pekao_Bank/VPhone/Assets/WB_Popup_PhoneInvest5_Verse.WB_Popup_PhoneInvest5_Verse_C:WidgetTree.TextCash.Text
msgctxt ",D9FD1D144AAE69385876FEB8197468FF"
msgid "$1000"
msgstr "$1000"

#. Key:	618FD4E746E1173ADDCC89B9F3957697
#. SourceLocation:	/Pekao_Bank/VPhone/Assets/WB_Popup_PhoneInvest5_Verse.WB_Popup_PhoneInvest5_Verse_C:WidgetTree.TextInvestReturn.Text
#: /Pekao_Bank/VPhone/Assets/WB_Popup_PhoneInvest5_Verse.WB_Popup_PhoneInvest5_Verse_C:WidgetTree.TextInvestReturn.Text
msgctxt ",618FD4E746E1173ADDCC89B9F3957697"
msgid "Zdobądź 1500 USD z powrotem po 1 minucie"
msgstr "Get $1500 back after 1 minute"

#. Key:	A95A6E2F477DBBA8A245D9A2198E2838
#. SourceLocation:	/Pekao_Bank/VPhone/Assets/WB_Popup_PhoneInvest5_Verse.WB_Popup_PhoneInvest5_Verse_C:WidgetTree.TextTime.Text
#: /Pekao_Bank/VPhone/Assets/WB_Popup_PhoneInvest5_Verse.WB_Popup_PhoneInvest5_Verse_C:WidgetTree.TextTime.Text
msgctxt ",A95A6E2F477DBBA8A245D9A2198E2838"
msgid "1min"
msgstr "1min"

#. Key:	14664DC442ECF87063F6D4A95B545EE8
#. SourceLocation:	/Pekao_Bank/VPhone/Assets/WB_Popup_PhoneReg2_Avatar.WB_Popup_PhoneReg2_Avatar_C:WidgetTree.UEFN_TextBlock_C_43.Text
#: /Pekao_Bank/VPhone/Assets/WB_Popup_PhoneReg2_Avatar.WB_Popup_PhoneReg2_Avatar_C:WidgetTree.UEFN_TextBlock_C_43.Text
msgctxt ",14664DC442ECF87063F6D4A95B545EE8"
msgid "Nazwa graczaFASDFSDF"
msgstr "Player NameFASDFSDF"

#. Key:	71C78994488153F84BA94F9C198327D6
#. SourceLocation:	/Pekao_Bank/VPopup/WBP_Popup1.WBP_Popup1_C:WidgetTree.UEFN_TextBlock_C_132.Text
#: /Pekao_Bank/VPopup/WBP_Popup1.WBP_Popup1_C:WidgetTree.UEFN_TextBlock_C_132.Text
msgctxt ",71C78994488153F84BA94F9C198327D6"
msgid "asdf asdf asd fas d fasd fas dfasd fasdfa sdfasd fas df asdf asdf fasd df asd fasd df asdf lub df asd fasd "
msgstr "asdf asdf asd fas d fasd fas dfasd fasdfa sdfasd fas df asdf asdf asdf fasd df asd f f f asdf "

#. Key:	TextBlockDefaultValue
#. SourceLocation:	/Pekao_Bank/VPopup/WBP_Popup2.WBP_Popup2_C:WidgetTree.TxtBtn1.Text
#: /Pekao_Bank/VPopup/WBP_Popup2.WBP_Popup2_C:WidgetTree.TxtBtn1.Text
msgctxt "UMG,TextBlockDefaultValue"
msgid "Blok tekstu"
msgstr "Text block"

#. Key:	9BDACD834FD3D9B96AD8E6A20A4F823E
#. SourceLocation:	/Pekao_Bank/VPopup/WBP_Popup2.WBP_Popup2_C:WidgetTree.UEFN_TextBlock_C_132.Text
#: /Pekao_Bank/VPopup/WBP_Popup2.WBP_Popup2_C:WidgetTree.UEFN_TextBlock_C_132.Text
msgctxt ",9BDACD834FD3D9B96AD8E6A20A4F823E"
msgid "asdf asdf asd fas d fasd fas dfasd fasdfa sdfasd fas df asdf asdf fasd df asd fasd df asdf lub df asd fasd "
msgstr "asdf asdf asd fas d fasd fas dfasd fasdfa sdfasd fas df asdf asdf asdf fasd df asd f f f asdf "

#. Key:	5F63DEC84348323944FCCCAEC934D071
#. SourceLocation:	/Pekao_Bank/VQuestSystem/WBP_QuestsListGen.WBP_QuestsListGen_C:WidgetTree.TextQuestDesc.Text
#: /Pekao_Bank/VQuestSystem/WBP_QuestsListGen.WBP_QuestsListGen_C:WidgetTree.TextQuestDesc.Text
msgctxt ",5F63DEC84348323944FCCCAEC934D071"
msgid "Odbierz swój pierwszy bank!"
msgstr "Pick up your first bank!"

#. Key:	F7D1F10345603C9540141F8AB54421D0
#. SourceLocation:	/Pekao_Bank/VQuestSystem/WBP_QuestsListGen.WBP_QuestsListGen_C:WidgetTree.TextReward.Text
#: /Pekao_Bank/VQuestSystem/WBP_QuestsListGen.WBP_QuestsListGen_C:WidgetTree.TextReward.Text
msgctxt ",F7D1F10345603C9540141F8AB54421D0"
msgid "100"
msgstr "100"

#. Key:	488FDF324289811BADC5D5B795C653D2
#. SourceLocation:	/Pekao_Bank/VRaceMinigame/UI/WBP_RaceCoinsBar.WBP_RaceCoinsBar_C:WidgetTree.PointsValue.Text
#: /Pekao_Bank/VRaceMinigame/UI/WBP_RaceCoinsBar.WBP_RaceCoinsBar_C:WidgetTree.PointsValue.Text
msgctxt ",488FDF324289811BADC5D5B795C653D2"
msgid "100"
msgstr "100"

#. Key:	D29AE9D742EA3C95F8F85AB501DC4276
#. SourceLocation:	/Pekao_Bank/VRaceMinigame/UI/WBP_RaceCoinsBar.WBP_RaceCoinsBar_C:WidgetTree.UEFN_TextBlock_C_0.Text
#: /Pekao_Bank/VRaceMinigame/UI/WBP_RaceCoinsBar.WBP_RaceCoinsBar_C:WidgetTree.UEFN_TextBlock_C_0.Text
msgctxt ",D29AE9D742EA3C95F8F85AB501DC4276"
msgid "POINTS"
msgstr "PUNTI"

#. Key:	A7D46A4146B0D01F6352449B378F33ED
#. SourceLocation:	/Pekao_Bank/VResourcesSystem/Assets/SaveTimer.SaveTimer_C:WidgetTree.TimerText.Text
#: /Pekao_Bank/VResourcesSystem/Assets/SaveTimer.SaveTimer_C:WidgetTree.TimerText.Text
msgctxt ",A7D46A4146B0D01F6352449B378F33ED"
msgid "5:55"
msgstr "5:55"

#. Key:	3D9932794AEACC2BFBFABAAB4E6AC417
#. SourceLocation:	/Pekao_Bank/VResourcesSystem/Assets/WidgetResourceMiniPhone.WidgetResourceMiniPhone_C:WidgetTree.TimerText.Text
#: /Pekao_Bank/VResourcesSystem/Assets/WidgetResourceMiniPhone.WidgetResourceMiniPhone_C:WidgetTree.TimerText.Text
msgctxt ",3D9932794AEACC2BFBFABAAB4E6AC417"
msgid "5:99"
msgstr "5:99"

#. Key:	542C38C54379204F683577A1551804F4
#. SourceLocation:	/Pekao_Bank/VResourcesSystem/Assets/WidgetResourceMiniPhone.WidgetResourceMiniPhone_C:WidgetTree.TimerText_1.Text
#: /Pekao_Bank/VResourcesSystem/Assets/WidgetResourceMiniPhone.WidgetResourceMiniPhone_C:WidgetTree.TimerText_1.Text
msgctxt ",542C38C54379204F683577A1551804F4"
msgid "100$"
msgstr "100$"

#. Key:	4A716AB1439950E15AB4BF99EC315072
#. SourceLocation:	/Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.CurGold.Text
#: /Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.CurGold.Text
msgctxt ",4A716AB1439950E15AB4BF99EC315072"
msgid "9999"
msgstr "9999"

#. Key:	8B8CDAA6432AE2271CD52FBF3D8E572E
#. SourceLocation:	/Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.CurWood.Text
#: /Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.CurWood.Text
msgctxt ",8B8CDAA6432AE2271CD52FBF3D8E572E"
msgid "9999"
msgstr "9999"

#. Key:	580728FD4EDBE338DAFFBC95CDCC9E2B
#. SourceLocation:	/Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.GoldPerSec.Text
#: /Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.GoldPerSec.Text
msgctxt ",580728FD4EDBE338DAFFBC95CDCC9E2B"
msgid "9999/s"
msgstr "9999/s"

#. Key:	EC5F2AF54A4599A48D28A8AAF297EF05
#. SourceLocation:	/Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.Image1.Text
#: /Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.Image1.Text
msgctxt ",EC5F2AF54A4599A48D28A8AAF297EF05"
msgid "💸"
msgstr "💸"

#. Key:	2E20343C442D249F656FAFAA9F56D356
#. SourceLocation:	/Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.Image1_2.Text
#: /Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.Image1_2.Text
msgctxt ",2E20343C442D249F656FAFAA9F56D356"
msgid "🌲"
msgstr "🌲"

#. Key:	909A0397497F6215902ECCA419DE752E
#. SourceLocation:	/Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.Image1_3.Text
#: /Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.Image1_3.Text
msgctxt ",909A0397497F6215902ECCA419DE752E"
msgid "🐶"
msgstr "🐶"

#. Key:	CE24746C4D1DE52720305191846EB782
#. SourceLocation:	/Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.Image1_4.Text
#: /Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.Image1_4.Text
msgctxt ",CE24746C4D1DE52720305191846EB782"
msgid "🍀"
msgstr "🍀"

#. Key:	182ABC3C46CDFD8F9A07EDAC3EC574BE
#. SourceLocation:	/Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.PetBonus.Text
#: /Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.PetBonus.Text
msgctxt ",182ABC3C46CDFD8F9A07EDAC3EC574BE"
msgid "+5%"
msgstr "+5%"

#. Key:	55722A5D46CE0255A4F3EC81124AA82C
#. SourceLocation:	/Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.PlayerName.Text
#: /Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.PlayerName.Text
msgctxt ",55722A5D46CE0255A4F3EC81124AA82C"
msgid "WWWWWWWWWWWWWW"
msgstr "WWWWWWWWWWWWWW"

#. Key:	F54EE59646BE4175BB38BE8D91B240B5
#. SourceLocation:	/Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.RebirthBonus.Text
#: /Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.RebirthBonus.Text
msgctxt ",F54EE59646BE4175BB38BE8D91B240B5"
msgid "Premia za odrodzenie 1.25%"
msgstr "Rebirth bonus 1.25%"

#. Key:	CD4B030F404310BF218CF9BA6DA130FF
#. SourceLocation:	/Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.TextPoints.Text
#: /Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.TextPoints.Text
msgctxt ",CD4B030F404310BF218CF9BA6DA130FF"
msgid "Points: 1000"
msgstr "Pontos: 1000"

#. Key:	87E5CB6145F24709A2B532809DD847C0
#. SourceLocation:	/Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.TextTimeDaily.Text
#: /Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.TextTimeDaily.Text
msgctxt ",87E5CB6145F24709A2B532809DD847C0"
msgid "22min"
msgstr "22min"

#. Key:	A889F33043F5D3D678970782A28DA73C
#. SourceLocation:	/Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.Tokens.Text
#: /Pekao_Bank/VResourcesSystem/Assets/WidgetResources.WidgetResources_C:WidgetTree.Tokens.Text
msgctxt ",A889F33043F5D3D678970782A28DA73C"
msgid "10"
msgstr "10"

#. Key:	71E34E98404987AA1E0E3D9DDEBB2CB9
#. SourceLocation:	/Pekao_Bank/VTimer/WBP_Timer.WBP_Timer_C:WidgetTree.UEFN_TextBlock_C_0.Text
#: /Pekao_Bank/VTimer/WBP_Timer.WBP_Timer_C:WidgetTree.UEFN_TextBlock_C_0.Text
msgctxt ",71E34E98404987AA1E0E3D9DDEBB2CB9"
msgid "0:00"
msgstr "0:00"

#. Key:	A4286CF34DDB0DDE113E188FB03F306A
#. SourceLocation:	/Pekao_Bank/VTimeRewards/WBP_Verse_RewardsClaim.WBP_Verse_RewardsClaim_C:WidgetTree.Timer1.Text
#: /Pekao_Bank/VTimeRewards/WBP_Verse_RewardsClaim.WBP_Verse_RewardsClaim_C:WidgetTree.Timer1.Text
msgctxt ",A4286CF34DDB0DDE113E188FB03F306A"
msgid "10.99"
msgstr "10.99"

#. Key:	09BC935F4735BCDA50E2F9B43722DA20
#. SourceLocation:	/Pekao_Bank/VTimeRewards/WBP_Verse_RewardsOnScreenTimer.WBP_Verse_RewardsOnScreenTimer_C:WidgetTree.TimerText.Text
#: /Pekao_Bank/VTimeRewards/WBP_Verse_RewardsOnScreenTimer.WBP_Verse_RewardsOnScreenTimer_C:WidgetTree.TimerText.Text
msgctxt ",09BC935F4735BCDA50E2F9B43722DA20"
msgid "5:99"
msgstr "5:99"

#. Key:	/<EMAIL>/Pekao_Bank/notification_on_card_unlock/CardUnlockedMessage
#. SourceLocation:	Pekao_Bank/Content/gm.verse(166,5, 166,80)
#: Pekao_Bank/Content/gm.verse(166,5, 166,80)
msgctxt ",/<EMAIL>/Pekao_Bank/notification_on_card_unlock/CardUnlockedMessage"
msgid "Wzór karty odblokowana w banku!"
msgstr "Card pattern unlocked in the bank!"

#. Key:	/<EMAIL>/Pekao_Bank/NPC/bankclient_npc_behavior_message_module/SettingsCategory
#. SourceLocation:	Pekao_Bank/Content/NPC/bankclient_npc_behavior.verse(21,5, 21,61)
#: Pekao_Bank/Content/NPC/bankclient_npc_behavior.verse(21,5, 21,61)
msgctxt ",/<EMAIL>/Pekao_Bank/NPC/bankclient_npc_behavior_message_module/SettingsCategory"
msgid "Settings"
msgstr "Setări"

#. Key:	/<EMAIL>/Pekao_Bank/NPC/bankclient_npc_behavior_message_module/OnBeginMessage
#. SourceLocation:	Pekao_Bank/Content/NPC/bankclient_npc_behavior.verse(23,5, 25,1)
#: Pekao_Bank/Content/NPC/bankclient_npc_behavior.verse(23,5, 25,1)
msgctxt ",/<EMAIL>/Pekao_Bank/NPC/bankclient_npc_behavior_message_module/OnBeginMessage"
msgid "NPC Agent = {Agent}: OnBegin triggered let's get started."
msgstr "NPC Agent = {Agent}: OnBegin triggered, let's get started."

#. Key:	/<EMAIL>/Pekao_Bank/NPC/bankclient_npc_behavior_message_module/OnEndMessage
#. SourceLocation:	Pekao_Bank/Content/NPC/bankclient_npc_behavior.verse(25,5, 27,1)
#: Pekao_Bank/Content/NPC/bankclient_npc_behavior.verse(25,5, 27,1)
msgctxt ",/<EMAIL>/Pekao_Bank/NPC/bankclient_npc_behavior_message_module/OnEndMessage"
msgid "NPC Agent = {Agent}: OnEnd triggered let's cleanup."
msgstr "NPC Agent = {Agent}: OnEnd triggered let's clean up."

#. Key:	/<EMAIL>/Pekao_Bank/NPC/bankclient_npc_behavior_message_module/OnNavigateBeginMessage
#. SourceLocation:	Pekao_Bank/Content/NPC/bankclient_npc_behavior.verse(27,5, 29,1)
#: Pekao_Bank/Content/NPC/bankclient_npc_behavior.verse(27,5, 29,1)
msgctxt ",/<EMAIL>/Pekao_Bank/NPC/bankclient_npc_behavior_message_module/OnNavigateBeginMessage"
msgid "NPC Agent = {Agent}: Is moving to [{X},{Y},{Z}]"
msgstr "NPC Agent = {Agent}: Is moving to [{X},{Y},{Z}]"

#. Key:	/<EMAIL>/Pekao_Bank/NPC/bankclient_npc_behavior_message_module/OnNavigateErrorMessage
#. SourceLocation:	Pekao_Bank/Content/NPC/bankclient_npc_behavior.verse(29,5, 31,1)
#: Pekao_Bank/Content/NPC/bankclient_npc_behavior.verse(29,5, 31,1)
msgctxt ",/<EMAIL>/Pekao_Bank/NPC/bankclient_npc_behavior_message_module/OnNavigateErrorMessage"
msgid "NPC Agent = {Agent}: Hit error moving to [{X},{Y},{Z}], please refer to Island Setting's Navigation debug"
msgstr "NPC Agent = {Agent}: Hit error moving to [{X},{Y},{Z}], please refer to Island Setting's Navigation debug"

#. Key:	/<EMAIL>/Pekao_Bank/NPC/random_walking_npc_behavior_message_module/SettingsCategory
#. SourceLocation:	Pekao_Bank/Content/NPC/random_walking_npc_behavior.verse(20,5, 20,61)
#: Pekao_Bank/Content/NPC/random_walking_npc_behavior.verse(20,5, 20,61)
msgctxt ",/<EMAIL>/Pekao_Bank/NPC/random_walking_npc_behavior_message_module/SettingsCategory"
msgid "Settings"
msgstr "Setări"

#. Key:	/<EMAIL>/Pekao_Bank/NPC/random_walking_npc_behavior_message_module/OnBeginMessage
#. SourceLocation:	Pekao_Bank/Content/NPC/random_walking_npc_behavior.verse(22,5, 24,1)
#: Pekao_Bank/Content/NPC/random_walking_npc_behavior.verse(22,5, 24,1)
msgctxt ",/<EMAIL>/Pekao_Bank/NPC/random_walking_npc_behavior_message_module/OnBeginMessage"
msgid "NPC Agent = {Agent}: OnBegin triggered let's get started."
msgstr "NPC Agent = {Agent}: OnBegin triggered, let's get started."

#. Key:	/<EMAIL>/Pekao_Bank/NPC/random_walking_npc_behavior_message_module/OnEndMessage
#. SourceLocation:	Pekao_Bank/Content/NPC/random_walking_npc_behavior.verse(24,5, 26,1)
#: Pekao_Bank/Content/NPC/random_walking_npc_behavior.verse(24,5, 26,1)
msgctxt ",/<EMAIL>/Pekao_Bank/NPC/random_walking_npc_behavior_message_module/OnEndMessage"
msgid "NPC Agent = {Agent}: OnEnd triggered let's cleanup."
msgstr "NPC Agent = {Agent}: OnEnd triggered let's clean up."

#. Key:	/<EMAIL>/Pekao_Bank/NPC/random_walking_npc_behavior_message_module/OnNavigateBeginMessage
#. SourceLocation:	Pekao_Bank/Content/NPC/random_walking_npc_behavior.verse(26,5, 28,1)
#: Pekao_Bank/Content/NPC/random_walking_npc_behavior.verse(26,5, 28,1)
msgctxt ",/<EMAIL>/Pekao_Bank/NPC/random_walking_npc_behavior_message_module/OnNavigateBeginMessage"
msgid "NPC Agent = {Agent}: Is moving to [{X},{Y},{Z}]"
msgstr "NPC Agent = {Agent}: Is moving to [{X},{Y},{Z}]"

#. Key:	/<EMAIL>/Pekao_Bank/NPC/random_walking_npc_behavior_message_module/OnNavigateErrorMessage
#. SourceLocation:	Pekao_Bank/Content/NPC/random_walking_npc_behavior.verse(28,5, 30,1)
#: Pekao_Bank/Content/NPC/random_walking_npc_behavior.verse(28,5, 30,1)
msgctxt ",/<EMAIL>/Pekao_Bank/NPC/random_walking_npc_behavior_message_module/OnNavigateErrorMessage"
msgid "NPC Agent = {Agent}: Hit error moving to [{X},{Y},{Z}], please refer to Island Setting's Navigation debug"
msgstr "NPC Agent = {Agent}: Hit error moving to [{X},{Y},{Z}], please refer to Island Setting's Navigation debug"

#. Key:	/<EMAIL>/Pekao_Bank/VAudio/one_button_music_player_device/StringToMessage
#. SourceLocation:	Pekao_Bank/Content/VAudio/one_button_music_player_device.verse(49,5, 49,77)
#: Pekao_Bank/Content/VAudio/one_button_music_player_device.verse(49,5, 49,77)
msgctxt ",/<EMAIL>/Pekao_Bank/VAudio/one_button_music_player_device/StringToMessage"
msgid "{value}"
msgstr "{value}"

#. Key:	/<EMAIL>/Pekao_Bank/VBuyables/buyable/BoughtBusinessMsgPol
#. SourceLocation:	Pekao_Bank/Content/VBuyables/buyable.verse(144,5, 144,123)
#: Pekao_Bank/Content/VBuyables/buyable.verse(144,5, 144,123)
msgctxt ",/<EMAIL>/Pekao_Bank/VBuyables/buyable/BoughtBusinessMsgPol"
msgid "Biznes kupiony! +${Amount}/sek"
msgstr "Business bought! +${Amount}/sec"

#. Key:	/<EMAIL>/Pekao_Bank/VChestsSystem/spawned_chests_manager/OpenChestMessage
#. SourceLocation:	Pekao_Bank/Content/VChestsSystem/spawned_chests_system.verse(63,2, 63,52)
#: Pekao_Bank/Content/VChestsSystem/spawned_chests_system.verse(63,2, 63,52)
msgctxt ",/<EMAIL>/Pekao_Bank/VChestsSystem/spawned_chests_manager/OpenChestMessage"
msgid "Otwórz skrzynię"
msgstr "Open the chest"

#. Key:	/<EMAIL>/Pekao_Bank/VCreaturesSystem/upgradable_spawner/SpawnerUpgradedMessage
#. SourceLocation:	Pekao_Bank/Content/VCreaturesSystem/upgradable_spawner.verse(125,2, 125,127)
#: Pekao_Bank/Content/VCreaturesSystem/upgradable_spawner.verse(125,2, 125,127)
msgctxt ",/<EMAIL>/Pekao_Bank/VCreaturesSystem/upgradable_spawner/SpawnerUpgradedMessage"
msgid "{Level}Zaktualizowano Creature Spawner: {PrevLevel}-> lvl"
msgstr "{Level}Updated Creature Spawner: {PrevLevel}- > lvl"

#. Key:	/<EMAIL>/Pekao_Bank/VCreaturesSystem/upgradable_spawner/SpawnerUpgradedMessage2
#. SourceLocation:	Pekao_Bank/Content/VCreaturesSystem/upgradable_spawner.verse(126,2, 126,101)
#: Pekao_Bank/Content/VCreaturesSystem/upgradable_spawner.verse(126,2, 126,101)
msgctxt ",/<EMAIL>/Pekao_Bank/VCreaturesSystem/upgradable_spawner/SpawnerUpgradedMessage2"
msgid "Na zabicie: {Prev}💸 -> {Cur}💸"
msgstr "To kill: {Prev}💸 - > {Cur}💸"

#. Key:	/<EMAIL>/Pekao_Bank/VGiga/ToMessage
#. SourceLocation:	Pekao_Bank/Content/VGiga/String.verse(14,1, 14,63)
#: Pekao_Bank/Content/VGiga/String.verse(14,1, 14,63)
msgctxt ",/<EMAIL>/Pekao_Bank/VGiga/ToMessage"
msgid "{Text}"
msgstr "{Text}"

#. Key:	/<EMAIL>/Pekao_Bank/VGiga/ToMsg
#. SourceLocation:	Pekao_Bank/Content/VGiga/String.verse(15,1, 15,62)
#: Pekao_Bank/Content/VGiga/String.verse(15,1, 15,62)
msgctxt ",/<EMAIL>/Pekao_Bank/VGiga/ToMsg"
msgid "{Val}"
msgstr "{Val}"

#. Key:	/<EMAIL>/Pekao_Bank/VGiga/tick_rate_debug/TickRateText
#. SourceLocation:	Pekao_Bank/Content/VGiga/tick_rate_device.verse(15,2, 15,96)
#: Pekao_Bank/Content/VGiga/tick_rate_device.verse(15,2, 15,96)
msgctxt ",/<EMAIL>/Pekao_Bank/VGiga/tick_rate_debug/TickRateText"
msgid "{TickRateString}TPS"
msgstr "{TickRateString}TPS"

#. Key:	/<EMAIL>/Pekao_Bank/VLeaderboard/OkButtonTextVar
#. SourceLocation:	Pekao_Bank/Content/VLeaderboard/leaderboard_landing_page_generated.verse(14,1, 14,42)
#: Pekao_Bank/Content/VLeaderboard/leaderboard_landing_page_generated.verse(14,1, 14,42)
msgctxt ",/<EMAIL>/Pekao_Bank/VLeaderboard/OkButtonTextVar"
msgid "OK"
msgstr "OK"

#. Key:	/<EMAIL>/Pekao_Bank/VLeaderboard/TextVerificationTextVar
#. SourceLocation:	Pekao_Bank/Content/VLeaderboard/leaderboard_landing_page_generated.verse(15,1, 15,75)
#: Pekao_Bank/Content/VLeaderboard/leaderboard_landing_page_generated.verse(15,1, 15,75)
msgctxt ",/<EMAIL>/Pekao_Bank/VLeaderboard/TextVerificationTextVar"
msgid "Verification Code:\r\n9999"
msgstr "Verifikationskode:\n9999"

#. Key:	/<EMAIL>/Pekao_Bank/VLeaderboard/TextPointsTextVar
#. SourceLocation:	Pekao_Bank/Content/VLeaderboard/leaderboard_landing_page_generated.verse(16,1, 16,55)
#: Pekao_Bank/Content/VLeaderboard/leaderboard_landing_page_generated.verse(16,1, 16,55)
msgctxt ",/<EMAIL>/Pekao_Bank/VLeaderboard/TextPointsTextVar"
msgid "Points: 9999"
msgstr "Bodov: 9999"

#. Key:	/<EMAIL>/Pekao_Bank/VMinigames/app_investment/InvestGainMsg
#. SourceLocation:	Pekao_Bank/Content/VMinigames/app_investment.verse(43,5, 43,51)
#: Pekao_Bank/Content/VMinigames/app_investment.verse(43,5, 43,51)
msgctxt ",/<EMAIL>/Pekao_Bank/VMinigames/app_investment/InvestGainMsg"
msgid "Aplikacja"
msgstr "Application"

#. Key:	/<EMAIL>/Pekao_Bank/VMovingObjectsCode/RotationRateTip
#. SourceLocation:	Pekao_Bank/Content/VMovingObjectsCode/animating_prop.verse(11,1, 11,101)
#: Pekao_Bank/Content/VMovingObjectsCode/animating_prop.verse(11,1, 11,101)
msgctxt ",/<EMAIL>/Pekao_Bank/VMovingObjectsCode/RotationRateTip"
msgid "Czas potrzebny na wykonanie jednego dodatkowego obrotu w sekundach."
msgstr "The time it takes to complete one additional rotation in seconds."

#. Key:	/<EMAIL>/Pekao_Bank/VMovingObjectsCode/UseEasePerKeyframeTip
#. SourceLocation:	Pekao_Bank/Content/VMovingObjectsCode/animating_prop.verse(13,1, 13,162)
#: Pekao_Bank/Content/VMovingObjectsCode/animating_prop.verse(13,1, 13,162)
msgctxt ",/<EMAIL>/Pekao_Bank/VMovingObjectsCode/UseEasePerKeyframeTip"
msgid "Czy ten rekwizyt powinien używać MoveEaseType dla każdej klatki kluczowej. False użyje typu Linear easy na każdej klatce."
msgstr "Whether this prop should use MoveEaseType for each keyframe. False will use the Linear easy type on each frame."

#. Key:	/<EMAIL>/Pekao_Bank/VMovingObjectsCode/MoveEaseTypeTip
#. SourceLocation:	Pekao_Bank/Content/VMovingObjectsCode/movable_prop.verse(11,1, 11,85)
#: Pekao_Bank/Content/VMovingObjectsCode/movable_prop.verse(11,1, 11,85)
msgctxt ",/<EMAIL>/Pekao_Bank/VMovingObjectsCode/MoveEaseTypeTip"
msgid "Ułatwienie animacji zastosowane do ruchu."
msgstr "Facilitation of animation applied to movement."

#. Key:	/<EMAIL>/Pekao_Bank/VMovingObjectsCode/MoveEndDelayTip
#. SourceLocation:	Pekao_Bank/Content/VMovingObjectsCode/movable_prop.verse(13,1, 13,78)
#: Pekao_Bank/Content/VMovingObjectsCode/movable_prop.verse(13,1, 13,78)
msgctxt ",/<EMAIL>/Pekao_Bank/VMovingObjectsCode/MoveEndDelayTip"
msgid "Opóźnienie po zakończeniu ruchu."
msgstr "Delay after the end of the movement."

#. Key:	/<EMAIL>/Pekao_Bank/VMovingObjectsCode/MoveOnceAndStopTip
#. SourceLocation:	Pekao_Bank/Content/VMovingObjectsCode/movable_prop.verse(15,1, 15,110)
#: Pekao_Bank/Content/VMovingObjectsCode/movable_prop.verse(15,1, 15,110)
msgctxt ",/<EMAIL>/Pekao_Bank/VMovingObjectsCode/MoveOnceAndStopTip"
msgid "Czy RootProp powinien zatrzymać się na miejscu po zakończeniu ruchu."
msgstr "Should RootProp stop in place after the move is complete."

#. Key:	/<EMAIL>/Pekao_Bank/VMovingObjectsCode/MoveStartDelayTip
#. SourceLocation:	Pekao_Bank/Content/VMovingObjectsCode/movable_prop.verse(17,1, 17,79)
#: Pekao_Bank/Content/VMovingObjectsCode/movable_prop.verse(17,1, 17,79)
msgctxt ",/<EMAIL>/Pekao_Bank/VMovingObjectsCode/MoveStartDelayTip"
msgid "Opóźnienie przed rozpoczęciem ruchu."
msgstr "Delay before the start of the movement."

#. Key:	/<EMAIL>/Pekao_Bank/VMovingObjectsCode/MoveTargetsTip
#. SourceLocation:	Pekao_Bank/Content/VMovingObjectsCode/movable_prop.verse(19,1, 19,127)
#: Pekao_Bank/Content/VMovingObjectsCode/movable_prop.verse(19,1, 19,127)
msgctxt ",/<EMAIL>/Pekao_Bank/VMovingObjectsCode/MoveTargetsTip"
msgid "Tablica CreativeProp, do której należy się poruszać. Te cele mogą być potomkami RootProp."
msgstr "The CreativeProp array to navigate to. These targets can be descendants of RootProp."

#. Key:	/<EMAIL>/Pekao_Bank/VMovingObjectsCode/RootPropTip
#. SourceLocation:	Pekao_Bank/Content/VMovingObjectsCode/movable_prop.verse(21,1, 21,117)
#: Pekao_Bank/Content/VMovingObjectsCode/movable_prop.verse(21,1, 21,117)
msgctxt ",/<EMAIL>/Pekao_Bank/VMovingObjectsCode/RootPropTip"
msgid "Rekwizyt, który się porusza. Powinna to być podstawowa podpórka obiektu, który chcesz przenieść."
msgstr "A prop that moves. This should be the basic support of the object you want to move."

#. Key:	/<EMAIL>/Pekao_Bank/VMovingObjectsCode/ShouldResetTip
#. SourceLocation:	Pekao_Bank/Content/VMovingObjectsCode/movable_prop.verse(23,1, 23,128)
#: Pekao_Bank/Content/VMovingObjectsCode/movable_prop.verse(23,1, 23,128)
msgctxt ",/<EMAIL>/Pekao_Bank/VMovingObjectsCode/ShouldResetTip"
msgid "Czy RootProp powinien zresetować się z powrotem do pozycji wyjściowej po zakończeniu ruchu."
msgstr "Should rootProp reset back to the starting position after the move is complete."

#. Key:	/<EMAIL>/Pekao_Bank/VMovingObjectsCode/MoveDurationTip
#. SourceLocation:	Pekao_Bank/Content/VMovingObjectsCode/movable_prop.verse(9,1, 9,101)
#: Pekao_Bank/Content/VMovingObjectsCode/movable_prop.verse(9,1, 9,101)
msgctxt ",/<EMAIL>/Pekao_Bank/VMovingObjectsCode/MoveDurationTip"
msgid "Czas potrzebny rekwizytowi na przeniesienie się do celu."
msgstr "The time it takes for the prop to move to the target."

#. Key:	/<EMAIL>/Pekao_Bank/VMovingObjectsCode/RotatingPropsTip
#. SourceLocation:	Pekao_Bank/Content/VMovingObjectsCode/prop_animator.verse(11,1, 11,79)
#: Pekao_Bank/Content/VMovingObjectsCode/prop_animator.verse(11,1, 11,79)
msgctxt ",/<EMAIL>/Pekao_Bank/VMovingObjectsCode/RotatingPropsTip"
msgid "Rekwizyty, które obracają się za pomocą animacji."
msgstr "Props that rotate using animation."

#. Key:	/<EMAIL>/Pekao_Bank/VMovingObjectsCode/ScalingPropsTip
#. SourceLocation:	Pekao_Bank/Content/VMovingObjectsCode/prop_animator.verse(13,1, 13,77)
#: Pekao_Bank/Content/VMovingObjectsCode/prop_animator.verse(13,1, 13,77)
msgctxt ",/<EMAIL>/Pekao_Bank/VMovingObjectsCode/ScalingPropsTip"
msgid "Rekwizyty skalowane za pomocą animacji."
msgstr "Props scaled with animation."

#. Key:	/<EMAIL>/Pekao_Bank/VMovingObjectsCode/MoveAndRotatePropsTip
#. SourceLocation:	Pekao_Bank/Content/VMovingObjectsCode/prop_animator.verse(15,1, 15,98)
#: Pekao_Bank/Content/VMovingObjectsCode/prop_animator.verse(15,1, 15,98)
msgctxt ",/<EMAIL>/Pekao_Bank/VMovingObjectsCode/MoveAndRotatePropsTip"
msgid "Rekwizyty, które poruszają się i obracają za pomocą animacji."
msgstr "Props that move and rotate using animation."

#. Key:	/<EMAIL>/Pekao_Bank/VMovingObjectsCode/TranslatingPropsTip
#. SourceLocation:	Pekao_Bank/Content/VMovingObjectsCode/prop_animator.verse(9,1, 9,92)
#: Pekao_Bank/Content/VMovingObjectsCode/prop_animator.verse(9,1, 9,92)
msgctxt ",/<EMAIL>/Pekao_Bank/VMovingObjectsCode/TranslatingPropsTip"
msgid "Rekwizyty, które tłumaczą (przenoszą) za pomocą animacji."
msgstr "Props that translate (transfer) with the help of animation."

#. Key:	/<EMAIL>/Pekao_Bank/VMovingObjectsCode/AdditionalRotationTip
#. SourceLocation:	Pekao_Bank/Content/VMovingObjectsCode/rotating_prop.verse(11,1, 11,84)
#: Pekao_Bank/Content/VMovingObjectsCode/rotating_prop.verse(11,1, 11,84)
msgctxt ",/<EMAIL>/Pekao_Bank/VMovingObjectsCode/AdditionalRotationTip"
msgid "Obrót do zastosowania do RootProp."
msgstr "Rotation to apply to RootProp."

#. Key:	/<EMAIL>/Pekao_Bank/VMovingObjectsCode/ShouldRotateForeverTip
#. SourceLocation:	Pekao_Bank/Content/VMovingObjectsCode/rotating_prop.verse(13,1, 13,90)
#: Pekao_Bank/Content/VMovingObjectsCode/rotating_prop.verse(13,1, 13,90)
msgctxt ",/<EMAIL>/Pekao_Bank/VMovingObjectsCode/ShouldRotateForeverTip"
msgid "Czy RootProp powinien się obracać na zawsze."
msgstr "Should RootProp rotate forever."

#. Key:	/<EMAIL>/Pekao_Bank/VMovingObjectsCode/MatchRotationTargetTip
#. SourceLocation:	Pekao_Bank/Content/VMovingObjectsCode/rotating_prop.verse(15,1, 15,169)
#: Pekao_Bank/Content/VMovingObjectsCode/rotating_prop.verse(15,1, 15,169)
msgctxt ",/<EMAIL>/Pekao_Bank/VMovingObjectsCode/MatchRotationTargetTip"
msgid "Opcjonalny podpór, do którego obrotu powinien obrócić się RootProp. Użyj tego, jeśli nie chcesz ustawiać dodatkowego obrotu."
msgstr "Optional support to which rotation RootProp should rotate. Use this if you don't want to set an extra spin."

#. Key:	/<EMAIL>/Pekao_Bank/VMovingObjectsCode/MatchScaleTargetTip
#. SourceLocation:	Pekao_Bank/Content/VMovingObjectsCode/scaling_prop.verse(11,1, 11,138)
#: Pekao_Bank/Content/VMovingObjectsCode/scaling_prop.verse(11,1, 11,138)
msgctxt ",/<EMAIL>/Pekao_Bank/VMovingObjectsCode/MatchScaleTargetTip"
msgid "Opcjonalna pozycja do przejścia do World Space. Użyj tego, jeśli nie chcesz ustawić MoveTarget."
msgstr "Optional position to move to World Space. Use this if you don't want to set MoveTarget."

#. Key:	/<EMAIL>/Pekao_Bank/VMovingObjectsCode/MovePositionTip
#. SourceLocation:	Pekao_Bank/Content/VMovingObjectsCode/translating_prop.verse(11,1, 11,134)
#: Pekao_Bank/Content/VMovingObjectsCode/translating_prop.verse(11,1, 11,134)
msgctxt ",/<EMAIL>/Pekao_Bank/VMovingObjectsCode/MovePositionTip"
msgid "Opcjonalna pozycja do przejścia do World Space. Użyj tego, jeśli nie chcesz ustawić MoveTarget."
msgstr "Optional position to move to World Space. Use this if you don't want to set MoveTarget."

#. Key:	/<EMAIL>/Pekao_Bank/VNews/UEFN_TextBlock_C_6TextVar
#. SourceLocation:	Pekao_Bank/Content/VNews/news_ui_generated.verse(23,1, 23,79)
#: Pekao_Bank/Content/VNews/news_ui_generated.verse(23,1, 23,79)
msgctxt ",/<EMAIL>/Pekao_Bank/VNews/UEFN_TextBlock_C_6TextVar"
msgid "- Eliminacja botów Grants Xp"
msgstr "- Elimination of Grants Xp bots"

#. Key:	/<EMAIL>/Pekao_Bank/VNews/UEFN_TextBlock_C_3TextVar
#. SourceLocation:	Pekao_Bank/Content/VNews/news_ui_generated.verse(24,1, 24,75)
#: Pekao_Bank/Content/VNews/news_ui_generated.verse(24,1, 24,75)
msgctxt ",/<EMAIL>/Pekao_Bank/VNews/UEFN_TextBlock_C_3TextVar"
msgid "- Przetworniki kolekcjonerskie Xp"
msgstr "- Xp Collector Transducers"

#. Key:	/<EMAIL>/Pekao_Bank/VNews/UEFN_TextBlock_C_2TextVar
#. SourceLocation:	Pekao_Bank/Content/VNews/news_ui_generated.verse(25,1, 25,102)
#: Pekao_Bank/Content/VNews/news_ui_generated.verse(25,1, 25,102)
msgctxt ",/<EMAIL>/Pekao_Bank/VNews/UEFN_TextBlock_C_2TextVar"
msgid "- Codzienne nagrody Playtime Xp (Reset poprawiony w 1.41!)"
msgstr "- Daily rewards Playtime Xp (Reset fixed in 1.41!)"

#. Key:	/<EMAIL>/Pekao_Bank/VNews/UEFN_TextBlock_C_8TextVar
#. SourceLocation:	Pekao_Bank/Content/VNews/news_ui_generated.verse(26,1, 26,62)
#: Pekao_Bank/Content/VNews/news_ui_generated.verse(26,1, 26,62)
msgctxt ",/<EMAIL>/Pekao_Bank/VNews/UEFN_TextBlock_C_8TextVar"
msgid "AKTUALIZACJA 1.41"
msgstr "UPDATE 1.41"

#. Key:	/<EMAIL>/Pekao_Bank/VNews/UEFN_TextBlock_C_1TextVar
#. SourceLocation:	Pekao_Bank/Content/VNews/news_ui_generated.verse(27,1, 27,72)
#: Pekao_Bank/Content/VNews/news_ui_generated.verse(27,1, 27,72)
msgctxt ",/<EMAIL>/Pekao_Bank/VNews/UEFN_TextBlock_C_1TextVar"
msgid "- Naprawiono leczenie do kupienia"
msgstr "- Fixed a treatment to buy"

#. Key:	/<EMAIL>/Pekao_Bank/VNews/UEFN_TextBlock_CTextVar
#. SourceLocation:	Pekao_Bank/Content/VNews/news_ui_generated.verse(28,1, 28,88)
#: Pekao_Bank/Content/VNews/news_ui_generated.verse(28,1, 28,88)
msgctxt ",/<EMAIL>/Pekao_Bank/VNews/UEFN_TextBlock_CTextVar"
msgid "- Naprawiono czasami znikanie piłki nożnej"
msgstr "- Fixed occasional disappearances of football"

#. Key:	/<EMAIL>/Pekao_Bank/VNews/UEFN_TextBlock_C_5TextVar
#. SourceLocation:	Pekao_Bank/Content/VNews/news_ui_generated.verse(29,1, 29,100)
#: Pekao_Bank/Content/VNews/news_ui_generated.verse(29,1, 29,100)
msgctxt ",/<EMAIL>/Pekao_Bank/VNews/UEFN_TextBlock_C_5TextVar"
msgid "- Wzmocnienia i umiejętności oparte na piłkarzach!"
msgstr "- Boosts and skills based on footballers!"

#. Key:	/<EMAIL>/Pekao_Bank/VNews/UEFN_TextBlock_C_4TextVar
#. SourceLocation:	Pekao_Bank/Content/VNews/news_ui_generated.verse(30,1, 30,61)
#: Pekao_Bank/Content/VNews/news_ui_generated.verse(30,1, 30,61)
msgctxt ",/<EMAIL>/Pekao_Bank/VNews/UEFN_TextBlock_C_4TextVar"
msgid "AKTUALIZACJA 1.3"
msgstr "UPDATE 1.3"

#. Key:	/<EMAIL>/Pekao_Bank/VNews/UEFN_Button_Regular_CTextVar
#. SourceLocation:	Pekao_Bank/Content/VNews/news_ui_generated.verse(31,1, 31,57)
#: Pekao_Bank/Content/VNews/news_ui_generated.verse(31,1, 31,57)
msgctxt ",/<EMAIL>/Pekao_Bank/VNews/UEFN_Button_Regular_CTextVar"
msgid "ZAGRAĆ"
msgstr "PLAY"

#. Key:	/<EMAIL>/Pekao_Bank/VNotifications/TextFull_7TextVar
#. SourceLocation:	Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(50,1, 50,69)
#: Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(50,1, 50,69)
msgctxt ",/<EMAIL>/Pekao_Bank/VNotifications/TextFull_7TextVar"
msgid "Woofffffffd +3fffffffdfsdf"
msgstr "Woofffffffd +3fffffffffdfsdf"

#. Key:	/<EMAIL>/Pekao_Bank/VNotifications/TextRes_7TextVar
#. SourceLocation:	Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(51,1, 51,46)
#: Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(51,1, 51,46)
msgctxt ",/<EMAIL>/Pekao_Bank/VNotifications/TextRes_7TextVar"
msgid "🌲"
msgstr "🌲"

#. Key:	/<EMAIL>/Pekao_Bank/VNotifications/TextFull_6TextVar
#. SourceLocation:	Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(52,1, 52,69)
#: Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(52,1, 52,69)
msgctxt ",/<EMAIL>/Pekao_Bank/VNotifications/TextFull_6TextVar"
msgid "Woofffffffd +3fffffffdfsdf"
msgstr "Woofffffffd +3fffffffffdfsdf"

#. Key:	/<EMAIL>/Pekao_Bank/VNotifications/TextRes_6TextVar
#. SourceLocation:	Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(53,1, 53,46)
#: Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(53,1, 53,46)
msgctxt ",/<EMAIL>/Pekao_Bank/VNotifications/TextRes_6TextVar"
msgid "🌲"
msgstr "🌲"

#. Key:	/<EMAIL>/Pekao_Bank/VNotifications/TextFull_5TextVar
#. SourceLocation:	Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(54,1, 54,69)
#: Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(54,1, 54,69)
msgctxt ",/<EMAIL>/Pekao_Bank/VNotifications/TextFull_5TextVar"
msgid "Woofffffffd +3fffffffdfsdf"
msgstr "Woofffffffd +3fffffffffdfsdf"

#. Key:	/<EMAIL>/Pekao_Bank/VNotifications/TextRes_5TextVar
#. SourceLocation:	Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(55,1, 55,46)
#: Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(55,1, 55,46)
msgctxt ",/<EMAIL>/Pekao_Bank/VNotifications/TextRes_5TextVar"
msgid "🌲"
msgstr "🌲"

#. Key:	/<EMAIL>/Pekao_Bank/VNotifications/TextFull_4TextVar
#. SourceLocation:	Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(56,1, 56,69)
#: Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(56,1, 56,69)
msgctxt ",/<EMAIL>/Pekao_Bank/VNotifications/TextFull_4TextVar"
msgid "Woofffffffd +3fffffffdfsdf"
msgstr "Woofffffffd +3fffffffffdfsdf"

#. Key:	/<EMAIL>/Pekao_Bank/VNotifications/TextRes_4TextVar
#. SourceLocation:	Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(57,1, 57,46)
#: Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(57,1, 57,46)
msgctxt ",/<EMAIL>/Pekao_Bank/VNotifications/TextRes_4TextVar"
msgid "🌲"
msgstr "🌲"

#. Key:	/<EMAIL>/Pekao_Bank/VNotifications/TextFull_3TextVar
#. SourceLocation:	Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(58,1, 58,69)
#: Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(58,1, 58,69)
msgctxt ",/<EMAIL>/Pekao_Bank/VNotifications/TextFull_3TextVar"
msgid "Woofffffffd +3fffffffdfsdf"
msgstr "Woofffffffd +3fffffffffdfsdf"

#. Key:	/<EMAIL>/Pekao_Bank/VNotifications/TextRes_3TextVar
#. SourceLocation:	Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(59,1, 59,46)
#: Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(59,1, 59,46)
msgctxt ",/<EMAIL>/Pekao_Bank/VNotifications/TextRes_3TextVar"
msgid "🌲"
msgstr "🌲"

#. Key:	/<EMAIL>/Pekao_Bank/VNotifications/TextFull_2TextVar
#. SourceLocation:	Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(60,1, 60,69)
#: Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(60,1, 60,69)
msgctxt ",/<EMAIL>/Pekao_Bank/VNotifications/TextFull_2TextVar"
msgid "Woofffffffd +3fffffffdfsdf"
msgstr "Woofffffffd +3fffffffffdfsdf"

#. Key:	/<EMAIL>/Pekao_Bank/VNotifications/TextRes_2TextVar
#. SourceLocation:	Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(61,1, 61,46)
#: Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(61,1, 61,46)
msgctxt ",/<EMAIL>/Pekao_Bank/VNotifications/TextRes_2TextVar"
msgid "🌲"
msgstr "🌲"

#. Key:	/<EMAIL>/Pekao_Bank/VNotifications/TextFull_1TextVar
#. SourceLocation:	Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(62,1, 62,69)
#: Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(62,1, 62,69)
msgctxt ",/<EMAIL>/Pekao_Bank/VNotifications/TextFull_1TextVar"
msgid "Woofffffffd +3fffffffdfsdf"
msgstr "Woofffffffd +3fffffffffdfsdf"

#. Key:	/<EMAIL>/Pekao_Bank/VNotifications/TextRes_1TextVar
#. SourceLocation:	Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(63,1, 63,46)
#: Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(63,1, 63,46)
msgctxt ",/<EMAIL>/Pekao_Bank/VNotifications/TextRes_1TextVar"
msgid "🌲"
msgstr "🌲"

#. Key:	/<EMAIL>/Pekao_Bank/VNotifications/TextFullTextVar
#. SourceLocation:	Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(64,1, 64,67)
#: Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(64,1, 64,67)
msgctxt ",/<EMAIL>/Pekao_Bank/VNotifications/TextFullTextVar"
msgid "Woofffffffd +3fffffffdfsdf"
msgstr "Woofffffffd +3fffffffffdfsdf"

#. Key:	/<EMAIL>/Pekao_Bank/VNotifications/TextResTextVar
#. SourceLocation:	Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(65,1, 65,44)
#: Pekao_Bank/Content/VNotifications/notifications_feed_player_generated.verse(65,1, 65,44)
msgctxt ",/<EMAIL>/Pekao_Bank/VNotifications/TextResTextVar"
msgid "🌲"
msgstr "🌲"

#. Key:	/<EMAIL>/Pekao_Bank/VPhone/phones/Completed
#. SourceLocation:	Pekao_Bank/Content/VPhone/phones.verse(119,5, 119,41)
#: Pekao_Bank/Content/VPhone/phones.verse(119,5, 119,41)
msgctxt ",/<EMAIL>/Pekao_Bank/VPhone/phones/Completed"
msgid "✅"
msgstr "✅"

#. Key:	/<EMAIL>/Pekao_Bank/VPhone/TextRewardTextVar
#. SourceLocation:	Pekao_Bank/Content/VPhone/ui_gen_achiev.verse(27,1, 27,47)
#: Pekao_Bank/Content/VPhone/ui_gen_achiev.verse(27,1, 27,47)
msgctxt ",/<EMAIL>/Pekao_Bank/VPhone/TextRewardTextVar"
msgid "1000"
msgstr "1000"

#. Key:	/<EMAIL>/Pekao_Bank/VPhone/TextQuestDescTextVar
#. SourceLocation:	Pekao_Bank/Content/VPhone/ui_gen_achiev.verse(28,1, 28,68)
#: Pekao_Bank/Content/VPhone/ui_gen_achiev.verse(28,1, 28,68)
msgctxt ",/<EMAIL>/Pekao_Bank/VPhone/TextQuestDescTextVar"
msgid "Odbierz swój pierwszy bank!"
msgstr "Pick up your first bank!"

#. Key:	/<EMAIL>/Pekao_Bank/VPhone/TextInvestReturnTextVar
#. SourceLocation:	Pekao_Bank/Content/VPhone/ui_gen_invest2_generated.verse(28,1, 28,78)
#: Pekao_Bank/Content/VPhone/ui_gen_invest2_generated.verse(28,1, 28,78)
msgctxt ",/<EMAIL>/Pekao_Bank/VPhone/TextInvestReturnTextVar"
msgid "Zdobądź 1500 USD z powrotem po 1 minucie"
msgstr "Get $1500 back after 1 minute"

#. Key:	/<EMAIL>/Pekao_Bank/VPhone/TextTimeTextVar
#. SourceLocation:	Pekao_Bank/Content/VPhone/ui_gen_invest2_generated.verse(30,1, 30,45)
#: Pekao_Bank/Content/VPhone/ui_gen_invest2_generated.verse(30,1, 30,45)
msgctxt ",/<EMAIL>/Pekao_Bank/VPhone/TextTimeTextVar"
msgid "1min"
msgstr "1min"

#. Key:	/<EMAIL>/Pekao_Bank/VPhone/TextCashTextVar
#. SourceLocation:	Pekao_Bank/Content/VPhone/ui_gen_invest2_generated.verse(33,1, 33,46)
#: Pekao_Bank/Content/VPhone/ui_gen_invest2_generated.verse(33,1, 33,46)
msgctxt ",/<EMAIL>/Pekao_Bank/VPhone/TextCashTextVar"
msgid "$1000"
msgstr "$1000"

#. Key:	/<EMAIL>/Pekao_Bank/VPhone/TimerText_1TextVar
#. SourceLocation:	Pekao_Bank/Content/VPhone/ui_gen_mini_phone_timer_generated.verse(13,1, 13,48)
#: Pekao_Bank/Content/VPhone/ui_gen_mini_phone_timer_generated.verse(13,1, 13,48)
msgctxt ",/<EMAIL>/Pekao_Bank/VPhone/TimerText_1TextVar"
msgid "100$"
msgstr "100$"

#. Key:	/<EMAIL>/Pekao_Bank/VPhone/TimerTextTextVar
#. SourceLocation:	Pekao_Bank/Content/VPhone/ui_gen_mini_phone_timer_generated.verse(14,1, 14,46)
#: Pekao_Bank/Content/VPhone/ui_gen_mini_phone_timer_generated.verse(14,1, 14,46)
msgctxt ",/<EMAIL>/Pekao_Bank/VPhone/TimerTextTextVar"
msgid "5:99"
msgstr "5:99"

#. Key:	/<EMAIL>/Pekao_Bank/VPhone/ButtonCardTextVar
#. SourceLocation:	Pekao_Bank/Content/VPhone/ui_gen_reg2.verse(12,1, 12,48)
#: Pekao_Bank/Content/VPhone/ui_gen_reg2.verse(12,1, 12,48)
msgctxt ",/<EMAIL>/Pekao_Bank/VPhone/ButtonCardTextVar"
msgid "Zaakceptuj"
msgstr "Accept"

#. Key:	/<EMAIL>/Pekao_Bank/VPopup/popup/MsgOk
#. SourceLocation:	Pekao_Bank/Content/VPopup/popup.verse(39,2, 39,33)
#: Pekao_Bank/Content/VPopup/popup.verse(39,2, 39,33)
msgctxt ",/<EMAIL>/Pekao_Bank/VPopup/popup/MsgOk"
msgid "Okej"
msgstr "Okay"

#. Key:	/<EMAIL>/Pekao_Bank/VPopup/popup/MsgAnuluj
#. SourceLocation:	Pekao_Bank/Content/VPopup/popup.verse(41,2, 41,41)
#: Pekao_Bank/Content/VPopup/popup.verse(41,2, 41,41)
msgctxt ",/<EMAIL>/Pekao_Bank/VPopup/popup/MsgAnuluj"
msgid "Anuluj"
msgstr "cancel"

#. Key:	/<EMAIL>/Pekao_Bank/VQuestSystem/quest_system/AgentToStr
#. SourceLocation:	Pekao_Bank/Content/VQuestSystem/quest_system.verse(330,5, 330,63)
#: Pekao_Bank/Content/VQuestSystem/quest_system.verse(330,5, 330,63)
msgctxt ",/<EMAIL>/Pekao_Bank/VQuestSystem/quest_system/AgentToStr"
msgid "Tekst {Agent}"
msgstr "Text {Agent}"

#. Key:	/<EMAIL>/Pekao_Bank/VQuestSystem/TextRewardTextVar
#. SourceLocation:	Pekao_Bank/Content/VQuestSystem/quests_list_generated.verse(16,1, 16,46)
#: Pekao_Bank/Content/VQuestSystem/quests_list_generated.verse(16,1, 16,46)
msgctxt ",/<EMAIL>/Pekao_Bank/VQuestSystem/TextRewardTextVar"
msgid "100"
msgstr "100"

#. Key:	/<EMAIL>/Pekao_Bank/VQuestSystem/TextQuestDescTextVar
#. SourceLocation:	Pekao_Bank/Content/VQuestSystem/quests_list_generated.verse(17,1, 17,68)
#: Pekao_Bank/Content/VQuestSystem/quests_list_generated.verse(17,1, 17,68)
msgctxt ",/<EMAIL>/Pekao_Bank/VQuestSystem/TextQuestDescTextVar"
msgid "Odbierz swój pierwszy bank!"
msgstr "Pick up your first bank!"

#. Key:	/<EMAIL>/Pekao_Bank/VRaceMinigame/PointsValueTextVar
#. SourceLocation:	Pekao_Bank/Content/VRaceMinigame/race_points_canvas_generated.verse(11,1, 11,47)
#: Pekao_Bank/Content/VRaceMinigame/race_points_canvas_generated.verse(11,1, 11,47)
msgctxt ",/<EMAIL>/Pekao_Bank/VRaceMinigame/PointsValueTextVar"
msgid "100"
msgstr "100"

#. Key:	/<EMAIL>/Pekao_Bank/VRaceMinigame/UEFN_TextBlock_C_0TextVar
#. SourceLocation:	Pekao_Bank/Content/VRaceMinigame/race_points_canvas_generated.verse(12,1, 12,57)
#: Pekao_Bank/Content/VRaceMinigame/race_points_canvas_generated.verse(12,1, 12,57)
msgctxt ",/<EMAIL>/Pekao_Bank/VRaceMinigame/UEFN_TextBlock_C_0TextVar"
msgid "POINTS"
msgstr "PUNTI"

#. Key:	/<EMAIL>/Pekao_Bank/VRaceMinigame/RaceTipTrackType
#. SourceLocation:	Pekao_Bank/Content/VRaceMinigame/race_track.verse(36,1, 36,139)
#: Pekao_Bank/Content/VRaceMinigame/race_track.verse(36,1, 36,139)
msgctxt ",/<EMAIL>/Pekao_Bank/VRaceMinigame/RaceTipTrackType"
msgid "Samochód - rodzi samochód, WalkCheckPoints - używa punktów kontrolnych, Walk - brak punktów kontrolnych i wymagany jest WinVolume"
msgstr "Car - gives birth to a car, WalkCheckPoints - uses checkpoints, Walk - no checkpoints and WinVolume is required"

#. Key:	/<EMAIL>/Pekao_Bank/VRaceMinigame/RaceTipStartChannel
#. SourceLocation:	Pekao_Bank/Content/VRaceMinigame/race_track.verse(37,1, 37,111)
#: Pekao_Bank/Content/VRaceMinigame/race_track.verse(37,1, 37,111)
msgctxt ",/<EMAIL>/Pekao_Bank/VRaceMinigame/RaceTipStartChannel"
msgid "Wymagane. Urządzenia zewnętrzne mogą używać tego kanału do rozpoczęcia wyścigu."
msgstr "Required. External devices can use this channel to start the race."

#. Key:	/<EMAIL>/Pekao_Bank/VRaceMinigame/RaceTipMStartTeleport
#. SourceLocation:	Pekao_Bank/Content/VRaceMinigame/race_track.verse(38,1, 38,102)
#: Pekao_Bank/Content/VRaceMinigame/race_track.verse(38,1, 38,102)
msgctxt ",/<EMAIL>/Pekao_Bank/VRaceMinigame/RaceTipMStartTeleport"
msgid "Opcjonalnie. Przenosi gracza do tego teleportera na starcie wyścigu."
msgstr "Optional. Moves the player to this teleporter at the start of the race."

#. Key:	/<EMAIL>/Pekao_Bank/VRaceMinigame/RaceTipMExitVolume
#. SourceLocation:	Pekao_Bank/Content/VRaceMinigame/race_track.verse(39,1, 39,114)
#: Pekao_Bank/Content/VRaceMinigame/race_track.verse(39,1, 39,114)
msgctxt ",/<EMAIL>/Pekao_Bank/VRaceMinigame/RaceTipMExitVolume"
msgid "Opcjonalnie. Zakończy (JAKO ANULUJ) wyścig, gdy gracz wejdzie do tego woluminu."
msgstr "Optional. It will end (AS CANCEL) the race when the player enters this volume."

#. Key:	/<EMAIL>/Pekao_Bank/VRaceMinigame/RaceTipMExitTeleport
#. SourceLocation:	Pekao_Bank/Content/VRaceMinigame/race_track.verse(40,1, 40,117)
#: Pekao_Bank/Content/VRaceMinigame/race_track.verse(40,1, 40,117)
msgctxt ",/<EMAIL>/Pekao_Bank/VRaceMinigame/RaceTipMExitTeleport"
msgid "Opcjonalnie. Teleportuje gracza do tego teleportera po zakończeniu wyścigu."
msgstr "Optional. Teleports the player to this teleporter at the end of the race."

#. Key:	/<EMAIL>/Pekao_Bank/VRaceMinigame/RaceTipMWinVolume
#. SourceLocation:	Pekao_Bank/Content/VRaceMinigame/race_track.verse(41,1, 41,199)
#: Pekao_Bank/Content/VRaceMinigame/race_track.verse(41,1, 41,199)
msgctxt ",/<EMAIL>/Pekao_Bank/VRaceMinigame/RaceTipMWinVolume"
msgid "Opcjonalnie. Zakończy (AS WIN) wyścig, gdy gracz wejdzie w ten wolumen. Może być używany zamiast punktów kontrolnych (ostatni punkt kontrolny również automatycznie kończy wyścig)"
msgstr "Optional. It will end (AS WIN) the race when the player enters this volume. Can be used instead of checkpoints (the last checkpoint also automatically ends the race)"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/buttons_to_granters/NotOwnedMessage
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/buttons_to_granters.verse(79,2, 79,66)
#: Pekao_Bank/Content/VResourcesSystem/buttons_to_granters.verse(79,2, 79,66)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/buttons_to_granters/NotOwnedMessage"
msgid "Nie jesteś właścicielem tego domu!"
msgstr "You don't own this house!"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/conditional_button_for_gold/StoppedMakingMoneyMessage
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/conditional_button_for_gold.verse(48,5, 48,126)
#: Pekao_Bank/Content/VResourcesSystem/conditional_button_for_gold.verse(48,5, 48,126)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/conditional_button_for_gold/StoppedMakingMoneyMessage"
msgid "{Name} zatrzymał się -${AmountPerSec}/s"
msgstr "{Name} stopped -${AmountPerSec}/s"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/conditional_button_for_gold/StoppedMakingMoneyNoNameMessage
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/conditional_button_for_gold.verse(50,5, 50,121)
#: Pekao_Bank/Content/VResourcesSystem/conditional_button_for_gold.verse(50,5, 50,121)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/conditional_button_for_gold/StoppedMakingMoneyNoNameMessage"
msgid "Maszyna zatrzymała się -${AmountPerSec}/s"
msgstr "The machine stopped -${AmountPerSec}/s"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/gold_crit_amount_upgrader_devic/GainUpgradedMsg
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/gold_crit_amount_upgrader_devic.verse(32,2, 32,138)
#: Pekao_Bank/Content/VResourcesSystem/gold_crit_amount_upgrader_devic.verse(32,2, 32,138)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/gold_crit_amount_upgrader_devic/GainUpgradedMsg"
msgid "Kwota krytyki pieniężnej podwyższona do poziomu: {Level}\n{Gain}Kwota krytu:%"
msgstr "Amount of monetary criticism increased to: {Level}\n{Gain}Coverage Amount:%"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/gold_crit_upgrader_devic/GainUpgradedMsg
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/gold_crit_upgrader_devic.verse(33,2, 33,139)
#: Pekao_Bank/Content/VResourcesSystem/gold_crit_upgrader_devic.verse(33,2, 33,139)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/gold_crit_upgrader_devic/GainUpgradedMsg"
msgid "Szansa na krytykę pieniężną podwyższona do poziomu: {Level}\n {Gain}Szansa krytyczna:%"
msgstr "Chance for monetary criticism increased to: {Level}\n {Gain}Critical Chance:%"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/gold_gain_upgrader_devic/GainUpgradedMsg
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/gold_gain_upgrader_devic.verse(33,2, 33,136)
#: Pekao_Bank/Content/VResourcesSystem/gold_gain_upgrader_devic.verse(33,2, 33,136)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/gold_gain_upgrader_devic/GainUpgradedMsg"
msgid "Zysk pieniężny podwyższony do poziomu: {Level}\n Zysk: {GoldGain}💸"
msgstr "Cash profit increased to: {Level}\n Profit: {GoldGain}💸"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/PlayerNameTextVar
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(42,1, 42,55)
#: Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(42,1, 42,55)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/PlayerNameTextVar"
msgid "WWWWWWWWWWWWWW"
msgstr "WWWWWWWWWWWWWW"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/TokensTextVar
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(43,1, 43,41)
#: Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(43,1, 43,41)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/TokensTextVar"
msgid "10"
msgstr "10"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/CurGoldTextVar
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(44,1, 44,44)
#: Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(44,1, 44,44)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/CurGoldTextVar"
msgid "9999"
msgstr "9999"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/TextPointsTextVar
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(45,1, 45,55)
#: Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(45,1, 45,55)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/TextPointsTextVar"
msgid "Points: 1000"
msgstr "Pontos: 1000"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/RebirthBonusTextVar
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(46,1, 46,64)
#: Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(46,1, 46,64)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/RebirthBonusTextVar"
msgid "Premia za odrodzenie 1.25%"
msgstr "Rebirth bonus 1.25%"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/Image1_4TextVar
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(47,1, 47,45)
#: Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(47,1, 47,45)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/Image1_4TextVar"
msgid "🍀"
msgstr "🍀"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/TextTimeDailyTextVar
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(48,1, 48,51)
#: Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(48,1, 48,51)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/TextTimeDailyTextVar"
msgid "22min"
msgstr "22min"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/PetBonusTextVar
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(49,1, 49,44)
#: Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(49,1, 49,44)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/PetBonusTextVar"
msgid "+5%"
msgstr "+5%"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/Image1_3TextVar
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(50,1, 50,45)
#: Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(50,1, 50,45)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/Image1_3TextVar"
msgid "🐶"
msgstr "🐶"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/CurWoodTextVar
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(51,1, 51,44)
#: Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(51,1, 51,44)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/CurWoodTextVar"
msgid "9999"
msgstr "9999"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/Image1_2TextVar
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(52,1, 52,45)
#: Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(52,1, 52,45)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/Image1_2TextVar"
msgid "🌲"
msgstr "🌲"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/GoldPerSecTextVar
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(53,1, 53,49)
#: Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(53,1, 53,49)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/GoldPerSecTextVar"
msgid "9999/s"
msgstr "9999/s"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/Image1_1TextVar
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(54,1, 54,45)
#: Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(54,1, 54,45)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/Image1_1TextVar"
msgid "💸"
msgstr "💸"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/Image1TextVar
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(55,1, 55,43)
#: Pekao_Bank/Content/VResourcesSystem/resource_manager_ui_generated.verse(55,1, 55,43)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/Image1TextVar"
msgid "💸"
msgstr "💸"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/resources_manager/AgentName
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/resources_manager.verse(149,5, 149,58)
#: Pekao_Bank/Content/VResourcesSystem/resources_manager.verse(149,5, 149,58)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/resources_manager/AgentName"
msgid "{Agent}"
msgstr "{Agent}"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/resources_manager/GiveGoldMessage
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/resources_manager.verse(490,5, 490,86)
#: Pekao_Bank/Content/VResourcesSystem/resources_manager.verse(490,5, 490,86)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/resources_manager/GiveGoldMessage"
msgid "{Msg}: +{Total}"
msgstr "{Msg}: +{Total}"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/TimerTextTextVar
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/save_system_timer_ui_generated.verse(12,1, 12,46)
#: Pekao_Bank/Content/VResourcesSystem/save_system_timer_ui_generated.verse(12,1, 12,46)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/TimerTextTextVar"
msgid "5:55"
msgstr "5:55"

#. Key:	/<EMAIL>/Pekao_Bank/VResourcesSystem/stat_upgrader_devic/GainUpgradedMsg
#. SourceLocation:	Pekao_Bank/Content/VResourcesSystem/stat_upgrader_devic.verse(48,2, 48,126)
#: Pekao_Bank/Content/VResourcesSystem/stat_upgrader_devic.verse(48,2, 48,126)
msgctxt ",/<EMAIL>/Pekao_Bank/VResourcesSystem/stat_upgrader_devic/GainUpgradedMsg"
msgid "Zysk pieniężny podwyższony do poziomu: {Level}\n Zysk: {GoldGain}💸"
msgstr "Cash profit increased to: {Level}\n Profit: {GoldGain}💸"

#. Key:	/<EMAIL>/Pekao_Bank/VTimeRewards/Timer6TextVar
#. SourceLocation:	Pekao_Bank/Content/VTimeRewards/ui_gen_time_rewards_claim.verse(22,1, 22,44)
#: Pekao_Bank/Content/VTimeRewards/ui_gen_time_rewards_claim.verse(22,1, 22,44)
msgctxt ",/<EMAIL>/Pekao_Bank/VTimeRewards/Timer6TextVar"
msgid "10.99"
msgstr "10.99"

#. Key:	/<EMAIL>/Pekao_Bank/VTimeRewards/Timer5TextVar
#. SourceLocation:	Pekao_Bank/Content/VTimeRewards/ui_gen_time_rewards_claim.verse(23,1, 23,44)
#: Pekao_Bank/Content/VTimeRewards/ui_gen_time_rewards_claim.verse(23,1, 23,44)
msgctxt ",/<EMAIL>/Pekao_Bank/VTimeRewards/Timer5TextVar"
msgid "10.99"
msgstr "10.99"

#. Key:	/<EMAIL>/Pekao_Bank/VTimeRewards/Timer4TextVar
#. SourceLocation:	Pekao_Bank/Content/VTimeRewards/ui_gen_time_rewards_claim.verse(24,1, 24,44)
#: Pekao_Bank/Content/VTimeRewards/ui_gen_time_rewards_claim.verse(24,1, 24,44)
msgctxt ",/<EMAIL>/Pekao_Bank/VTimeRewards/Timer4TextVar"
msgid "10.99"
msgstr "10.99"

#. Key:	/<EMAIL>/Pekao_Bank/VTimeRewards/Timer3TextVar
#. SourceLocation:	Pekao_Bank/Content/VTimeRewards/ui_gen_time_rewards_claim.verse(25,1, 25,44)
#: Pekao_Bank/Content/VTimeRewards/ui_gen_time_rewards_claim.verse(25,1, 25,44)
msgctxt ",/<EMAIL>/Pekao_Bank/VTimeRewards/Timer3TextVar"
msgid "10.99"
msgstr "10.99"

#. Key:	/<EMAIL>/Pekao_Bank/VTimeRewards/Timer2TextVar
#. SourceLocation:	Pekao_Bank/Content/VTimeRewards/ui_gen_time_rewards_claim.verse(26,1, 26,44)
#: Pekao_Bank/Content/VTimeRewards/ui_gen_time_rewards_claim.verse(26,1, 26,44)
msgctxt ",/<EMAIL>/Pekao_Bank/VTimeRewards/Timer2TextVar"
msgid "10.99"
msgstr "10.99"

#. Key:	/<EMAIL>/Pekao_Bank/VTimeRewards/Timer1TextVar
#. SourceLocation:	Pekao_Bank/Content/VTimeRewards/ui_gen_time_rewards_claim.verse(27,1, 27,44)
#: Pekao_Bank/Content/VTimeRewards/ui_gen_time_rewards_claim.verse(27,1, 27,44)
msgctxt ",/<EMAIL>/Pekao_Bank/VTimeRewards/Timer1TextVar"
msgid "10.99"
msgstr "10.99"

#. Key:	/<EMAIL>/Pekao_Bank/VTimeRewards/TimerTextTextVar
#. SourceLocation:	Pekao_Bank/Content/VTimeRewards/ui_gen_time_rewards_timer.verse(12,1, 12,46)
#: Pekao_Bank/Content/VTimeRewards/ui_gen_time_rewards_timer.verse(12,1, 12,46)
msgctxt ",/<EMAIL>/Pekao_Bank/VTimeRewards/TimerTextTextVar"
msgid "5:99"
msgstr "5:99"

