using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
using. VResourcesSystem

loan_character := class(auto_creative_device, i_init_async):

    @editable Character:?character_device = false
    @editable Conversation:?conversation_device = false
    @editable ConversationCreditRunning:?conversation_device = false
    @editable CharacterMarkToDestroy:?creative_prop = false

    CreditGivenEv:event(agent) = event(agent){}

    var Resources:?resources_manager = false

    var CreditRunning: logic=false
    
    InitAsync<override>(Container:vcontainer)<suspends>:void=
        set Resources = Container.ResolveOp[resources_manager] or Err() 
        race:
            # CreditGivenEv.Await()
            loop:
                Agent := Character.G().InteractedWithEvent.Await()
                if(CreditRunning?):
                    ConversationCreditRunning.G().InitiateConversation(Agent)
                else:
                    Conversation.G().InitiateConversation(Agent)
            loop:
                ConvEv :tuple(agent, int)= Conversation.G().OnConversationEvent.Await()
                
                if(ConvEv(1) = 1):
                    spawn. HandleCredit(ConvEv(0))
        # CharacterMarkToDestroy.G().Dispose()

    HandleCredit(Agent:agent)<suspends>:void=
        set CreditRunning = true
        Resources.G().GiveGold(Agent, 10000, ?ShowNotification := true) 
        CreditGivenEv.Signal(Agent)
        Sleep(60.0)
        Resources.G().DecreaseGoldPerSec(Agent, 25)
        TotalSeconds := 10000.0/25.0
        Sleep(TotalSeconds)
        Resources.G().IncreaseGoldPerSec(Agent, 25)
        set CreditRunning = false

        
