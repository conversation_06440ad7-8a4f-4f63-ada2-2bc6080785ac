using. Pool

#gen
#list_t1
#int
#id-107214ae-bbe8-40d0-9095-90b9a2ffdb74
list_int<public> := class:
	var<private> Length<public>:int = 0

	var Arr:[]int = array{}
	var<private> ArrLength:int = 0

	GetAll<public>()<transacts>:[]int=
		Arr

	Get<public>(I:int)<decides><transacts>:int=
		Arr[I]
		
	Add<public>(Obj:int)<transacts>:void=
		if(ArrLength > Length):
			if(set Arr[Length] = Obj):
				set Length += 1
			else:
				LError()
		else:
			set Arr += array. Obj
			set ArrLength += 1
			set Length += 1

	Clear<public>()<transacts>:void=
		set Arr = array{}
		set ArrLength = 0
		set Length = 0
		

	# RemoveElement<public>(I:int)<transacts>:void=
	# 	if(I < Count):
	# 		for(X := I..Count-2
	# 			Val := Arr[X+1]
	# 		):
	# 			if. set Arr[X] = Val
	# 		set Count -= 1
	# 	else:
	# 		LError()
		
list_int_c<public>(Arr:[]int):void=
	list_int:
		Arr := Arr
		ArrLength := Arr.Length
		Length := Arr.Length
#id-107214ae-bbe8-40d0-9095-90b9a2ffdb74

#gen
#list_t1
#pooled_editable_cinematic_sequence_device
#id-c8aadf49-bfea-45ef-931c-40191ee43dde
list_pooled_editable_cinematic_sequence_device<public> := class:
	var<private> Length<public>:int = 0

	var Arr:[]pooled_editable_cinematic_sequence_device = array{}
	var<private> ArrLength:int = 0

	GetAll<public>()<transacts>:[]pooled_editable_cinematic_sequence_device=
		Arr

	Get<public>(I:int)<decides><transacts>:pooled_editable_cinematic_sequence_device=
		Arr[I]
		
	Add<public>(Obj:pooled_editable_cinematic_sequence_device)<transacts>:void=
		if(ArrLength > Length):
			if(set Arr[Length] = Obj):
				set Length += 1
			else:
				LError()
		else:
			set Arr += array. Obj
			set ArrLength += 1
			set Length += 1

	Clear<public>()<transacts>:void=
		set Arr = array{}
		set ArrLength = 0
		set Length = 0
		

	# RemoveElement<public>(I:int)<transacts>:void=
	# 	if(I < Count):
	# 		for(X := I..Count-2
	# 			Val := Arr[X+1]
	# 		):
	# 			if. set Arr[X] = Val
	# 		set Count -= 1
	# 	else:
	# 		LError()
		
list_pooled_editable_cinematic_sequence_device_c<public>(Arr:[]pooled_editable_cinematic_sequence_device):void=
	list_pooled_editable_cinematic_sequence_device:
		Arr := Arr
		ArrLength := Arr.Length
		Length := Arr.Length
#id-c8aadf49-bfea-45ef-931c-40191ee43dde

#gen
#list_t1
#i_disposable
#id-f4c73a3b-31db-4dd6-b556-db6626bce774
list_i_disposable<public> := class:
	var<private> Length<public>:int = 0

	var Arr:[]i_disposable = array{}
	var<private> ArrLength:int = 0

	GetAll<public>()<transacts>:[]i_disposable=
		Arr

	Get<public>(I:int)<decides><transacts>:i_disposable=
		Arr[I]
		
	Add<public>(Obj:i_disposable)<transacts>:void=
		if(ArrLength > Length):
			if(set Arr[Length] = Obj):
				set Length += 1
			else:
				LError()
		else:
			set Arr += array. Obj
			set ArrLength += 1
			set Length += 1

	Clear<public>()<transacts>:void=
		set Arr = array{}
		set ArrLength = 0
		set Length = 0
		

	# RemoveElement<public>(I:int)<transacts>:void=
	# 	if(I < Count):
	# 		for(X := I..Count-2
	# 			Val := Arr[X+1]
	# 		):
	# 			if. set Arr[X] = Val
	# 		set Count -= 1
	# 	else:
	# 		LError()
		
list_i_disposable_c<public>(Arr:[]i_disposable):void=
	list_i_disposable:
		Arr := Arr
		ArrLength := Arr.Length
		Length := Arr.Length
#id-f4c73a3b-31db-4dd6-b556-db6626bce774

#gen
#list_t1
#char
#id-629f57dd-e56e-4ddf-99e7-31e1ba809468
list_char<public> := class:
	var<private> Length<public>:int = 0

	var Arr:[]char = array{}
	var<private> ArrLength:int = 0

	GetAll<public>()<transacts>:[]char=
		Arr

	Get<public>(I:int)<decides><transacts>:char=
		Arr[I]
		
	Add<public>(Obj:char)<transacts>:void=
		if(ArrLength > Length):
			if(set Arr[Length] = Obj):
				set Length += 1
			else:
				LError()
		else:
			set Arr += array. Obj
			set ArrLength += 1
			set Length += 1

	Clear<public>()<transacts>:void=
		set Arr = array{}
		set ArrLength = 0
		set Length = 0
		

	# RemoveElement<public>(I:int)<transacts>:void=
	# 	if(I < Count):
	# 		for(X := I..Count-2
	# 			Val := Arr[X+1]
	# 		):
	# 			if. set Arr[X] = Val
	# 		set Count -= 1
	# 	else:
	# 		LError()
		
list_char_c<public>(Arr:[]char):void=
	list_char:
		Arr := Arr
		ArrLength := Arr.Length
		Length := Arr.Length
#id-629f57dd-e56e-4ddf-99e7-31e1ba809468