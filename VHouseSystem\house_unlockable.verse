using {/Verse.org/Simulation}
using {/Verse.org/Simulation/Tags}
using {/Verse.org/Assets}
using {/Verse.org/Verse}
using {/Verse.org/Random}
using {/Verse.org/Colors}
using {/Verse.org/Colors/NamedColors}
using {/Verse.org/Native}
using {/Verse.org/Concurrency}
using {/UnrealEngine.com/Temporary}
using {/UnrealEngine.com/Temporary/UI}
using {/UnrealEngine.com/Temporary/SpatialMath}
using {/UnrealEngine.com/Temporary/Diagnostics}
using {/UnrealEngine.com/Temporary/Curves}
using {/Fortnite.com/UI}
using {/Fortnite.com/Devices}
using {/Fortnite.com/Devices/CreativeAnimation}
using {/Fortnite.com/Devices/CreativeAnimation/InterpolationTypes}
using {/Fortnite.com/Vehicles}
using {/Fortnite.com/Teams}
using {/Fortnite.com/Playspaces}
using {/Fortnite.com/Game}
using {/Fortnite.com/FortPlayerUtilities}
using {/Fortnite.com/Characters}
using {/Fortnite.com/AI}
using { VGiga }
using { VGiga.Pool }
using{VResourcesSystem }
using { VLocalization }
using {VArrowSystem}
using. VNotifications
using. VCustomBillboard

house_unlockable := class<concrete>():
	@editable Id:string = ""
	@editable PropForTrigger:creative_prop = creative_prop{}

	# @editable PropToShow:creative_prop = creative_prop{}

	# @editable ShowAfterUnlocking:[]string  = array{}
	# @editable UnlockablesToShowAfterUnlocking:[]house_unlockable = array{}

	Init(Presets:unlockable_ids_options, CustomBillboardSpawner:custom_billboard_spawner, ResourceManager:resources_manager, TriggersPool:trigger_device_pool, ResetEvent:event(), Loc:i_localization, ArrowSystem:arrow_system,
	Notifs:notifications_system):house_unlockable_manager=
		
		Preset := if(UnlockableOptions := Presets.Get[Id]):
			UnlockableOptions
		else:
			LPrint("ERROR REQUESTED UNLOCK ID: '{Id}', NOT FOUND")
			unlockable_id_options{}

		CanShowArrow := if(Preset.AdditionalData.Find[unlockable_additional_data.ShowUpdatePopup] or not Preset.ShowArrow?):
			false
		else:
			true

		UnManager := house_unlockable_manager:
			D := Self
			UnlockableOptions := Preset
			CustomBillboardSpawner := CustomBillboardSpawner
			ResourceManager  := ResourceManager
			TriggersPool := TriggersPool
			ResetEvent := ResetEvent
			Loc := Loc
			ArrowSystem := ArrowSystem
			Notifs := Notifs
			CanShowArrow := CanShowArrow
		.Init()

		UnManager

CompareUnlocksManagersByGold(UnlockA:house_unlockable_manager, UnlockB:house_unlockable_manager)<transacts>:int=
	A := UnlockA.UnlockableOptions.GoldCost
	B := UnlockB.UnlockableOptions.GoldCost
	if(A < B). -1
	else if(A > B). 1
	else 0
#gen
#minbypriv
#house_unlockable_manager
#id-aa692b49-7026-4f66-81b5-3261fc19a86d

(Input:[]house_unlockable_manager).MinBy(Comparer: type{_(:house_unlockable_manager, :house_unlockable_manager)<transacts> : int})<transacts><decides> : house_unlockable_manager = 
	var Smallest :house_unlockable_manager= Input[0]
	for(X := 1..Input.Length
		Next := Input[X]
		Comparer(Smallest, Next) > 0
	):
		set Smallest = Next

	Smallest
#id-aa692b49-7026-4f66-81b5-3261fc19a86d

#gen
#con
#id-85730dd7-5f2d-4a46-9252-82de7597ed7a
unlocked_event_data_c(PAgent:agent,
	PUnlockable:house_unlockable_manager,
	PWasLoaded:logic
)<transacts>:unlocked_event_data=
	unlocked_event_data:
		Agent := PAgent
		Unlockable := PUnlockable
		WasLoaded := PWasLoaded
#id-85730dd7-5f2d-4a46-9252-82de7597ed7a
unlocked_event_data := struct:
	Agent<public>:agent
	Unlockable<public>:house_unlockable_manager
	WasLoaded<public>:logic
	
house_unlockable_manager := class<unique>():
	D:house_unlockable
	Loc:i_localization
	UnlockableOptions:unlockable_id_options
	ArrowSystem:arrow_system
	var InitTr:transform = transform{}
	# var PropToShowInitTr:transform = transform{}

	var MOwner:?agent = false
	var IsCoop:logic = false
	CustomBillboardSpawner:custom_billboard_spawner
	TriggersPool :trigger_device_pool
	ResourceManager:resources_manager
	var IsShowingArrowToTrigger:logic = false
	Notifs:notifications_system

	FakeTriggerBuyAutoBuy:event(?agent) = event(?agent){}
	ResetEvent:event()
	UnlockedEvent:event(unlocked_event_data) = event(unlocked_event_data){} # sends what to unlock
	LoadBuyRequestedEvent:event() = event(){}
	CanShowArrow:logic

	FakeAutoBuy():void=
		if(MOwner?):
			FakeTriggerBuyAutoBuy.Signal(MOwner)
		else:
			LError()

	Init():house_unlockable_manager=
		# set PropToShowInitTr = D.PropToShow.GetTransform()
		set InitTr = D.PropForTrigger.GetTransform()
		# D.PropForTrigger.Hide()
		HideProp()
		Self

	HideArrowEvent:event() = event(){}

	HideArrowToTrigger():void=
		HideArrowEvent.Signal()

	ShowArrowToTrigger():void=
		spawn. ShowArrowToTriggerAsync()

	ShowArrowToTriggerAsync()<suspends>:void=
		if(Owner := MOwner?):
			set IsShowingArrowToTrigger = true
			var MArrow:?i_disposable =false
			var MBeacon:?i_disposable =false
			race:
				block:
					# set MArrow = ArrowSystem.ShowArrow(Owner, InitTr.Translation)
					set MBeacon = ArrowSystem.ShowBeacon(Owner, InitTr.Translation + 
					vector3:
						Z := 91.0
					)
					Sleep(Inf)
				ResetEvent.Await()
				UnlockedEvent.Await()
				HideArrowEvent.Await()
				
			set IsShowingArrowToTrigger = false
			if(Arrow := MArrow?):
				Arrow.Dispose()
				
			if(Beacon := MBeacon?):
				Beacon.Dispose()

	RegisterOwner(MAgent:?agent, VIsCoop:logic):void=
		set MOwner = MAgent
		set IsCoop = VIsCoop

	HideProp():void=
		if. D.PropForTrigger.TeleportTo[InitTr.WithTranslationZ(-2500.0)]
	

	GetLocalized(Text:string):string=
		if(Owner := MOwner?):
			Loc.G(Owner, Text)
		else:
			Text
		
	Vector3OneLessZ :vector3= vector3:
		X := 1.0
		Y := 0.34 #width
		Z := 0.34

	ShowTrigger(HighlightPropAsset:creative_prop_asset, HighlightPropAssetFans:creative_prop_asset)<suspends>:void=
		# LPrintStack()

		if(
			Trigger := TriggersPool.Rent[]
			Agent := MOwner?
		):
			LPrint("Showing unlock id: {D.Id}, InitPos: {InitTr.Translation}")
			
			# if. Billboard.TeleportTo[InitTr]
			if. Trigger.TeleportTo[InitTr]
			# Billboard.SetTextSize(20)
			Text := if (UnlockableOptions.AdditionalData.Find[unlockable_additional_data.ShowUpdatePopup]):
# 				Loc.G(Agent, "Find Rebirth\nMore Updates Soon!")
				Loc.G(Agent, "Bank Completed\nMore Updates Soon!")
			else if(UnlockableOptions.GoldCost > 0):
				# "{GetLocalized("Buy")} {GetLocalized(UnlockableOptions.Name)}\n<green>${UnlockableOptions.GoldCost}"
				if(UnlockableOptions.Name = ""):
					"{GetLocalized(D.Id)}\n<green>${UnlockableOptions.GoldCost}"
					# "{GetLocalized("Upgrade")}\n<green>${UnlockableOptions.GoldCost}"
				else:
					# "{GetLocalized(D.Id)}\n<green>${UnlockableOptions.GoldCost}"
					"{GetLocalized(UnlockableOptions.Name)}\n<green>${UnlockableOptions.GoldCost}"
			else:
				# "{GetLocalized("Buy")} {GetLocalized(UnlockableOptions.Name)}\n{GetLocalized("Fans")} {UnlockableOptions.FansCost}"
				"{GetLocalized(UnlockableOptions.Name)}\n{GetLocalized("Fans")} {UnlockableOptions.FansCost}"
			# Billboard.SetText(Text.ToMessage())
			Billboard := CustomBillboardSpawner.Spawn(Text, InitTr.Translation + vector3{Z:=150.0}, Vector3OneLessZ)
			
			AssetForHighlight := if(UnlockableOptions.GoldCost > 0):
				HighlightPropAsset
			else:
				HighlightPropAssetFans
			
			SpawnedHighlightResult := SpawnProp(AssetForHighlight, InitTr)
			MSpawnedHighlight := SpawnedHighlightResult(0)
			# PrintSpawnPropResult(SpawnedHighlightResult(1))

			spawn. WaitForTriggerBuy(Trigger, Billboard, MSpawnedHighlight)
		else:
			LError()

# 	EndGameMessage1<localizes>():message = "Find rebirth button!"

	WaitForTriggerBuy(Trigger:trigger_device, Billboard:custom_billboard, MSpawnedHighlight:?creative_prop)<suspends>:void=
		var WasUnlocked:logic = false
		var WasLoaded:logic = false
		race:
			ResetEvent.Await()
			block:
				LoadBuyRequestedEvent.Await()
				set WasUnlocked = true
				set WasLoaded = true
			loop:
				MAgent := race:
					Trigger.TriggeredEvent.Await()
					FakeTriggerBuyAutoBuy.Await()
				# Print("OnBuyTrigger")
				if(Owner := MOwner?,
					Agent := MAgent?,
					(Owner = Agent) or IsCoop?
				):
					if (UnlockableOptions.AdditionalData.Find[unlockable_additional_data.ShowUpdatePopup]):
						Notifs.ShowTopNotification(Agent, ToMsg(Loc.G(Agent, "Bank completed, congrats!")))
# 						Notifs.ShowTopNotification(Agent, "Follow 'Kamyker' on X|Twitter and join our Discord: NpZjcQHYeU".ToMessage()
					else if(UnlockableOptions.GoldCost > 0):
						WasGoldSpent := ResourceManager.TryToTakeGold(Agent, UnlockableOptions.GoldCost)
						if(WasGoldSpent?):
							set WasUnlocked = true
							break
					else if(UnlockableOptions.FansCost > 0):
						HaveFans := ResourceManager.CheckIfHaveFans(Agent, UnlockableOptions.FansCost)
						if(HaveFans?):
							set WasUnlocked = true
							break

# 		Print("unlocked1")
		if(WasUnlocked?):
			spawn. ShowProp()
# 			Print("unlocked2")
			if(Owner := MOwner?):
# 				Print("unlocked3")
				UnlockedEvent.Signal(unlocked_event_data_c(Owner, Self, WasLoaded))

		if(SpawnedHighlight := MSpawnedHighlight?):
			SpawnedHighlight.Dispose()
		else:
			LError()
		
		Billboard.Dispose()
		# if. Billboard.TeleportTo[vector3{Z := -999.0}, rotation{}]
		# BillboardsPool.Return(Billboard)
		if. Trigger.TeleportTo[vector3{Z := -999.0}, rotation{}]
		TriggersPool.Return(Trigger)
	
	ShowProp()<suspends>:void=
		InitScale := InitTr.Scale
		InitTrScaled := InitTr.WithScale(Vector3(InitScale.X, InitScale.Y, 0.01))
		if. D.PropForTrigger.TeleportTo[InitTrScaled]

		if:
			AnimCtrl := D.PropForTrigger.GetAnimationController[]
		then:
			Deltas:[]keyframe_delta = array{keyframe_delta:
				DeltaLocation := vector3{}
				DeltaRotation := rotation{}
				DeltaScale := Vector3(1.0, 1.0, InitTr.Scale.Z / 0.01)
				Interpolation := cubic_bezier_parameters_c(0.15,0.64,0.55,0.92) #InterpolationTypes.EaseOut
				Time := 1.0
			}
			AnimCtrl.SetAnimation(Deltas, ?Mode := animation_mode.OneShot)
			AnimCtrl.Play()