using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 
using. VNotifications


buttons_to_granters_tag := class(tag_comparable):
	GetComparable<override>()<computes>:comparable=
		"buttons_to_granters"
	
buttons_to_granters_c<public>(D:creative_device, Area:area_interface, AllowOnlyOwnerToUse:logic, Container:player_events_manager_devic, ?ActionBeforeGrant:?(agent->void) = false):buttons_to_granters=
	Notifs := Container.ResolveErr(notifications_system)
	ItemGranters := D.GetDevicesInAreaWithTag(item_granter_device, Area, buttons_to_granters_tag{})
	Buttons := D.GetDevicesInAreaWithTag(button_device, Area, buttons_to_granters_tag{})

	Instance := buttons_to_granters:
		ItemGranters := ItemGranters
		Buttons := Buttons
		AllowOnlyOwnerToUse := AllowOnlyOwnerToUse
		ActionBeforeGrant := ActionBeforeGrant
		Notifs := Notifs
	.Init()
	Instance

buttons_to_granters<public> := class<internal>:
	ItemGranters<public>:[]item_granter_device
	Buttons<public>:[]button_device
	Notifs:notifications_system

	AllowOnlyOwnerToUse<public>:logic
	ActionBeforeGrant<public>:?(agent->void)

	var MOwnerAgent :?agent= false

	SetOwner<public>(Agent:?agent):void=
		set MOwnerAgent = Agent

	Init():buttons_to_granters=
		for(Button:Buttons
			ButtonPos := Button.GetTransform().Translation
		):
			if(var ClosestGranter :item_granter_device= ItemGranters[0]
				var ClosestDistance :float= DistanceSquared(ClosestGranter.GetTransform().Translation, ButtonPos)
			):
				for(Granter:ItemGranters
					DistanceToGranter :float= DistanceSquared(Granter.GetTransform().Translation, ButtonPos)
					DistanceToGranter < ClosestDistance
				):
					set ClosestGranter = Granter
					set ClosestDistance = DistanceToGranter
				
				spawn. AwaitButtonClick(Button, ClosestGranter)
			else. LError()
		Self
	
	NotOwnedMessage<localizes>:message = "You don't own this house!"
	
	AwaitButtonClick(Button:button_device, Granter:item_granter_device)<suspends>:void=
		loop:

			Agent := Button.InteractedWithEvent.Await()
			
			if(Action := ActionBeforeGrant?):
				Action(Agent)

			if(not AllowOnlyOwnerToUse?):
				Granter.GrantItem(Agent)
			else if(Agent = MOwnerAgent?):
				Granter.GrantItem(Agent)
			else:
				Notifs.ShowTopNotification(Agent, NotOwnedMessage)


		