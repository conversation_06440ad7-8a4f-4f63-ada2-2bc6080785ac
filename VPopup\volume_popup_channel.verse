using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga 

volume_popup_channel := class(auto_creative_device, i_init):
	@editable Volume:?volume_device = false
	@editable ChannelOnAccept:?channel_device = false
	@editable MainText:string = ""
	
	var Popup :?popup= false
	var Loc:i_localization = empty_i_localization{}

	Init<override>(Container:vcontainer):void=
		set Popup = Container.ResolveOp[popup] or Err()
		set Loc = Container.Resolve_i_localization()
		spawn. OnVolume()

	OnVolume()<suspends>:void=
		loop:
			Agent := Volume.G().AgentEntersEvent.Await()
			spawn. OnVolumeEnterAgent(Agent)

	OnVolumeEnterAgent(Agent:agent)<suspends>:void=
		Result := Popup.G().ShowOkCancel(Agent, Loc.G(Agent, MainText))
		if(Result?):
			ChannelOnAccept.G().Transmit(option. Agent)
		