using. /Verse.org/Simulation
using. /Verse.org/Simulation/Tags
using. /Verse.org/Assets
using. /Verse.org/Verse
using. /Verse.org/Random
using. /Verse.org/Colors
using. /Verse.org/Colors/NamedColors
using. /Verse.org/Native
using. /Verse.org/Concurrency
using. /UnrealEngine.com/Temporary
using. /UnrealEngine.com/Temporary/UI
using. /UnrealEngine.com/Temporary/SpatialMath
using. /UnrealEngine.com/Temporary/Diagnostics
using. /UnrealEngine.com/Temporary/Curves
using. /Fortnite.com/UI
using. /Fortnite.com/Devices
using. /Fortnite.com/Devices/CreativeAnimation
using. /Fortnite.com/Devices/CreativeAnimation/InterpolationTypes
using. /Fortnite.com/Vehicles
using. /Fortnite.com/Teams
using. /Fortnite.com/Playspaces
using. /Fortnite.com/Game
using. /Fortnite.com/FortPlayerUtilities
using. /Fortnite.com/Characters
using. /Fortnite.com/AI
using. VGiga
using. VGiga.Pool
using. VGoldGranter
using. VNotifications
using. VPropSpawner
using. VPrefabs
using. VCoop


resources_manager<public> := class(auto_creative_device, i_init, i_init_per_player_async):
    # @editable GoldHudMessage : hud_message_device = hud_message_device{}
    # @editable FansHudMessage : hud_message_device = hud_message_device{}
    # @editable GoldHudMessageTop : hud_message_device = hud_message_device{}
    # @editable FansHudMessageTop : hud_message_device = hud_message_device{}
    @editable RedHudMessageTop<public> : hud_message_device = hud_message_device{}

    @editable RebirthRemoveWeapons:item_remover_device = item_remover_device{}
    @editable ButtonBoost:button_device = button_device{}
    @editable AccoladePoints:accolades_device = accolades_device{}
    # @editable GoldCoinAsset:creative_prop_asset = DefaultCreativePropAsset

    @editable MP_Rebirth:?analytics_device = false

    var Loc:i_localization = empty_i_localization{}
    # GoldGranter<public>:gold_granter
    # WoodGranter<public>:wood_granter
    var SaveSystem:?save_system = false
    var NotificationsFeed:?notifications_feed_devic = false
    var Notifications :?notifications_system= false
    var PropSpawner:?prop_spawner = false
    var Container:?player_events_manager_devic = false
    var Coop :?coop= false
    var MoneyAsGold:?money_as_gold_devic = false
    var MarketingInvestment:?marketing_investment_device = false

    var B:resources_manager_balance := resources_manager_balance{}
    var PrefabsBalance :?gm_balance= false
    RebirthEvent<public>:event(agent) = event(agent){}

    Has1mlnGoldEv<public>:event(agent) = event(agent){}
    var PlayerDataMap : map_agent_res_p_data = map_agent_res_p_data{}

    # 	PlayerCurrentGoldEvent<public>:event(tuple(agent,int)) = event(tuple(agent,int)){}
    # 	PlayerTotalGoldEarnedChangedEvent<public>:event(tuple(agent,int)) = event(tuple(agent,int)){}

    TickRate : float = 1.0

    Init<override>(C:vcontainer):void=
        set MoneyAsGold = C.ResolveOp[money_as_gold_devic] or Err()
        set Coop = C.ResolveOp[coop] or Err()
        set Notifications = C.ResolveOp[notifications_system] or Err()
        set	Loc = C.Resolve_i_localization()
        set	SaveSystem = option. C.ResolveErr(save_system)
        set	B = C.ResolveErr(resources_manager_balance)
        set	NotificationsFeed = option. C.ResolveErr(notifications_feed_devic)
        set	PropSpawner = option. C.ResolveErr(prop_spawner)
        set	Container = option. C.ResolveErr(player_events_manager_devic)
        set PrefabsBalance = C.ResolveErrOp(gm_balance)
        set MarketingInvestment = C.ResolveErrOp(marketing_investment_device)
        spawn. RunResources()
        Loc.GetLanguageChangedEvent().Subscribe1(OnLanguageChangedEvent)
        spawn. OnButtonBoost()

    OnButtonBoost()<suspends>:void=
        loop:
            Ag := ButtonBoost.InteractedWithEvent.Await()
            if(Data := PlayerDataMap.Get[Ag]):
                set Data.BoostMulti = 3.0
                NotificationsFeed.G().Show(Ag, GoldTexture, "Gold x2 :)".ToMessage())



    StartToDropGoldOnPropDestroy<public>(PropsManager:props_manager):void=
        if(not PropsManager.Disposed?):
            spawn. StartToDropGoldOnPropDestroyAsync(PropsManager)

    StartToDropGoldOnPropDestroyAsync<private>(PropsManager:props_manager)<suspends>:void=
        race:
            PropsManager.DisposedEv.Await()
            loop:
                PropDestroyedEv := PropsManager.GetPropDestroyedEv()
                PropUnique := PropDestroyedEv.Await()
                DestroyPos := PropUnique.Transform.Translation
                SpawnGoldAtPosition(DestroyPos + vector3{Z := 30.0})

    OnLanguageChangedEvent(Agent:agent):void=
        if(Data := PlayerDataMap.Get[Agent]):
            UpdateUI(Data)

    HideUi<public>(Agent:agent):void=
        if(Data := PlayerDataMap.Get[Agent]
            Data.UiVisible?
            Widgets := Data.Widgets
        ):
            Agent.RemoveFromPlayerUi(Widgets.ResourcesManagerUi)
            set Data.UiVisible = false
            if(Coop.G().IsCoop[]):
                for(NonAdmin:Coop.G().GetNonAdminPlayers()):
                    NonAdmin.RemoveFromPlayerUi(Widgets.ResourcesManagerUi)

    ShowUi<public>(Agent:agent, WithNameAndAvatar:logic):void=
        LPrint("ShowUi maybe")
        if(Data := PlayerDataMap.Get[Agent]
            Widgets := Data.Widgets
        ):
            LPrint("ShowUi")
            if(WithNameAndAvatar?):
                Widgets.PlayerName.Show()
                LPrint("show name")

                #avatar in phone
            else if(not WithNameAndAvatar?, not Data.UiVisible?):
                Widgets.PlayerName.Hide()
                LPrint("hide name")
                #avatar in phone

            if(not Data.UiVisible?):
                Agent.AddToPlayerUi(Widgets.ResourcesManagerUi, false, 10)
                if(Coop.G().IsCoop[]):
                    for(NonAdmin:Coop.G().GetNonAdminPlayers()):
                        NonAdmin.AddToPlayerUi(Widgets.ResourcesManagerUi, false, 10)
                set Data.UiVisible = true
        else:
            LPrint("ShowUi false")

    AgentName<localizes>(Agent:agent):message = "{Agent}"

    InitPlayerAsync<override>(Agent:agent, PlayerRemoved:event())<suspends>:void=
        Print("resources InitPlayerAsync"); Sleep(0.0)

        if(Coop.G().CoopAdmin? <> Agent):
            # if(Player := player[Agent]
            #     Save := SaveSystem.G().GetPlayerSave[CoopAdmin]
            #     RebirthLevel := SaveSystem.G().GetPlayerSave[CoopAdmin].Rebirths
            # ):
                # Print("PlayerDataMap.GetOrAwait(CoopAdmin)")
                # AdminData := PlayerDataMap.GetOrAwait(CoopAdmin)
                # Widgets := make_resources_manager_ui_generated()
                # AdminData.MWidgetsSyncer.G().Add(Agent, Widgets.ResourcesManagerUi)

                # Print("resources 1"); Sleep(0.0)
                # Sleep(0.0)
                # # if(B.UiShowName?):
                # #     Widgets.PlayerName.SetText(AgentName(Agent))
                # # else:
                # #     Widgets.PlayerName.SetText("".ToMessage())

                # # Widgets.TextPoints.Collapse()
                # # if(not B.UiShowGoldPerSecond?):
                # #     Widgets.OverlayGoldPerSec.Collapse()
                # # if(not B.UiShowPets?):
                # #     Widgets.OverlayPets.Collapse()
                # # if(not B.UiShowWood?):
                # #     Widgets.OverlayWood.Collapse()
                # # if(not B.UiShowRebirths?):
                # #     Widgets.RebirthBonus.Collapse()
                # # if(not B.UiShowTokens?):
                # #     Widgets.OverlayTokens.Collapse()
                # # else:
                # #     Widgets.Tokens.SetText("0".ToMessage())
                # # if(B.HideOnlyTokensOverlay?):
                # #     Widgets.OverlayTokens.Collapse()
                # # if(B.HideOnlyGoldOverlay?):
                # #     Widgets.OverlayGold.Collapse()

                # # Widgets.OverlayTimeDaily.Collapse()

                # # UiVisible := true
                # # NewPlayerData := res_p_data_c(Agent,
                # #     Widgets, Save, UiVisible, PrefabsBalance.G().GainRatio
                # # )
                # # set BoostMulti = PrefabsBalance.GainRatio
                # # Print("Agent.AddToPlayerUi(Widgets.ResourcesManagerUi)")
                # Agent.AddToPlayerUi(Widgets.ResourcesManagerUi)
                # # PlayerDataMap.Set(Agent, NewPlayerData)
                # UpdateUI(AdminData)

                # # GoldChangedEv := Save.Gold.ChangedEvent

                # # race:
                # PlayerRemoved.Await()
                #     # loop: # for 1 mln cash quest
                #     #     NewGold := GoldChangedEv.Await()

                #     #     if(NewGold >= 1000000):
                #     #         Has1mlnGoldEv.Signal(Agent)
                #     #         Sleep(Inf)

                # Agent.RemoveFromPlayerUi(AdminData.Widgets.ResourcesManagerUi)

                # # PlayerDataMap.Remove(Agent)
            # else:
            #     LError()

        else:
            if(not SaveSystem.G().GetPlayerSave[player[Agent]]):
                LPrint("save failed")
            if(not SaveSystem.G().GetPlayerSave[player[Agent]].Rebirths):
                LPrint("save failed Rebirths")


            if(Player := player[Agent]
                Save := SaveSystem.G().GetPlayerSave[Player]
                RebirthLevel := SaveSystem.G().GetPlayerSave[Agent].Rebirths
            ):
                Print("resources 1"); Sleep(0.0)
                Widgets := make_resources_manager_ui_generated()

                if(B.MoneyAsGold?):
                    MoneyAsGold.G().AddGold(Player, Save.Gold.Get())


                if(B.UiShowName?):
                    Widgets.PlayerName.SetText(AgentName(Agent))
                else:
                    Widgets.PlayerName.SetText("".ToMessage())

                Widgets.TextPoints.Collapse()
                if(not B.UiShowGoldPerSecond?):
                    Widgets.OverlayGoldPerSec.Collapse()
                if(not B.UiShowPets?):
                    Widgets.OverlayPets.Collapse()
                if(not B.UiShowWood?):
                    Widgets.OverlayWood.Collapse()
                if(not B.UiShowRebirths?):
                    Widgets.RebirthBonus.Collapse()
                if(not B.UiShowTokens?):
                    Widgets.OverlayTokens.Collapse()
                else:
                    Widgets.Tokens.SetText("0".ToMessage())
                if(B.HideOnlyTokensOverlay?):
                    Widgets.OverlayTokens.Collapse()
                if(B.HideOnlyGoldOverlay?):
                    Widgets.OverlayGold.Collapse()

                Widgets.OverlayTimeDaily.Collapse()

                UiVisible := true
                NewPlayerData := res_p_data_c(Agent,
                    Widgets, Save, UiVisible, PrefabsBalance.G().GainRatio)
                # set BoostMulti = PrefabsBalance.GainRatio
                PlayerDataMap.Set(Agent, NewPlayerData)
                Sleep(0.0)
                Print("add to main player ui")
                if(Coop.G().IsAdmin[Agent]):
                    Coop.G().AddWidgetToSyncAsAdmin(Widgets.ResourcesManagerUi, widget_syncer_id.resources)
                Agent.AddToPlayerUi(Widgets.ResourcesManagerUi)

                UpdateUI(NewPlayerData)

                GoldChangedEv := Save.Gold.ChangedEvent

                race:
                    PlayerRemoved.Await()
                    loop: # for 1 mln cash quest
                        NewGold := GoldChangedEv.Await()

                        if(NewGold >= 1000000):
                            Has1mlnGoldEv.Signal(Agent)
                            Sleep(Inf)
                    # block:
                    # 	if(not B.MoneyAsGold?):
                    # 		Sleep(Inf)
                    # 	var GoldLastCheck :int= 0
                    # 	loop:
                    # 		Sleep(2.0)
                    # 		if(Agent.GetFortCharacterActive[]):
                    # 			CurrentGold := D.ButtonForGoldCheck.GetItemCount(Player, 0)
                    # 			if(CurrentGold <> GoldLastCheck):
                    # 				# if(CurrentGold > Val.GoldLastCheck):
                    # 				# 	set Val.TotalGoldEarned += CurrentGold - Val.GoldLastCheck
                    # 				# 	PlayerTotalGoldEarnedChangedEvent.Signal((Player, Val.TotalGoldEarned))
                    # 				set GoldLastCheck = CurrentGold
                    # 				NewPlayerData.SetGold(GoldLastCheck)
                    # 				UpdateUI(NewPlayerData)
                    # 				# PlayerCurrentGoldEvent.Signal((Player, CurrentGold))
                    block:
                        # if(not B.EnableWoodResource?):
                            Sleep(Inf)
                        # var LastCheck :int= 0
                        # loop:
                        # 	Sleep(5.0)
                        # 	if(Agent.GetFortCharacterActive[]):
                        # 		var CurValue :int= D.TakeWoodConDevice.GetItemCount(Player, 0)
                        # 		if(CurValue <> LastCheck):
                        # 			if(CurValue > LastCheck
                        # 				LastCheck > 0 # this is bugfix when starting game
                        # 				NewPlayerData.PetsTotalLevel > 0
                        # 				Dif := CurValue - LastCheck
                        # 				BonusWood := Ceil[Dif * (NewPlayerData.PetsTotalLevel / 100.0)]
                        # 				BonusWood > 0
                        # 			):
                        # 				set CurValue += BonusWood
                        # 				GiveWood(Agent, BonusWood, Dif)

                        # 			set LastCheck = CurValue
                        # 			NewPlayerData.SetWood(LastCheck)
                        # 			UpdateUI(NewPlayerData)


                if(PlayerData := PlayerDataMap.Get[Agent]):
                    Agent.RemoveFromPlayerUi(Widgets.ResourcesManagerUi)

                PlayerDataMap.Remove(Agent)
            else:
                LError()

    GetPlayerDataMap<public>(Agent:agent)<decides><transacts>:res_p_data=
        PlayerDataMap.Get[Agent]

    GetPlayerDataMapOrAwait<public>(Agent:agent)<suspends>:res_p_data=
        PlayerDataMap.GetOrAwait(Agent)

    GetGoldGain(Agent:agent):int=
        Level := SaveSystem.G().GetIntStat(Agent, int_stat_id.GoldGain)
        (Level + 1) * 100

    GetGoldGainNextLevel(Agent:agent):int=
        Level := SaveSystem.G().GetIntStat(Agent, int_stat_id.GoldGain)
        (Level + 2) * 100

    GetCritChancePct(Agent:agent):int=
        Level := SaveSystem.G().GetIntStat(Agent, int_stat_id.GoldCrit)
        (Level + 1) * 2

    GetCritChancePctNextLevel(Agent:agent):int=
        Level := SaveSystem.G().GetIntStat(Agent, int_stat_id.GoldCrit)
        (Level + 2) * 2

    GetCritAmountPct(Agent:agent):int=
        Level := SaveSystem.G().GetIntStat(Agent, int_stat_id.GoldCritAmount)
        (Level + 1) * 5

    GetCritAmountPctNextLevel(Agent:agent):int=
        Level := SaveSystem.G().GetIntStat(Agent, int_stat_id.GoldCritAmount)
        (Level + 2) * 5

    ResetDataIncreaseMultiplier<public>(Agent:agent, RemoveWeapons:logic):void=
        if(Data := PlayerDataMap.Get[Agent]):
            for(Obj:Container.G().Container.ResolveAll[i_on_rebirth]):
                Obj.OnRebirth(Agent)
            SaveSystem.G().OnRebirth(Agent)
            if(P_Rebirth := MP_Rebirth?):
                P_Rebirth.Submit(Agent)
            else:
                LPrint("P_Rebirth analytics missing")

            if(RemoveWeapons?):
                RebirthRemoveWeapons.Remove(Agent)
            if(B.MoneyAsGold?):
            else:
                Data.SetGold(0)

            set Data.CurFans = 0
            Data.GoldPerSec.Set(0)
            set Data.FansPerSec = 0
            Data.SetBonusMultiplier(0.0)
            # Data.IncreaseRebirthLevel()
            RebirthEvent.Signal(Agent)
            UpdateUI(Data)

    GiveWood<public>(Agent:agent, Value:int):void=
        if(Data := PlayerDataMap.Get[Agent]
            Player := player[Agent]
            Bonus := Ceil[Value * (Data.PetsTotalLevel / 100.0)]
        ):
            GainedPet := Ceil[Bonus * Data.CachedFinalMultiplier * Data.BoostMulti] or 0
            Gained := Ceil[Value * Data.CachedFinalMultiplier * Data.BoostMulti] or 0

            SaveSystem.G().AddWood(Player, Gained + GainedPet)

            # D.GoldHudMessageTop.Show(Agent, "+{Gained + GainedPet}🌲".ToMessage())
            NotificationsFeed.G().Show(Agent, "🌲".ToMessage(), "Wood +{Gained + GainedPet}".ToMessage())
            UpdateUI(Data)

    GiveGoldNoText(Agent:agent, Value:int):int=
        AdminOrAgen := AdminOrAgent(Agent)

        WithBonusValue := CalculateGold(AdminOrAgen, Value)
        if(Player := player[AdminOrAgen]
            Data := PlayerDataMap.Get[AdminOrAgen]
            WithBonusValue > 0
        ):
            if(B.MoneyAsGold?):
                MoneyAsGold.G().AddGold(Player, WithBonusValue)
                NewGold := MoneyAsGold.G().GetGold(Player)
                SaveSystem.G().SetGold(Player, NewGold) #this is here to mainly invoke changed event
            else:
                SaveSystem.G().AddGold(Player, WithBonusValue)

            UpdateUI(Data)
            return WithBonusValue
        0

    CalculateGold<public>(Agent:agent, Value:int):int=
        if( Value > 0
            Data := PlayerDataMap.Get[AdminOrAgent(Agent)]
            Bonus := Ceil[Value * (Data.PetsTotalLevel / 100.0)]
        ):
            GainedPet := Ceil[Bonus * Data.CachedFinalMultiplier] or 0
            Gained := Ceil[Value * Data.CachedFinalMultiplier] or 0

            return Gained + GainedPet
        0

    GoldTexture :texture= VResourcesSystem.Assets.TTextures.T_GoldCoin

    GetRebirths<public>(Agent:agent):int=
        SaveSystem.G().GetRebirths(AdminOrAgent(Agent))

    GivePoints<public>(Agent:agent, Value:float, ?OnlyShowPopup:logic=false):void=
        if(ValueInt := Floor[Value]):
            GivePoints(Agent, ValueInt, ?OnlyShowPopup := OnlyShowPopup)

    GivePoints<public>(Agent:agent, Value:int, ?OnlyShowPopup:logic=false):void=
        if(Player := player[AdminOrAgent(Agent)]
            Value > 0
        ):
            if(OnlyShowPopup?):
                # NotificationsFeed.G().Show(Agent, GoldTexture, "Gold +{Value.ToShortNumberString()}".ToMessage())
                Notifications.G().ShowBigTopNotification(Agent, "+{Value.ToShortNumberString()}".ToMessage(), 3.0)
            else:
                GiveGold(Agent, Value, ?ShowNotification := false)
                Notifications.G().ShowBigTopNotification(Agent, "+{Value.ToShortNumberString()}".ToMessage(), 3.0)


        # old give points, now cash above
        # if(Player := player[AdminOrAgent(Agent)]
        #     Data := PlayerDataMap.Get[AdminOrAgent(Agent)]
        #     Value > 0
        # ):
        #     FinalValue := Value + SaveSystem.G().GetRebirths(Agent)
        #     Notifications.G().ShowBigTopNotification(Agent, "+{FinalValue.ToShortNumberString()}".ToMessage(), 3.0)

        #     if(not OnlyShowPopup?):
        #         AccoladePoints.Award(Agent)
        #         SaveSystem.G().AddPoints(Player, FinalValue)
        #         UpdateUI(Data, ?UpdateCode:=true)
        #         # NotificationsFeed.G().Show(Agent, VResourcesSystem.Assets.TTextures.T_Token, "+{Value.ToShortNumberString()}".ToMessage())
        #         # NotificationsFeed.G().Show(Agent, GoldTexture, "Points +{Value.ToShortNumberString()}".ToMessage())

    GiveGold<public>(Agent:agent, Value:int, ?ShowNotification:logic = false):int=
        Total := GiveGoldNoText(Agent, Value)
        if(Total > 0):
            if(ShowNotification?):
                NotificationsFeed.G().Show(Agent, GoldTexture, "Gold +{Total.ToShortNumberString()}".ToMessage())
            Total
            # D.GoldHudMessageTop.Show(Agent, "+{Total} 💸{Loc.G(Agent, "Money")}".ToMessage())
        else:
            0

    GiveGoldCrit<public>(Agent:agent, Value:int):void=
        Total := GiveGoldNoText(Agent, Value)
        if(Total > 0):
            NotificationsFeed.G().Show(Agent, "🤑".ToMessage(), "Crit! +{Total.ToShortNumberString()}".ToMessage(), ?Color := NamedColors.Orange)

    GiveGoldText<public>(Agent:agent, Value:int, Text:string, ?ShowNotification:logic = false):int=
        Total := GiveGoldNoText(Agent, Value)
        if(Total > 0):
            if(ShowNotification?):
                NotificationsFeed.G().Show(Agent, GoldTexture, "{Loc.G(Agent, Text)}: +{Total.ToShortNumberString()}".ToMessage())
            Total
        else:
            0
            # D.GoldHudMessageTop.Show(Agent, "{Loc.G(Agent, Text)}: +{Total} 💸".ToMessage())

    GiveGoldMessage<localizes>(Msg:message, Total:string):message = "{Msg}: +{Total}"

    GiveGoldMsg<public>(Agent:agent, Value:int, Text:message, ?ShowNotification:logic = false):int=
        Total := GiveGoldNoText(Agent, Value)
        if(Total > 0):
            if(ShowNotification?):
                NotificationsFeed.G().Show(Agent, GoldTexture, GiveGoldMessage(Text,Total.ToShortNumberString()))
            Total
        else:
            0

    CalculateGoldWithMultiplier<public>(Agent:agent, Time:float, Multiplier:float):int=
        if(Data := PlayerDataMap.Get[AdminOrAgent(Agent)]):
            GoldGained := Ceil[Data.GoldPerSec.Get() * Time * Multiplier] or 0
            CalculateGold(Agent, GoldGained)
        else:
            0
    CalculateGoldWithMultiplierMinSec<public>(Agent:agent, Time:float, Multiplier:float, MinGoldPerSec:int):int=
        if(Data := PlayerDataMap.Get[AdminOrAgent(Agent)]):
            GoldPerSec := Max(Data.GoldPerSec.Get(), MinGoldPerSec)
            GoldGained := Ceil[GoldPerSec * Time * Multiplier] or 0
            CalculateGold(Agent, GoldGained)
        else:
            0
    AdminOrAgent(Agent:agent)<transacts>:agent=
        Coop.G().AdminOrAgent(Agent)
    GiveGoldWithMultiplier<public>(Agent:agent, Time:float, Multiplier:float, Text:string, ?ShowNotification:logic = false):int=
        if(Data := PlayerDataMap.Get[AdminOrAgent(Agent)]):
            GoldGained := Ceil[Data.GoldPerSec.Get() * Time * Multiplier] or 0
            GiveGoldText(Agent, GoldGained, Text, ?ShowNotification:= ShowNotification)
        else:
            0
    GiveGoldWithMultiplier<public>(Agent:agent, Time:float, Multiplier:float, ?ShowNotification:logic = false):int=
        if(Data := PlayerDataMap.Get[AdminOrAgent(Agent)]):
            GoldGained := Ceil[Data.GoldPerSec.Get() * Time * Multiplier] or 0
            GiveGold(Agent, GoldGained, ?ShowNotification := ShowNotification)
        else:
            0
    GiveGoldWithMultiplier<public>(Agent:agent, Reward:tuple(float,float), Text:string, ?ShowNotification:logic = false):int=
        Time := Reward(0)
        Multiplier := Reward(1)
        GiveGoldWithMultiplier(Agent, Time, Multiplier, Text, ?ShowNotification := ShowNotification)

    GiveGoldWithMultiplier<public>(Agent:agent, Reward:tuple(float,float), ?ShowNotification:logic = false):int=
        Time := Reward(0)
        Multiplier := Reward(1)
        GiveGoldWithMultiplier(Agent, Time, Multiplier, ?ShowNotification := ShowNotification)

    GiveFansWithMultiplier<public>(Agent:agent, Time:float, Multiplier:float):void=
        if(Data := PlayerDataMap.Get[AdminOrAgent(Agent)]):
            Gained := if(N := Round[Data.FansPerSec * Time * Data.CachedFinalMultiplier * Multiplier * Data.BoostMulti]). N else. 0
            set Data.CurFans += Gained
            NotificationsFeed.G().Show(Agent, "👨‍👩‍👦".ToMessage(), "{Loc.G(Agent, "Fans")}: +{Gained.ToShortNumberString()}".ToMessage())
            # FansHudMessageTop.Show(Agent, "{Loc.G(Agent, "Marketing")}: +{Gained} 👨‍👩‍👦{Loc.G(Agent, "Fans")}".ToMessage())
            UpdateUI(Data)


    SetImageCardBg<public>(Agent:agent, Texture:texture):void=
        if(Data := PlayerDataMap.Get[AdminOrAgent(Agent)]):
            Print("SetImageCardBg")
            Data.Widgets.ImageCardBg.SetImage(Texture)
            Data.Widgets.ImageCardBg.Hide()
            Data.Widgets.ImageCardBg.Show()
            # Data.Widgets.Tokens.SetText(Data.Widgets.Tokens.GetText().ToMessage()) #force refresh

    SpawnGoldAtPosition <public>(Pos:vector3):void={}
        # spawn. SpawnGoldAtPositionAsync(Pos)

    # SpawnGoldAtPositionAsync<private>(Pos:vector3)<suspends>:void=
    # 	MCoin := PropSpawner.G().Spawn(GoldCoinAsset, Pos, Rotation0)
    # 	#TODO per player in area optimizatin
    # 	if(Coin := MCoin?):
    # 		loop:
    # 			Sleep(0.4)
    # 			if(Coin.IsValid[]):
    # 				CoinPos := Coin.Prop.GetTransform().Translation
    # 				for(P->Player:Container.G().RegisteredPlayers
    # 					Char := P.GetFortCharacterActive[]
    # 				):
    # 					Dist := Distance(Char.GetTransform().Translation, CoinPos)
    # 					if(Dist < 100.0):
    # 						GoldAmount := GetGoldGain(P)
    # 						CritChancePct := GetCritChancePct(P) * 1.0

    # 						if(GetRandomFloat(0.0, 100.0) <= CritChancePct):
    # 							CritAmountMultiplier := 1.0 + GetCritAmountPct(P) * 0.01
    # 							if(ToGive := (GoldAmount * CritAmountMultiplier).ToInt[]):
    # 								GiveGold(P, ToGive)
    # 						else:
    # 							GiveGold(P, GoldAmount)
    # 						Coin.Dispose()
    # 						break
    # 			else:
    # 				LError()
    # 	# if(GoldSpawner := GoldSpawnerDevicePool.Rent[]):
    # 	# 	if. GoldSpawner.TeleportTo[Pos, Rotation0]
    # 	# 	Sleep(1.0)
    # 	# 	GoldSpawner.SpawnItem()
    # 	# 	GoldSpawnerDevicePool.Return(GoldSpawner)



    IncreaseGoldPerSec<public>(Agent:agent, Amount:int):void=
        if(Amount <> 0):
            if(Data := PlayerDataMap.Get[AdminOrAgent(Agent)]):
                Data.GoldPerSec.Add(Amount)
                UpdateUI(Data)
                NotificationsFeed.G().Show(Agent, GoldTexture, "+{Amount.ToShortNumberString()}/{Loc.G(Agent, "sec")}".ToMessage())
                # GoldHudMessageTop.Show(Agent, "+{Amount.ToShortNumberString()}💸/{Loc.G(Agent, "sec")}".ToMessage())

    DecreaseGoldPerSec<public>(Agent:agent, Amount:int):void=
        if(Amount <> 0):
            if(Data := PlayerDataMap.Get[AdminOrAgent(Agent)]):
                Data.GoldPerSec.Add(-Amount)
                UpdateUI(Data)
                NotificationsFeed.G().Show(Agent, GoldTexture, "-{Amount.ToShortNumberString()}/{Loc.G(Agent, "sec")}".ToMessage())

    IncreaseFansPerSec<public>(Agent:agent, Amount:int):void=
        if(B.FansEnabled?
            Data := PlayerDataMap.Get[Agent]
        ):
            set Data.FansPerSec += Amount
            UpdateUI(Data)
            NotificationsFeed.G().Show(Agent, "👨‍👩‍👦".ToMessage(), "+{Amount.ToShortNumberString()}👨‍👩‍👦/{Loc.G(Agent, "sec")}".ToMessage())
            # FansHudMessageTop.Show(Agent, "+{Amount}👨‍👩‍👦/{Loc.G(Agent, "sec")}".ToMessage())

    GetNextRebirthMultiplierPct<public>(Agent:agent):int=
        if(Data := PlayerDataMap.Get[Agent]):
            Data.GetNextRebirthMultiplierPct()
        else:
            1

    # TryToTakeResource(Agent:agent, Amount:int, CancelEvent:event(), ResourceIcon:string, ConButton:conditional_button_device)<suspends>:logic=
    # 	CurCount2 := ConButton.GetItemCount(Agent, 0)

    # 	if(CurCount2 < Amount):
    # 		D.RedHudMessageTop.Show(Agent, "{Loc.G(Agent, "Missing")} {Amount - CurCount2}{ResourceIcon}!".ToMessage())
    # 		return false

    # 	ConButton.SetItemCountRequired(0, Amount)
    # 	# TEST THIS WITH MANY PLAYERS!

    # 	Result :logic= race:
    # 		block:
    # 			CancelEvent.Await()
    # 			false
    # 		block:
    # 			ConButton.ActivatedEvent.Await()
    # 			true
    # 		block:
    # 			ConButton.NotEnoughItemsEvent.Await()
    # 			CurCount := ConButton.GetItemCount(Agent, 0)
    # 			D.RedHudMessageTop.Show(Agent, "{Loc.G(Agent, "Missing")} {Amount - CurCount}💸!".ToMessage())
    # 			false
    # 		block:
    # 			ConButton.Activate(Agent)
    # 			# timeout something is wrong
    # 			Sleep(5.0)
    # 			Print("timeout")
    # 			LError()
    # 			D.RedHudMessageTop.Show(Agent, "Error, try again!".ToMessage())
    # 			false

    # 	return Result

    TryToTakeWood<public>(Agent:agent, Amount:int, CancelEvent:event()):logic=
        # TryToTakeResource(Agent, Amount, CancelEvent, "🌲", D.TakeWoodConDevice)
        if(Data := PlayerDataMap.Get[Agent]):
            if(Data.GetWood() >= Amount):
                Data.SetWood(Data.GetWood() - Amount)
                UpdateUI(Data)
                return true
            else:
                RedHudMessageTop.Show(Agent, "{Loc.G(Agent, "Missing")} {Amount - Data.GetWood()}🌲!".ToMessage())
        return false

    TryToTakeGold<public>(Agent:agent, Amount:int)<suspends>:logic=
        if(Data := PlayerDataMap.Get[AdminOrAgent(Agent)]):
            Result := if(B.MoneyAsGold?):
                MoneyAsGold.G().TryToTakeGold(Agent, Amount)
            else:
                if(Data.GetGold() < Amount):
                    false
                else:
                    Data.SetGold(Data.GetGold() - Amount)
                    true
            if(Result?):
                if(B.MoneyAsGold?, Player := player[Agent]):
                    NewGold := MoneyAsGold.G().GetGold(Agent)
                    SaveSystem.G().SetGold(Player, NewGold) #this is here to mainly invoke changed event
                UpdateUI(Data)
                NotificationsFeed.G().Show(Agent, GoldTexture, "-{Amount.ToShortNumberString()}".ToMessage(), ?Color:=NamedColors.Red)
            else:
                RedHudMessageTop.Show(Agent, "{Loc.G(Agent, "Missing")} {Amount - GetGold(Agent)}💸!".ToMessage())
            return Result
        return false

    TryToTakeFortniteGold<public>(Agent:agent, Amount:int)<suspends>:logic=
        # if(Data := PlayerDataMap.Get[AdminOrAgent(Agent)]):
            Result := MoneyAsGold.G().TryToTakeGold(Agent, Amount)
            if(Result?):
                # NotificationsFeed.G().Show(Agent, GoldTexture, "-{Amount.ToShortNumberString()}".ToMessage(), ?Color:=NamedColors.Red)
            else:
                RedHudMessageTop.Show(Agent, "{Loc.G(Agent, "Missing")} {Amount - GetGold(Agent)}!".ToMessage())
            return Result
        # return false

    # TryToTakeGoldNotify<public>(Agent:agent, Amount:int, Text:string):logic=
    # 	if(Data := PlayerDataMap.Get[Agent]):
    # 		if(Data.GetGold() >= Amount):
    # 			Data.SetGold(Data.GetGold() - Amount)

    # 			UpdateUI(Data)
    # 			return true
    # 		else:
    # 			RedHudMessageTop.Show(Agent, "{Loc.G(Agent, "Missing")} {Amount - Data.GetGold()}💸!".ToMessage())
    # 	return false

    # TryToTakeTokens<public>(Agent:agent, Amount:int):logic=
    # 	if(Data := PlayerDataMap.Get[Agent]):
    # 		if(Data.GetTokens() >= Amount):
    # 			Data.TakeTokens(Amount)
    # 			UpdateUI(Data)
    # 			return true
    # 		else:
    # 			RedHudMessageTop.Show(Agent, "{Loc.G(Agent, "Missing")} {Amount - Data.GetTokens()}📀!".ToMessage())
    # 	return false


    ShowRedHudMessage<public>(Agent:agent, Text:string):void=
        RedHudMessageTop.Show(Agent, "{Loc.G(Agent, Text)}".ToMessage())



    GetPoints<public>(Agent:agent)<decides><transacts>:int=
        # var Arr  :[]material = array{M_FortniteBase_Parent_Inst_material{}}
        # set Arr = Arr + array. M_FortniteBase_Parent_Inst_material{}
        Data := PlayerDataMap.Get[Agent]
        Data.GetPoints()

    GetGold<public>(Agent:agent):int=
        # var Arr  :[]material = array{M_FortniteBase_Parent_Inst_material{}}
        # set Arr = Arr + array. M_FortniteBase_Parent_Inst_material{}
        if(B.MoneyAsGold?):
            MoneyAsGold.G().GetGold(Agent)
        else:
            if(Data := PlayerDataMap.Get[Agent]):
                Data.GetGold()
            else:
                LError()
                0


    CheckIfHaveFans<public>(Agent:agent, Amount:int):logic=
        if(Data := PlayerDataMap.Get[Agent]):
            if(Data.CurFans >= Amount):
                return true
            else:
                RedHudMessageTop.Show(Agent, "{Loc.G(Agent, "Missing")} {Amount - Data.CurFans}👨‍👩‍👦!".ToMessage())
        return false

    SetBonusMultiplier<public>(Agent:agent, MultiplierPct:float):void=
        if(Data := PlayerDataMap.Get[Agent]):
            Data.SetBonusMultiplier(MultiplierPct / 100.0)
            UpdateUI(Data)

    SetGainRatioMultiplier<public>(Agent:agent, BoostMulti:float):void=
        if(Data := PlayerDataMap.Get[Agent]):
            set Data.BoostMulti = BoostMulti

    # IncreaseTokens<public>(Agent:agent):void=
    # 	if(Data := PlayerDataMap.Get[Agent]):
    # 		Data.Tokens.Add(1)
    # 		UpdateUI(Data)
    # 		NotificationsFeed.G().Show(Agent, VResourcesSystem.Assets.TTextures.T_Token, "+1".ToMessage())

    RunResources()<suspends>:void=
        # MultiForDoubleTickrate := if (B.FansEnabled?):
        # 	2
        # else. 1

        loop:
            for(Player -> Data : PlayerDataMap.DataMap
                Agent := Data.Agent
            ):
                GoldGained := Ceil[Data.GoldPerSec.Get() * TickRate * Data.BoostMulti] or 0
                GiveGoldText(Agent, GoldGained, "Bank")

                # Add customer revenue if marketing investment device is available
                if(MarketingDevice := MarketingInvestment?):
                    CustomerCount := SaveSystem.G().GetIntStat(Agent, int_stat_id.CustomerCount)
                    if(CustomerCount > 0):
                        CustomerRevenue := Round[CustomerCount * 0.05 * TickRate] or 0 # $0.05 per customer per second
                        if(CustomerRevenue > 0):
                            GiveGoldText(Agent, CustomerRevenue, "Customers")

            Sleep(TickRate)

            if(B.FansEnabled?):
                for(Player -> Data : PlayerDataMap.DataMap
                    Agent := Data.Agent
                ):
                    FansGained := Round[Data.FansPerSec * TickRate * Data.CachedFinalMultiplier * Data.BoostMulti] or 0
                    if(FansGained > 0):
                        set Data.CurFans += FansGained
                        UpdateUI(Data)
                        NotificationsFeed.G().Show(Player, "👨‍👩‍👦".ToMessage(), "+{FansGained.ToShortNumberString()} {Loc.G(Agent, "Fans")}".ToMessage())

            Sleep(TickRate)

    SetPetsTotalLevel<public>(Agent:agent, Level:int):void=
        if(Data := PlayerDataMap.Get[Agent]):
            set Data.PetsTotalLevel = Level
            UpdateUI(Data)


    UpdateUI(Data:res_p_data, ?UpdateCode:logic=false):void=
        Agent := Data.Agent
        if(Player := player[Agent]):
            Widgets := Data.Widgets
            MoneyAmount := if(B.MoneyAsGold?):
                MoneyAsGold.G().GetGold(Agent)
            else:
                Data.GetGold()
            Widgets.CurGold.SetText("{MoneyAmount.ToShortNumberString()}".ToMessage())
            # Widgets.CurFans.SetText("👨‍👩‍👦{Loc.G(Agent, "Fans")}: {Data.CurFans}".ToMessage())
            Multiplier := Data.CachedFinalMultiplier - 1.0 # 0.10
            Multiplier1000 := Data.CachedFinalMultiplier * 1000.0
            # Widgets.RebirthBonus.SetText("⭐️{Loc.G(Agent, "Rebirths: {Data.RebirthLevel} Bonus:")}: {Multiplier1000.ToString(1)}".ToMessage())
            Widgets.RebirthBonus.SetText("⭐️{Loc.G(Agent, "Rebirths")}: {Data.Save.Rebirths}".ToMessage())

            GoldGain := Round[Data.GoldPerSec.Get() * Data.CachedFinalMultiplier] or 0
            # FansGain := Round[Data.FansPerSec * Data.CachedFinalMultiplier] or 0
            Widgets.GoldPerSec.SetText("{GoldGain}/{Loc.G(Agent, "s")}".ToMessage())
            # Widgets.TextPoints.SetText("Points: {Data.GetPoints()}".ToMessage())
            # Widgets.FansPerSec.SetText("👨‍👩‍👦{Loc.G(Agent, "Fans gain")}: {FansGain}/{Loc.G(Agent, "sec")}".ToMessage())

            # Points := Data.GetPoints()
            # if(UpdateCode?):
            #     Playtime := SaveSystem.G().GetPlaytime(Player)
            #         Code := HashIntWithTimeInFront(Points, 6819, Playtime)

            #     if(Loc.IsEng[Agent]):
            #         Widgets.PlayerName.SetText("Code: {Code}".ToMessage())
            #     else:
            #         Widgets.PlayerName.SetText("Kod: {Code}".ToMessage())

            Widgets.Tokens.SetText("+{Data.GoldPerSec}/s".ToMessage())
            # if(B.UiShowTokens?):
            #     Widgets.Tokens.SetText("{Points}".ToMessage())
            if(B.UiShowWood?):
                Widgets.CurWood.SetText("{Data.GetWood()}".ToMessage())
            if(B.UiShowPets?):
                Widgets.PetBonus.SetText("+{Data.PetsTotalLevel}%".ToMessage())

            if(Coop.G().IsAdmin[Agent]):
                Coop.G().SyncWidget(widget_syncer_id.resources)
            # if(Syncer := Data.MWidgetsSyncer?):
            #     Syncer.Sync()



